// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1

package types

type ClearInstanceDataRequest struct {
	Revision     int64           `json:"revision,optional"`
	InstanceInfo InstanceTagInfo `json:"instanceInfo"`
}

type ClearInstanceDataResponse struct {
	Status       string `json:"status"`
	RowsAffected uint64 `json:"rowsAffected"`
}

type DataGroupTagInfo struct {
	DataGroupName string `json:"dataGroupName"`
}

type DataResult struct {
	List     []DataRow `json:"list"`
	Page     int64     `json:"page"`
	PageSize int64     `json:"pageSize"`
	Total    uint64    `json:"total"`
}

type DataRow struct {
	TimeStamp     int64            `json:"ts"`
	InstanceInfo  InstanceTagInfo  `json:"instanceInfo"`
	DataGroupInfo DataGroupTagInfo `json:"dataGroupInfo"`
	Datas         []SingleData     `json:"datas,optional"`
}

type DeleteSelectedDataRequest struct {
	Revision int64     `json:"revision,optional"`
	List     []DataRow `json:"list"`
}

type DeleteSelectedDataResponse struct {
	Status       string `json:"status"`
	RowsAffected uint64 `json:"rowsAffected"`
}

type ExportCSVRequest struct {
	FileName     string          `json:"fileName"`
	InstanceInfo InstanceTagInfo `json:"instanceInfo"`
}

type ExportCSVResponse struct {
	FileContent string `json:"fileContent"`
	FileName    string `json:"fileName"`
}

type FetchInstanceDataRequest struct {
	DataGroupInfo DataGroupTagInfo `json:"dataGroupInfo,optional"`
	Revision      int64            `json:"revision,optional"`
	WhereInfos    []WhereInfo      `json:"whereInfos,optional"`
	DataRowLimit  uint64           `json:"dataRowLimit,optional"`
	Page          int64            `json:"page,optional"`
	PageSize      int64            `json:"pageSize,optional"`
	InstanceInfo  InstanceTagInfo  `json:"instanceInfo"`
	FieldsInfo    FieldsInfo       `json:"fieldsInfo,optional"`
}

type FetchInstanceDataResponse struct {
	List     []DataRow `json:"list"`
	Page     int64     `json:"page"`
	PageSize int64     `json:"pageSize"`
	Total    uint64    `json:"total"`
}

type FetchProducerDataRequest struct {
	TableInfo     TableInfo        `json:"tableInfo"`
	Revision      int64            `json:"revision,optional"`
	WhereInfos    []WhereInfo      `json:"whereInfos,optional"`
	DataRowLimit  uint64           `json:"dataRowLimit,optional"`
	Page          int64            `json:"page,optional"`
	PageSize      int64            `json:"pageSize,optional"`
	DataGroupInfo DataGroupTagInfo `json:"dataGroupInfo,optional"`
	InstanceInfo  InstanceTagInfo  `json:"instanceInfo,optional"`
	FieldsInfo    FieldsInfo       `json:"fieldsInfo,optional"`
}

type FetchProducerDataResponse struct {
	List     []DataRow `json:"list"`
	Page     int64     `json:"page"`
	PageSize int64     `json:"pageSize"`
	Total    uint64    `json:"total"`
}

type FieldsInfo struct {
	FieldName []string `json:"fieldName"`
}

type InstanceTagInfo struct {
	InstanceName string `json:"instanceName,optional"`
	InstanceUID  string `json:"instanceUID"`
}

type SingleData struct {
	Field string `json:"field"`
	Value string `json:"value"`
}

type TableInfo struct {
	TableUID string `json:"tableUID"`
}

type WhereInfo struct {
	ColumnName    string `json:"columnName"`
	CompareSymbol string `json:"compareSymbol"`
	CompareValue  string `json:"compareValue"`
}
