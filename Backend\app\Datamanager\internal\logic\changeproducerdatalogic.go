package logic

import (
	"context"
	"fmt"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeProducerDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewChangeProducerDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeProducerDataLogic {
	return &ChangeProducerDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ChangeProducerDataLogic) ChangeProducerData(in *datamanager.ChangeProducerDataRequest) (*datamanager.ChangeProducerDataResponse, error) {
	// todo: add your logic here and delete this line
	var err error

	// 获取RevisionDataProducer
	revisionedProducer, err := l.svcCtx.DriverManager.GetRevisionProducerByProducerUID(
		in.TableInfo.TableUid,
		in.Revision,
	)
	if err != nil {
		return nil, err
	}

	// 使用SQLWrapper的InsertData方法
	err = l.svcCtx.SQLWrapper.InsertData(revisionedProducer, in.DataRows)
	if err != nil {
		return nil, err
	}

	rowsAffected := int64(len(in.DataRows))

	return &datamanager.ChangeProducerDataResponse{
		Status: "success, rowsAffected: " + fmt.Sprintf("%d", rowsAffected),
	}, nil
}
