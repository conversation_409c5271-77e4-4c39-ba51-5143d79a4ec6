package zetcd

import (
	"context"
	"fmt"
	"sync"

	"github.com/zeromicro/go-zero/core/logx"
	clientv3 "go.etcd.io/etcd/client/v3"
)

// WatcherCallback 监听器回调函数类型
type WatcherCallback func(key, value string, eventType string, metadata map[string]interface{}) error

// SubsriberInfo 单个监听器的信息
type SubsriberInfo struct {
	Key        string                 // 监听的键
	CancelFunc context.CancelFunc     // 取消函数
	Callback   WatcherCallback        // 回调函数
	Metadata   map[string]interface{} // 额外的元数据
}

// Subscriber 支持多key监听和高级功能
type Subscriber struct {
	client      EtcdClientInterface       // 统一的etcd客户端接口
	mu          sync.RWMutex              // 读写锁保护
	subscribers map[string]*SubsriberInfo // key -> WatcherInfo
}

// NewSubscriber 创建新的监听器组(迁移自WatcherGroup)
func NewSubscriber(cli EtcdClientInterface) *Subscriber {
	return &Subscriber{
		client:      cli,
		subscribers: make(map[string]*SubsriberInfo),
	}
}

// AddSubscriber 添加监听器并直接启动etcd监听(迁移自WatcherGroup)
func (s *Subscriber) AddSubscriber(key string, callback WatcherCallback, metadata map[string]interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查是否已存在
	if _, exists := s.subscribers[key]; exists {
		return fmt.Errorf("watcher for key '%s' already exists", key)
	}

	// 启动etcd监听
	cancelFunc := s.startEtcdWatcher(key, callback, metadata)

	// 添加新的监听器
	s.subscribers[key] = &SubsriberInfo{
		Key:        key,
		CancelFunc: cancelFunc,
		Callback:   callback,
		Metadata:   metadata,
	}

	logx.Infof("[Subscriber] Added watcher for key: %s", key)
	return nil
}

// RemoveSubscriber 移除监听器(迁移自WatcherGroup)
func (s *Subscriber) RemoveSubscriber(key string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	watcherInfo, exists := s.subscribers[key]
	if !exists {
		return fmt.Errorf("watcher for key '%s' not found", key)
	}

	// 取消监听
	if watcherInfo.CancelFunc != nil {
		watcherInfo.CancelFunc()
	}

	// 从map中删除
	delete(s.subscribers, key)

	logx.Infof("[Subscriber] Removed watcher for key: %s", key)
	return nil
}

// startEtcdWatcher 启动etcd监听器(迁移自WatcherGroup)
func (s *Subscriber) startEtcdWatcher(key string, callback WatcherCallback, metadata map[string]interface{}) context.CancelFunc {
	ctx, cancel := context.WithCancel(context.Background())

	go func() {
		// 首先获取当前值
		resp, err := s.client.Get(s.client.Ctx(), key)
		if err != nil {
			logx.Errorf("[Subscriber] Failed to get initial value for key %s: %v", key, err)
		} else if len(resp.Kvs) > 0 {
			// 处理初始值
			initialMetadata := make(map[string]interface{})
			for k, v := range metadata {
				initialMetadata[k] = v
			}
			initialMetadata["etcd_version"] = resp.Kvs[0].Version
			initialMetadata["etcd_revision"] = resp.Kvs[0].ModRevision

			err := callback(key, string(resp.Kvs[0].Value), "PUT", initialMetadata)
			if err != nil {
				logx.Errorf("[Subscriber] Failed to handle initial data for key %s: %v", key, err)
			} else {
				logx.Infof("[Subscriber] Successfully processed initial data for key %s", key)
			}
		}

		// 监听键的变化
		watchChan := s.client.Watch(ctx, key)
		for {
			select {
			case <-ctx.Done():
				logx.Infof("[Subscriber] Watcher for key %s stopped", key)
				return
			case watchResponse := <-watchChan:
				for _, event := range watchResponse.Events {
					eventKey := string(event.Kv.Key)
					eventValue := string(event.Kv.Value)
					eventType := "PUT"
					if event.Type == clientv3.EventTypeDelete {
						eventType = "DELETE"
					}

					// 创建包含etcd版本信息的metadata副本
					eventMetadata := make(map[string]interface{})
					for k, v := range metadata {
						eventMetadata[k] = v
					}
					eventMetadata["etcd_version"] = event.Kv.Version
					eventMetadata["etcd_revision"] = event.Kv.ModRevision

					// 执行回调函数
					err := callback(eventKey, eventValue, eventType, eventMetadata)
					if err != nil {
						logx.Errorf("[Subscriber] Failed to handle event for key %s: %v", eventKey, err)
					} else {
						logx.Infof("[Subscriber] Successfully processed event for key %s", eventKey)
					}
				}
			}
		}
	}()

	return cancel
}
