// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/udp"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Udp is the model entity for the Udp schema.
type Udp struct {
	config `json:"-"`
	// ID of the ent.
	// Frame ID
	ID string `json:"id,omitempty"`
	// Foreign key to Device
	DeviceID string `json:"device_id,omitempty"`
	// Frame Type
	Type string `json:"type,omitempty"`
	// Frame Header
	Header string `json:"header,omitempty"`
	// Frame Type ID
	TypeID string `json:"type_id,omitempty"`
	// Description of the Udp entity
	Description string `json:"description,omitempty"`
	// Name of the Udp entity
	Name string `json:"name,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// Update timestamp
	UpdateUnix time.Time `json:"update_unix,omitempty"`
	// <PERSON><PERSON> holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UdpQuery when eager-loading is set.
	Edges        UdpEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UdpEdges holds the relations/edges for other nodes in the graph.
type UdpEdges struct {
	// Device holds the value of the device edge.
	Device *Device `json:"device,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// DeviceOrErr returns the Device value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UdpEdges) DeviceOrErr() (*Device, error) {
	if e.Device != nil {
		return e.Device, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: device.Label}
	}
	return nil, &NotLoadedError{edge: "device"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Udp) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case udp.FieldID, udp.FieldDeviceID, udp.FieldType, udp.FieldHeader, udp.FieldTypeID, udp.FieldDescription, udp.FieldName:
			values[i] = new(sql.NullString)
		case udp.FieldCreateUnix, udp.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Udp fields.
func (u *Udp) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case udp.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				u.ID = value.String
			}
		case udp.FieldDeviceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_id", values[i])
			} else if value.Valid {
				u.DeviceID = value.String
			}
		case udp.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				u.Type = value.String
			}
		case udp.FieldHeader:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field header", values[i])
			} else if value.Valid {
				u.Header = value.String
			}
		case udp.FieldTypeID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type_id", values[i])
			} else if value.Valid {
				u.TypeID = value.String
			}
		case udp.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				u.Description = value.String
			}
		case udp.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				u.Name = value.String
			}
		case udp.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				u.CreateUnix = value.Time
			}
		case udp.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				u.UpdateUnix = value.Time
			}
		default:
			u.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Udp.
// This includes values selected through modifiers, order, etc.
func (u *Udp) Value(name string) (ent.Value, error) {
	return u.selectValues.Get(name)
}

// QueryDevice queries the "device" edge of the Udp entity.
func (u *Udp) QueryDevice() *DeviceQuery {
	return NewUDPClient(u.config).QueryDevice(u)
}

// Update returns a builder for updating this Udp.
// Note that you need to call Udp.Unwrap() before calling this method if this Udp
// was returned from a transaction, and the transaction was committed or rolled back.
func (u *Udp) Update() *UDPUpdateOne {
	return NewUDPClient(u.config).UpdateOne(u)
}

// Unwrap unwraps the Udp entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (u *Udp) Unwrap() *Udp {
	_tx, ok := u.config.driver.(*txDriver)
	if !ok {
		panic("ent: Udp is not a transactional entity")
	}
	u.config.driver = _tx.drv
	return u
}

// String implements the fmt.Stringer.
func (u *Udp) String() string {
	var builder strings.Builder
	builder.WriteString("Udp(")
	builder.WriteString(fmt.Sprintf("id=%v, ", u.ID))
	builder.WriteString("device_id=")
	builder.WriteString(u.DeviceID)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(u.Type)
	builder.WriteString(", ")
	builder.WriteString("header=")
	builder.WriteString(u.Header)
	builder.WriteString(", ")
	builder.WriteString("type_id=")
	builder.WriteString(u.TypeID)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(u.Description)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(u.Name)
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(u.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(u.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Udps is a parsable slice of Udp.
type Udps []*Udp
