// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/test_create_db"
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Test_create_db is the model entity for the Test_create_db schema.
type Test_create_db struct {
	config
	// ID of the ent.
	ID           int `json:"id,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Test_create_db) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case test_create_db.FieldID:
			values[i] = new(sql.NullInt64)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Test_create_db fields.
func (tcd *Test_create_db) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case test_create_db.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			tcd.ID = int(value.Int64)
		default:
			tcd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Test_create_db.
// This includes values selected through modifiers, order, etc.
func (tcd *Test_create_db) Value(name string) (ent.Value, error) {
	return tcd.selectValues.Get(name)
}

// Update returns a builder for updating this Test_create_db.
// Note that you need to call Test_create_db.Unwrap() before calling this method if this Test_create_db
// was returned from a transaction, and the transaction was committed or rolled back.
func (tcd *Test_create_db) Update() *TestCreateDbUpdateOne {
	return NewTestCreateDbClient(tcd.config).UpdateOne(tcd)
}

// Unwrap unwraps the Test_create_db entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (tcd *Test_create_db) Unwrap() *Test_create_db {
	_tx, ok := tcd.config.driver.(*txDriver)
	if !ok {
		panic("ent: Test_create_db is not a transactional entity")
	}
	tcd.config.driver = _tx.drv
	return tcd
}

// String implements the fmt.Stringer.
func (tcd *Test_create_db) String() string {
	var builder strings.Builder
	builder.WriteString("Test_create_db(")
	builder.WriteString(fmt.Sprintf("id=%v", tcd.ID))
	builder.WriteByte(')')
	return builder.String()
}

// Test_create_dbs is a parsable slice of Test_create_db.
type Test_create_dbs []*Test_create_db
