// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Modelmanager/internal/ent/model"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ModelCreate is the builder for creating a Model entity.
type ModelCreate struct {
	config
	mutation *ModelMutation
	hooks    []Hook
}

// SetModelID sets the "model_id" field.
func (mc *ModelCreate) SetModelID(s string) *ModelCreate {
	mc.mutation.SetModelID(s)
	return mc
}

// SetModelNameVersion sets the "model_name_version" field.
func (mc *ModelCreate) SetModelNameVersion(s string) *ModelCreate {
	mc.mutation.SetModelNameVersion(s)
	return mc
}

// SetFrameworkVersion sets the "framework_version" field.
func (mc *ModelCreate) SetFrameworkVersion(s string) *ModelCreate {
	mc.mutation.SetFrameworkVersion(s)
	return mc
}

// SetNillableFrameworkVersion sets the "framework_version" field if the given value is not nil.
func (mc *ModelCreate) SetNillableFrameworkVersion(s *string) *ModelCreate {
	if s != nil {
		mc.SetFrameworkVersion(*s)
	}
	return mc
}

// SetTypeDesc sets the "type_desc" field.
func (mc *ModelCreate) SetTypeDesc(s string) *ModelCreate {
	mc.mutation.SetTypeDesc(s)
	return mc
}

// SetNillableTypeDesc sets the "type_desc" field if the given value is not nil.
func (mc *ModelCreate) SetNillableTypeDesc(s *string) *ModelCreate {
	if s != nil {
		mc.SetTypeDesc(*s)
	}
	return mc
}

// SetStoragePath sets the "storage_path" field.
func (mc *ModelCreate) SetStoragePath(s string) *ModelCreate {
	mc.mutation.SetStoragePath(s)
	return mc
}

// SetAccuracy sets the "accuracy" field.
func (mc *ModelCreate) SetAccuracy(f float64) *ModelCreate {
	mc.mutation.SetAccuracy(f)
	return mc
}

// SetNillableAccuracy sets the "accuracy" field if the given value is not nil.
func (mc *ModelCreate) SetNillableAccuracy(f *float64) *ModelCreate {
	if f != nil {
		mc.SetAccuracy(*f)
	}
	return mc
}

// SetPrecision sets the "precision" field.
func (mc *ModelCreate) SetPrecision(f float64) *ModelCreate {
	mc.mutation.SetPrecision(f)
	return mc
}

// SetNillablePrecision sets the "precision" field if the given value is not nil.
func (mc *ModelCreate) SetNillablePrecision(f *float64) *ModelCreate {
	if f != nil {
		mc.SetPrecision(*f)
	}
	return mc
}

// SetRecall sets the "recall" field.
func (mc *ModelCreate) SetRecall(f float64) *ModelCreate {
	mc.mutation.SetRecall(f)
	return mc
}

// SetNillableRecall sets the "recall" field if the given value is not nil.
func (mc *ModelCreate) SetNillableRecall(f *float64) *ModelCreate {
	if f != nil {
		mc.SetRecall(*f)
	}
	return mc
}

// SetF1Score sets the "f1_score" field.
func (mc *ModelCreate) SetF1Score(f float64) *ModelCreate {
	mc.mutation.SetF1Score(f)
	return mc
}

// SetNillableF1Score sets the "f1_score" field if the given value is not nil.
func (mc *ModelCreate) SetNillableF1Score(f *float64) *ModelCreate {
	if f != nil {
		mc.SetF1Score(*f)
	}
	return mc
}

// SetCreatedBy sets the "created_by" field.
func (mc *ModelCreate) SetCreatedBy(s string) *ModelCreate {
	mc.mutation.SetCreatedBy(s)
	return mc
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (mc *ModelCreate) SetNillableCreatedBy(s *string) *ModelCreate {
	if s != nil {
		mc.SetCreatedBy(*s)
	}
	return mc
}

// SetCreateUnix sets the "create_unix" field.
func (mc *ModelCreate) SetCreateUnix(t time.Time) *ModelCreate {
	mc.mutation.SetCreateUnix(t)
	return mc
}

// SetNillableCreateUnix sets the "create_unix" field if the given value is not nil.
func (mc *ModelCreate) SetNillableCreateUnix(t *time.Time) *ModelCreate {
	if t != nil {
		mc.SetCreateUnix(*t)
	}
	return mc
}

// SetUpdateUnix sets the "update_unix" field.
func (mc *ModelCreate) SetUpdateUnix(t time.Time) *ModelCreate {
	mc.mutation.SetUpdateUnix(t)
	return mc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (mc *ModelCreate) SetNillableUpdateUnix(t *time.Time) *ModelCreate {
	if t != nil {
		mc.SetUpdateUnix(*t)
	}
	return mc
}

// Mutation returns the ModelMutation object of the builder.
func (mc *ModelCreate) Mutation() *ModelMutation {
	return mc.mutation
}

// Save creates the Model in the database.
func (mc *ModelCreate) Save(ctx context.Context) (*Model, error) {
	mc.defaults()
	return withHooks(ctx, mc.sqlSave, mc.mutation, mc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (mc *ModelCreate) SaveX(ctx context.Context) *Model {
	v, err := mc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mc *ModelCreate) Exec(ctx context.Context) error {
	_, err := mc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mc *ModelCreate) ExecX(ctx context.Context) {
	if err := mc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mc *ModelCreate) defaults() {
	if _, ok := mc.mutation.FrameworkVersion(); !ok {
		v := model.DefaultFrameworkVersion
		mc.mutation.SetFrameworkVersion(v)
	}
	if _, ok := mc.mutation.TypeDesc(); !ok {
		v := model.DefaultTypeDesc
		mc.mutation.SetTypeDesc(v)
	}
	if _, ok := mc.mutation.Accuracy(); !ok {
		v := model.DefaultAccuracy
		mc.mutation.SetAccuracy(v)
	}
	if _, ok := mc.mutation.Precision(); !ok {
		v := model.DefaultPrecision
		mc.mutation.SetPrecision(v)
	}
	if _, ok := mc.mutation.Recall(); !ok {
		v := model.DefaultRecall
		mc.mutation.SetRecall(v)
	}
	if _, ok := mc.mutation.F1Score(); !ok {
		v := model.DefaultF1Score
		mc.mutation.SetF1Score(v)
	}
	if _, ok := mc.mutation.CreatedBy(); !ok {
		v := model.DefaultCreatedBy
		mc.mutation.SetCreatedBy(v)
	}
	if _, ok := mc.mutation.CreateUnix(); !ok {
		v := model.DefaultCreateUnix()
		mc.mutation.SetCreateUnix(v)
	}
	if _, ok := mc.mutation.UpdateUnix(); !ok {
		v := model.DefaultUpdateUnix()
		mc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mc *ModelCreate) check() error {
	if _, ok := mc.mutation.ModelID(); !ok {
		return &ValidationError{Name: "model_id", err: errors.New(`ent: missing required field "Model.model_id"`)}
	}
	if v, ok := mc.mutation.ModelID(); ok {
		if err := model.ModelIDValidator(v); err != nil {
			return &ValidationError{Name: "model_id", err: fmt.Errorf(`ent: validator failed for field "Model.model_id": %w`, err)}
		}
	}
	if _, ok := mc.mutation.ModelNameVersion(); !ok {
		return &ValidationError{Name: "model_name_version", err: errors.New(`ent: missing required field "Model.model_name_version"`)}
	}
	if _, ok := mc.mutation.FrameworkVersion(); !ok {
		return &ValidationError{Name: "framework_version", err: errors.New(`ent: missing required field "Model.framework_version"`)}
	}
	if _, ok := mc.mutation.TypeDesc(); !ok {
		return &ValidationError{Name: "type_desc", err: errors.New(`ent: missing required field "Model.type_desc"`)}
	}
	if _, ok := mc.mutation.StoragePath(); !ok {
		return &ValidationError{Name: "storage_path", err: errors.New(`ent: missing required field "Model.storage_path"`)}
	}
	if v, ok := mc.mutation.StoragePath(); ok {
		if err := model.StoragePathValidator(v); err != nil {
			return &ValidationError{Name: "storage_path", err: fmt.Errorf(`ent: validator failed for field "Model.storage_path": %w`, err)}
		}
	}
	if _, ok := mc.mutation.Accuracy(); !ok {
		return &ValidationError{Name: "accuracy", err: errors.New(`ent: missing required field "Model.accuracy"`)}
	}
	if v, ok := mc.mutation.Accuracy(); ok {
		if err := model.AccuracyValidator(v); err != nil {
			return &ValidationError{Name: "accuracy", err: fmt.Errorf(`ent: validator failed for field "Model.accuracy": %w`, err)}
		}
	}
	if _, ok := mc.mutation.Precision(); !ok {
		return &ValidationError{Name: "precision", err: errors.New(`ent: missing required field "Model.precision"`)}
	}
	if v, ok := mc.mutation.Precision(); ok {
		if err := model.PrecisionValidator(v); err != nil {
			return &ValidationError{Name: "precision", err: fmt.Errorf(`ent: validator failed for field "Model.precision": %w`, err)}
		}
	}
	if _, ok := mc.mutation.Recall(); !ok {
		return &ValidationError{Name: "recall", err: errors.New(`ent: missing required field "Model.recall"`)}
	}
	if v, ok := mc.mutation.Recall(); ok {
		if err := model.RecallValidator(v); err != nil {
			return &ValidationError{Name: "recall", err: fmt.Errorf(`ent: validator failed for field "Model.recall": %w`, err)}
		}
	}
	if _, ok := mc.mutation.F1Score(); !ok {
		return &ValidationError{Name: "f1_score", err: errors.New(`ent: missing required field "Model.f1_score"`)}
	}
	if v, ok := mc.mutation.F1Score(); ok {
		if err := model.F1ScoreValidator(v); err != nil {
			return &ValidationError{Name: "f1_score", err: fmt.Errorf(`ent: validator failed for field "Model.f1_score": %w`, err)}
		}
	}
	if _, ok := mc.mutation.CreatedBy(); !ok {
		return &ValidationError{Name: "created_by", err: errors.New(`ent: missing required field "Model.created_by"`)}
	}
	if _, ok := mc.mutation.CreateUnix(); !ok {
		return &ValidationError{Name: "create_unix", err: errors.New(`ent: missing required field "Model.create_unix"`)}
	}
	if _, ok := mc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "Model.update_unix"`)}
	}
	return nil
}

func (mc *ModelCreate) sqlSave(ctx context.Context) (*Model, error) {
	if err := mc.check(); err != nil {
		return nil, err
	}
	_node, _spec := mc.createSpec()
	if err := sqlgraph.CreateNode(ctx, mc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	mc.mutation.id = &_node.ID
	mc.mutation.done = true
	return _node, nil
}

func (mc *ModelCreate) createSpec() (*Model, *sqlgraph.CreateSpec) {
	var (
		_node = &Model{config: mc.config}
		_spec = sqlgraph.NewCreateSpec(model.Table, sqlgraph.NewFieldSpec(model.FieldID, field.TypeInt))
	)
	if value, ok := mc.mutation.ModelID(); ok {
		_spec.SetField(model.FieldModelID, field.TypeString, value)
		_node.ModelID = value
	}
	if value, ok := mc.mutation.ModelNameVersion(); ok {
		_spec.SetField(model.FieldModelNameVersion, field.TypeString, value)
		_node.ModelNameVersion = value
	}
	if value, ok := mc.mutation.FrameworkVersion(); ok {
		_spec.SetField(model.FieldFrameworkVersion, field.TypeString, value)
		_node.FrameworkVersion = value
	}
	if value, ok := mc.mutation.TypeDesc(); ok {
		_spec.SetField(model.FieldTypeDesc, field.TypeString, value)
		_node.TypeDesc = value
	}
	if value, ok := mc.mutation.StoragePath(); ok {
		_spec.SetField(model.FieldStoragePath, field.TypeString, value)
		_node.StoragePath = value
	}
	if value, ok := mc.mutation.Accuracy(); ok {
		_spec.SetField(model.FieldAccuracy, field.TypeFloat64, value)
		_node.Accuracy = value
	}
	if value, ok := mc.mutation.Precision(); ok {
		_spec.SetField(model.FieldPrecision, field.TypeFloat64, value)
		_node.Precision = value
	}
	if value, ok := mc.mutation.Recall(); ok {
		_spec.SetField(model.FieldRecall, field.TypeFloat64, value)
		_node.Recall = value
	}
	if value, ok := mc.mutation.F1Score(); ok {
		_spec.SetField(model.FieldF1Score, field.TypeFloat64, value)
		_node.F1Score = value
	}
	if value, ok := mc.mutation.CreatedBy(); ok {
		_spec.SetField(model.FieldCreatedBy, field.TypeString, value)
		_node.CreatedBy = value
	}
	if value, ok := mc.mutation.CreateUnix(); ok {
		_spec.SetField(model.FieldCreateUnix, field.TypeTime, value)
		_node.CreateUnix = value
	}
	if value, ok := mc.mutation.UpdateUnix(); ok {
		_spec.SetField(model.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	return _node, _spec
}

// ModelCreateBulk is the builder for creating many Model entities in bulk.
type ModelCreateBulk struct {
	config
	err      error
	builders []*ModelCreate
}

// Save creates the Model entities in the database.
func (mcb *ModelCreateBulk) Save(ctx context.Context) ([]*Model, error) {
	if mcb.err != nil {
		return nil, mcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mcb.builders))
	nodes := make([]*Model, len(mcb.builders))
	mutators := make([]Mutator, len(mcb.builders))
	for i := range mcb.builders {
		func(i int, root context.Context) {
			builder := mcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ModelMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mcb *ModelCreateBulk) SaveX(ctx context.Context) []*Model {
	v, err := mcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mcb *ModelCreateBulk) Exec(ctx context.Context) error {
	_, err := mcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mcb *ModelCreateBulk) ExecX(ctx context.Context) {
	if err := mcb.Exec(ctx); err != nil {
		panic(err)
	}
}
