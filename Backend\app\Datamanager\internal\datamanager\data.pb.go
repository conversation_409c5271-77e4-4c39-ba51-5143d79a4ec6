// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: rpc/data.proto

package datamanager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TableInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TableUid      string                 `protobuf:"bytes,1,opt,name=table_uid,json=tableUid,proto3" json:"table_uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableInfo) Reset() {
	*x = TableInfo{}
	mi := &file_rpc_data_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableInfo) ProtoMessage() {}

func (x *TableInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableInfo.ProtoReflect.Descriptor instead.
func (*TableInfo) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{0}
}

func (x *TableInfo) GetTableUid() string {
	if x != nil {
		return x.TableUid
	}
	return ""
}

type InstanceTagInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InstanceName  string                 `protobuf:"bytes,1,opt,name=instance_name,json=instanceName,proto3" json:"instance_name,omitempty"`
	InstanceUid   string                 `protobuf:"bytes,2,opt,name=instance_uid,json=instanceUid,proto3" json:"instance_uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InstanceTagInfo) Reset() {
	*x = InstanceTagInfo{}
	mi := &file_rpc_data_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstanceTagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstanceTagInfo) ProtoMessage() {}

func (x *InstanceTagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstanceTagInfo.ProtoReflect.Descriptor instead.
func (*InstanceTagInfo) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{1}
}

func (x *InstanceTagInfo) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

func (x *InstanceTagInfo) GetInstanceUid() string {
	if x != nil {
		return x.InstanceUid
	}
	return ""
}

type DataGroupTagInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DatagroupName string                 `protobuf:"bytes,1,opt,name=datagroup_name,json=datagroupName,proto3" json:"datagroup_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataGroupTagInfo) Reset() {
	*x = DataGroupTagInfo{}
	mi := &file_rpc_data_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataGroupTagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataGroupTagInfo) ProtoMessage() {}

func (x *DataGroupTagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataGroupTagInfo.ProtoReflect.Descriptor instead.
func (*DataGroupTagInfo) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{2}
}

func (x *DataGroupTagInfo) GetDatagroupName() string {
	if x != nil {
		return x.DatagroupName
	}
	return ""
}

type FieldsInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FieldName     []string               `protobuf:"bytes,1,rep,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FieldsInfo) Reset() {
	*x = FieldsInfo{}
	mi := &file_rpc_data_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FieldsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldsInfo) ProtoMessage() {}

func (x *FieldsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldsInfo.ProtoReflect.Descriptor instead.
func (*FieldsInfo) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{3}
}

func (x *FieldsInfo) GetFieldName() []string {
	if x != nil {
		return x.FieldName
	}
	return nil
}

type WhereInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ColumnName    string                 `protobuf:"bytes,1,opt,name=column_name,json=columnName,proto3" json:"column_name,omitempty"`
	CompareSymbol string                 `protobuf:"bytes,2,opt,name=compare_symbol,json=compareSymbol,proto3" json:"compare_symbol,omitempty"`
	CompareValue  string                 `protobuf:"bytes,3,opt,name=compare_value,json=compareValue,proto3" json:"compare_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WhereInfo) Reset() {
	*x = WhereInfo{}
	mi := &file_rpc_data_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhereInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhereInfo) ProtoMessage() {}

func (x *WhereInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhereInfo.ProtoReflect.Descriptor instead.
func (*WhereInfo) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{4}
}

func (x *WhereInfo) GetColumnName() string {
	if x != nil {
		return x.ColumnName
	}
	return ""
}

func (x *WhereInfo) GetCompareSymbol() string {
	if x != nil {
		return x.CompareSymbol
	}
	return ""
}

func (x *WhereInfo) GetCompareValue() string {
	if x != nil {
		return x.CompareValue
	}
	return ""
}

type DataRow struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ts            *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	InstanceInfo  *InstanceTagInfo       `protobuf:"bytes,2,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	DatagroupInfo *DataGroupTagInfo      `protobuf:"bytes,3,opt,name=datagroup_info,json=datagroupInfo,proto3" json:"datagroup_info,omitempty"`
	Datas         []*SingleData          `protobuf:"bytes,4,rep,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataRow) Reset() {
	*x = DataRow{}
	mi := &file_rpc_data_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataRow) ProtoMessage() {}

func (x *DataRow) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataRow.ProtoReflect.Descriptor instead.
func (*DataRow) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{5}
}

func (x *DataRow) GetTs() *timestamppb.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *DataRow) GetInstanceInfo() *InstanceTagInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *DataRow) GetDatagroupInfo() *DataGroupTagInfo {
	if x != nil {
		return x.DatagroupInfo
	}
	return nil
}

func (x *DataRow) GetDatas() []*SingleData {
	if x != nil {
		return x.Datas
	}
	return nil
}

type SingleData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SingleData) Reset() {
	*x = SingleData{}
	mi := &file_rpc_data_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SingleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleData) ProtoMessage() {}

func (x *SingleData) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleData.ProtoReflect.Descriptor instead.
func (*SingleData) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{6}
}

func (x *SingleData) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SingleData) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GetProducerDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TableInfo     *TableInfo             `protobuf:"bytes,1,opt,name=table_info,json=tableInfo,proto3" json:"table_info,omitempty"`
	Revision      int64                  `protobuf:"varint,2,opt,name=revision,proto3" json:"revision,omitempty"`
	WhereInfos    []*WhereInfo           `protobuf:"bytes,3,rep,name=where_infos,json=whereInfos,proto3" json:"where_infos,omitempty"`
	DataRowLimit  uint64                 `protobuf:"varint,4,opt,name=data_row_limit,json=dataRowLimit,proto3" json:"data_row_limit,omitempty"`
	DatagroupInfo *DataGroupTagInfo      `protobuf:"bytes,5,opt,name=datagroup_info,json=datagroupInfo,proto3" json:"datagroup_info,omitempty"`
	InstanceInfo  *InstanceTagInfo       `protobuf:"bytes,6,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	FieldsInfo    *FieldsInfo            `protobuf:"bytes,7,opt,name=fields_info,json=fieldsInfo,proto3" json:"fields_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProducerDataRequest) Reset() {
	*x = GetProducerDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProducerDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProducerDataRequest) ProtoMessage() {}

func (x *GetProducerDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProducerDataRequest.ProtoReflect.Descriptor instead.
func (*GetProducerDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{7}
}

func (x *GetProducerDataRequest) GetTableInfo() *TableInfo {
	if x != nil {
		return x.TableInfo
	}
	return nil
}

func (x *GetProducerDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *GetProducerDataRequest) GetWhereInfos() []*WhereInfo {
	if x != nil {
		return x.WhereInfos
	}
	return nil
}

func (x *GetProducerDataRequest) GetDataRowLimit() uint64 {
	if x != nil {
		return x.DataRowLimit
	}
	return 0
}

func (x *GetProducerDataRequest) GetDatagroupInfo() *DataGroupTagInfo {
	if x != nil {
		return x.DatagroupInfo
	}
	return nil
}

func (x *GetProducerDataRequest) GetInstanceInfo() *InstanceTagInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *GetProducerDataRequest) GetFieldsInfo() *FieldsInfo {
	if x != nil {
		return x.FieldsInfo
	}
	return nil
}

type GetProducerDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DataCount     uint64                 `protobuf:"varint,2,opt,name=data_count,json=dataCount,proto3" json:"data_count,omitempty"`
	DataRows      []*DataRow             `protobuf:"bytes,3,rep,name=data_rows,json=dataRows,proto3" json:"data_rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProducerDataResponse) Reset() {
	*x = GetProducerDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProducerDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProducerDataResponse) ProtoMessage() {}

func (x *GetProducerDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProducerDataResponse.ProtoReflect.Descriptor instead.
func (*GetProducerDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{8}
}

func (x *GetProducerDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetProducerDataResponse) GetDataCount() uint64 {
	if x != nil {
		return x.DataCount
	}
	return 0
}

func (x *GetProducerDataResponse) GetDataRows() []*DataRow {
	if x != nil {
		return x.DataRows
	}
	return nil
}

type DeleteProducerDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TableInfo     *TableInfo             `protobuf:"bytes,1,opt,name=table_info,json=tableInfo,proto3" json:"table_info,omitempty"`
	Revision      int64                  `protobuf:"varint,2,opt,name=revision,proto3" json:"revision,omitempty"`
	WhereInfos    []*WhereInfo           `protobuf:"bytes,3,rep,name=where_infos,json=whereInfos,proto3" json:"where_infos,omitempty"`
	DatagroupInfo *DataGroupTagInfo      `protobuf:"bytes,4,opt,name=datagroup_info,json=datagroupInfo,proto3" json:"datagroup_info,omitempty"`
	InstanceInfo  *InstanceTagInfo       `protobuf:"bytes,5,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	FieldsInfo    *FieldsInfo            `protobuf:"bytes,6,opt,name=fields_info,json=fieldsInfo,proto3" json:"fields_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteProducerDataRequest) Reset() {
	*x = DeleteProducerDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteProducerDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProducerDataRequest) ProtoMessage() {}

func (x *DeleteProducerDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProducerDataRequest.ProtoReflect.Descriptor instead.
func (*DeleteProducerDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteProducerDataRequest) GetTableInfo() *TableInfo {
	if x != nil {
		return x.TableInfo
	}
	return nil
}

func (x *DeleteProducerDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *DeleteProducerDataRequest) GetWhereInfos() []*WhereInfo {
	if x != nil {
		return x.WhereInfos
	}
	return nil
}

func (x *DeleteProducerDataRequest) GetDatagroupInfo() *DataGroupTagInfo {
	if x != nil {
		return x.DatagroupInfo
	}
	return nil
}

func (x *DeleteProducerDataRequest) GetInstanceInfo() *InstanceTagInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *DeleteProducerDataRequest) GetFieldsInfo() *FieldsInfo {
	if x != nil {
		return x.FieldsInfo
	}
	return nil
}

type DeleteProducerDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RowsAffected  uint64                 `protobuf:"varint,2,opt,name=rows_affected,json=rowsAffected,proto3" json:"rows_affected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteProducerDataResponse) Reset() {
	*x = DeleteProducerDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteProducerDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProducerDataResponse) ProtoMessage() {}

func (x *DeleteProducerDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProducerDataResponse.ProtoReflect.Descriptor instead.
func (*DeleteProducerDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteProducerDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeleteProducerDataResponse) GetRowsAffected() uint64 {
	if x != nil {
		return x.RowsAffected
	}
	return 0
}

type ChangeProducerDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TableInfo     *TableInfo             `protobuf:"bytes,1,opt,name=table_info,json=tableInfo,proto3" json:"table_info,omitempty"`
	Revision      int64                  `protobuf:"varint,2,opt,name=revision,proto3" json:"revision,omitempty"`
	DataRows      []*DataRow             `protobuf:"bytes,3,rep,name=data_rows,json=dataRows,proto3" json:"data_rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeProducerDataRequest) Reset() {
	*x = ChangeProducerDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeProducerDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeProducerDataRequest) ProtoMessage() {}

func (x *ChangeProducerDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeProducerDataRequest.ProtoReflect.Descriptor instead.
func (*ChangeProducerDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{11}
}

func (x *ChangeProducerDataRequest) GetTableInfo() *TableInfo {
	if x != nil {
		return x.TableInfo
	}
	return nil
}

func (x *ChangeProducerDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *ChangeProducerDataRequest) GetDataRows() []*DataRow {
	if x != nil {
		return x.DataRows
	}
	return nil
}

type ChangeProducerDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeProducerDataResponse) Reset() {
	*x = ChangeProducerDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeProducerDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeProducerDataResponse) ProtoMessage() {}

func (x *ChangeProducerDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeProducerDataResponse.ProtoReflect.Descriptor instead.
func (*ChangeProducerDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{12}
}

func (x *ChangeProducerDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetInstanceDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DatagroupInfo *DataGroupTagInfo      `protobuf:"bytes,1,opt,name=datagroup_info,json=datagroupInfo,proto3" json:"datagroup_info,omitempty"`
	Revision      int64                  `protobuf:"varint,2,opt,name=revision,proto3" json:"revision,omitempty"`
	DataRowLimit  uint64                 `protobuf:"varint,3,opt,name=data_row_limit,json=dataRowLimit,proto3" json:"data_row_limit,omitempty"`
	InstanceInfo  *InstanceTagInfo       `protobuf:"bytes,4,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	FieldsInfo    *FieldsInfo            `protobuf:"bytes,5,opt,name=fields_info,json=fieldsInfo,proto3" json:"fields_info,omitempty"`
	WhereInfos    []*WhereInfo           `protobuf:"bytes,6,rep,name=where_infos,json=whereInfos,proto3" json:"where_infos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInstanceDataRequest) Reset() {
	*x = GetInstanceDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInstanceDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceDataRequest) ProtoMessage() {}

func (x *GetInstanceDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceDataRequest.ProtoReflect.Descriptor instead.
func (*GetInstanceDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{13}
}

func (x *GetInstanceDataRequest) GetDatagroupInfo() *DataGroupTagInfo {
	if x != nil {
		return x.DatagroupInfo
	}
	return nil
}

func (x *GetInstanceDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *GetInstanceDataRequest) GetDataRowLimit() uint64 {
	if x != nil {
		return x.DataRowLimit
	}
	return 0
}

func (x *GetInstanceDataRequest) GetInstanceInfo() *InstanceTagInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *GetInstanceDataRequest) GetFieldsInfo() *FieldsInfo {
	if x != nil {
		return x.FieldsInfo
	}
	return nil
}

func (x *GetInstanceDataRequest) GetWhereInfos() []*WhereInfo {
	if x != nil {
		return x.WhereInfos
	}
	return nil
}

type GetInstanceDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DataCount     uint64                 `protobuf:"varint,2,opt,name=data_count,json=dataCount,proto3" json:"data_count,omitempty"`
	DataRows      []*DataRow             `protobuf:"bytes,3,rep,name=data_rows,json=dataRows,proto3" json:"data_rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInstanceDataResponse) Reset() {
	*x = GetInstanceDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInstanceDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstanceDataResponse) ProtoMessage() {}

func (x *GetInstanceDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstanceDataResponse.ProtoReflect.Descriptor instead.
func (*GetInstanceDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{14}
}

func (x *GetInstanceDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetInstanceDataResponse) GetDataCount() uint64 {
	if x != nil {
		return x.DataCount
	}
	return 0
}

func (x *GetInstanceDataResponse) GetDataRows() []*DataRow {
	if x != nil {
		return x.DataRows
	}
	return nil
}

type DeleteInstanceDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InstanceInfo  *InstanceTagInfo       `protobuf:"bytes,1,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	Revision      int64                  `protobuf:"varint,2,opt,name=revision,proto3" json:"revision,omitempty"`
	WhereInfos    []*WhereInfo           `protobuf:"bytes,3,rep,name=where_infos,json=whereInfos,proto3" json:"where_infos,omitempty"`
	DatagroupInfo *DataGroupTagInfo      `protobuf:"bytes,4,opt,name=datagroup_info,json=datagroupInfo,proto3" json:"datagroup_info,omitempty"`
	FieldsInfo    *FieldsInfo            `protobuf:"bytes,5,opt,name=fields_info,json=fieldsInfo,proto3" json:"fields_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteInstanceDataRequest) Reset() {
	*x = DeleteInstanceDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteInstanceDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInstanceDataRequest) ProtoMessage() {}

func (x *DeleteInstanceDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInstanceDataRequest.ProtoReflect.Descriptor instead.
func (*DeleteInstanceDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteInstanceDataRequest) GetInstanceInfo() *InstanceTagInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *DeleteInstanceDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *DeleteInstanceDataRequest) GetWhereInfos() []*WhereInfo {
	if x != nil {
		return x.WhereInfos
	}
	return nil
}

func (x *DeleteInstanceDataRequest) GetDatagroupInfo() *DataGroupTagInfo {
	if x != nil {
		return x.DatagroupInfo
	}
	return nil
}

func (x *DeleteInstanceDataRequest) GetFieldsInfo() *FieldsInfo {
	if x != nil {
		return x.FieldsInfo
	}
	return nil
}

type DeleteInstanceDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RowsAffected  uint64                 `protobuf:"varint,2,opt,name=rows_affected,json=rowsAffected,proto3" json:"rows_affected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteInstanceDataResponse) Reset() {
	*x = DeleteInstanceDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteInstanceDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInstanceDataResponse) ProtoMessage() {}

func (x *DeleteInstanceDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInstanceDataResponse.ProtoReflect.Descriptor instead.
func (*DeleteInstanceDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteInstanceDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DeleteInstanceDataResponse) GetRowsAffected() uint64 {
	if x != nil {
		return x.RowsAffected
	}
	return 0
}

type ChangeInstanceDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InstanceInfo  *InstanceTagInfo       `protobuf:"bytes,1,opt,name=instance_info,json=instanceInfo,proto3" json:"instance_info,omitempty"`
	Revision      int64                  `protobuf:"varint,2,opt,name=revision,proto3" json:"revision,omitempty"`
	DataRows      []*DataRow             `protobuf:"bytes,3,rep,name=data_rows,json=dataRows,proto3" json:"data_rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeInstanceDataRequest) Reset() {
	*x = ChangeInstanceDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeInstanceDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeInstanceDataRequest) ProtoMessage() {}

func (x *ChangeInstanceDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeInstanceDataRequest.ProtoReflect.Descriptor instead.
func (*ChangeInstanceDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{17}
}

func (x *ChangeInstanceDataRequest) GetInstanceInfo() *InstanceTagInfo {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *ChangeInstanceDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *ChangeInstanceDataRequest) GetDataRows() []*DataRow {
	if x != nil {
		return x.DataRows
	}
	return nil
}

type ChangeInstanceDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeInstanceDataResponse) Reset() {
	*x = ChangeInstanceDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeInstanceDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeInstanceDataResponse) ProtoMessage() {}

func (x *ChangeInstanceDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeInstanceDataResponse.ProtoReflect.Descriptor instead.
func (*ChangeInstanceDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{18}
}

func (x *ChangeInstanceDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ChangeDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Revision      int64                  `protobuf:"varint,1,opt,name=revision,proto3" json:"revision,omitempty"`
	DataRows      []*DataRow             `protobuf:"bytes,2,rep,name=data_rows,json=dataRows,proto3" json:"data_rows,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeDataRequest) Reset() {
	*x = ChangeDataRequest{}
	mi := &file_rpc_data_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeDataRequest) ProtoMessage() {}

func (x *ChangeDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeDataRequest.ProtoReflect.Descriptor instead.
func (*ChangeDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{19}
}

func (x *ChangeDataRequest) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *ChangeDataRequest) GetDataRows() []*DataRow {
	if x != nil {
		return x.DataRows
	}
	return nil
}

type ChangeDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeDataResponse) Reset() {
	*x = ChangeDataResponse{}
	mi := &file_rpc_data_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeDataResponse) ProtoMessage() {}

func (x *ChangeDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_data_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeDataResponse.ProtoReflect.Descriptor instead.
func (*ChangeDataResponse) Descriptor() ([]byte, []int) {
	return file_rpc_data_proto_rawDescGZIP(), []int{20}
}

func (x *ChangeDataResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_rpc_data_proto protoreflect.FileDescriptor

var file_rpc_data_proto_rawDesc = string([]byte{
	0x0a, 0x0e, 0x72, 0x70, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x28,
	0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x69, 0x64, 0x22, 0x59, 0x0a, 0x0f, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x55, 0x69, 0x64, 0x22, 0x39, 0x0a, 0x10, 0x44, 0x61, 0x74, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2b,
	0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x78, 0x0a, 0x09, 0x57,
	0x68, 0x65, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x07, 0x44, 0x61, 0x74, 0x61, 0x52, 0x6f,
	0x77, 0x12, 0x2a, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x41, 0x0a,
	0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x44, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x22, 0x38, 0x0a, 0x0a, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x8d, 0x03, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x0b, 0x77, 0x68, 0x65, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x57, 0x68, 0x65, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77, 0x68, 0x65, 0x72,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72,
	0x6f, 0x77, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x44, 0x0a, 0x0e,
	0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x83, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x31, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x6f, 0x77, 0x73, 0x22, 0xea, 0x02, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x77, 0x68, 0x65, 0x72, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x68, 0x65, 0x72, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77, 0x68, 0x65, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x44, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54,
	0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x59, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f, 0x77, 0x73,
	0x5f, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x72, 0x6f, 0x77, 0x73, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xa1, 0x01,
	0x0a, 0x19, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77,
	0x73, 0x22, 0x34, 0x0a, 0x1a, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd6, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x44, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x6f, 0x77,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x6f, 0x77, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a,
	0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x0b, 0x77, 0x68, 0x65, 0x72, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x68, 0x65, 0x72, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77, 0x68, 0x65, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x22, 0x83, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x6f, 0x77, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x6f, 0x77, 0x73, 0x22, 0xb3, 0x02, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x77, 0x68, 0x65, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x57, 0x68, 0x65, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x77, 0x68, 0x65, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x44, 0x0a, 0x0e,
	0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x59, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f, 0x77, 0x73, 0x5f, 0x61, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x72, 0x6f, 0x77, 0x73, 0x41,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x19, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x6f, 0x77,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x73, 0x22, 0x34, 0x0a, 0x1a, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x62, 0x0a,
	0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x52, 0x6f, 0x77,
	0x73, 0x22, 0x2c, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32,
	0xad, 0x05, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a,
	0x12, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x26, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4d, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x0f, 0x5a, 0x0d, 0x2e, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_rpc_data_proto_rawDescOnce sync.Once
	file_rpc_data_proto_rawDescData []byte
)

func file_rpc_data_proto_rawDescGZIP() []byte {
	file_rpc_data_proto_rawDescOnce.Do(func() {
		file_rpc_data_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_data_proto_rawDesc), len(file_rpc_data_proto_rawDesc)))
	})
	return file_rpc_data_proto_rawDescData
}

var file_rpc_data_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_rpc_data_proto_goTypes = []any{
	(*TableInfo)(nil),                  // 0: datamanager.TableInfo
	(*InstanceTagInfo)(nil),            // 1: datamanager.InstanceTagInfo
	(*DataGroupTagInfo)(nil),           // 2: datamanager.DataGroupTagInfo
	(*FieldsInfo)(nil),                 // 3: datamanager.FieldsInfo
	(*WhereInfo)(nil),                  // 4: datamanager.WhereInfo
	(*DataRow)(nil),                    // 5: datamanager.DataRow
	(*SingleData)(nil),                 // 6: datamanager.SingleData
	(*GetProducerDataRequest)(nil),     // 7: datamanager.GetProducerDataRequest
	(*GetProducerDataResponse)(nil),    // 8: datamanager.GetProducerDataResponse
	(*DeleteProducerDataRequest)(nil),  // 9: datamanager.DeleteProducerDataRequest
	(*DeleteProducerDataResponse)(nil), // 10: datamanager.DeleteProducerDataResponse
	(*ChangeProducerDataRequest)(nil),  // 11: datamanager.ChangeProducerDataRequest
	(*ChangeProducerDataResponse)(nil), // 12: datamanager.ChangeProducerDataResponse
	(*GetInstanceDataRequest)(nil),     // 13: datamanager.GetInstanceDataRequest
	(*GetInstanceDataResponse)(nil),    // 14: datamanager.GetInstanceDataResponse
	(*DeleteInstanceDataRequest)(nil),  // 15: datamanager.DeleteInstanceDataRequest
	(*DeleteInstanceDataResponse)(nil), // 16: datamanager.DeleteInstanceDataResponse
	(*ChangeInstanceDataRequest)(nil),  // 17: datamanager.ChangeInstanceDataRequest
	(*ChangeInstanceDataResponse)(nil), // 18: datamanager.ChangeInstanceDataResponse
	(*ChangeDataRequest)(nil),          // 19: datamanager.ChangeDataRequest
	(*ChangeDataResponse)(nil),         // 20: datamanager.ChangeDataResponse
	(*timestamppb.Timestamp)(nil),      // 21: google.protobuf.Timestamp
}
var file_rpc_data_proto_depIdxs = []int32{
	21, // 0: datamanager.DataRow.ts:type_name -> google.protobuf.Timestamp
	1,  // 1: datamanager.DataRow.instance_info:type_name -> datamanager.InstanceTagInfo
	2,  // 2: datamanager.DataRow.datagroup_info:type_name -> datamanager.DataGroupTagInfo
	6,  // 3: datamanager.DataRow.datas:type_name -> datamanager.SingleData
	0,  // 4: datamanager.GetProducerDataRequest.table_info:type_name -> datamanager.TableInfo
	4,  // 5: datamanager.GetProducerDataRequest.where_infos:type_name -> datamanager.WhereInfo
	2,  // 6: datamanager.GetProducerDataRequest.datagroup_info:type_name -> datamanager.DataGroupTagInfo
	1,  // 7: datamanager.GetProducerDataRequest.instance_info:type_name -> datamanager.InstanceTagInfo
	3,  // 8: datamanager.GetProducerDataRequest.fields_info:type_name -> datamanager.FieldsInfo
	5,  // 9: datamanager.GetProducerDataResponse.data_rows:type_name -> datamanager.DataRow
	0,  // 10: datamanager.DeleteProducerDataRequest.table_info:type_name -> datamanager.TableInfo
	4,  // 11: datamanager.DeleteProducerDataRequest.where_infos:type_name -> datamanager.WhereInfo
	2,  // 12: datamanager.DeleteProducerDataRequest.datagroup_info:type_name -> datamanager.DataGroupTagInfo
	1,  // 13: datamanager.DeleteProducerDataRequest.instance_info:type_name -> datamanager.InstanceTagInfo
	3,  // 14: datamanager.DeleteProducerDataRequest.fields_info:type_name -> datamanager.FieldsInfo
	0,  // 15: datamanager.ChangeProducerDataRequest.table_info:type_name -> datamanager.TableInfo
	5,  // 16: datamanager.ChangeProducerDataRequest.data_rows:type_name -> datamanager.DataRow
	2,  // 17: datamanager.GetInstanceDataRequest.datagroup_info:type_name -> datamanager.DataGroupTagInfo
	1,  // 18: datamanager.GetInstanceDataRequest.instance_info:type_name -> datamanager.InstanceTagInfo
	3,  // 19: datamanager.GetInstanceDataRequest.fields_info:type_name -> datamanager.FieldsInfo
	4,  // 20: datamanager.GetInstanceDataRequest.where_infos:type_name -> datamanager.WhereInfo
	5,  // 21: datamanager.GetInstanceDataResponse.data_rows:type_name -> datamanager.DataRow
	1,  // 22: datamanager.DeleteInstanceDataRequest.instance_info:type_name -> datamanager.InstanceTagInfo
	4,  // 23: datamanager.DeleteInstanceDataRequest.where_infos:type_name -> datamanager.WhereInfo
	2,  // 24: datamanager.DeleteInstanceDataRequest.datagroup_info:type_name -> datamanager.DataGroupTagInfo
	3,  // 25: datamanager.DeleteInstanceDataRequest.fields_info:type_name -> datamanager.FieldsInfo
	1,  // 26: datamanager.ChangeInstanceDataRequest.instance_info:type_name -> datamanager.InstanceTagInfo
	5,  // 27: datamanager.ChangeInstanceDataRequest.data_rows:type_name -> datamanager.DataRow
	5,  // 28: datamanager.ChangeDataRequest.data_rows:type_name -> datamanager.DataRow
	7,  // 29: datamanager.Data.GetProducerData:input_type -> datamanager.GetProducerDataRequest
	9,  // 30: datamanager.Data.DeleteProducerData:input_type -> datamanager.DeleteProducerDataRequest
	11, // 31: datamanager.Data.ChangeProducerData:input_type -> datamanager.ChangeProducerDataRequest
	13, // 32: datamanager.Data.GetInstanceData:input_type -> datamanager.GetInstanceDataRequest
	15, // 33: datamanager.Data.DeleteInstanceData:input_type -> datamanager.DeleteInstanceDataRequest
	17, // 34: datamanager.Data.ChangeInstanceData:input_type -> datamanager.ChangeInstanceDataRequest
	19, // 35: datamanager.Data.ChangeData:input_type -> datamanager.ChangeDataRequest
	8,  // 36: datamanager.Data.GetProducerData:output_type -> datamanager.GetProducerDataResponse
	10, // 37: datamanager.Data.DeleteProducerData:output_type -> datamanager.DeleteProducerDataResponse
	12, // 38: datamanager.Data.ChangeProducerData:output_type -> datamanager.ChangeProducerDataResponse
	14, // 39: datamanager.Data.GetInstanceData:output_type -> datamanager.GetInstanceDataResponse
	16, // 40: datamanager.Data.DeleteInstanceData:output_type -> datamanager.DeleteInstanceDataResponse
	18, // 41: datamanager.Data.ChangeInstanceData:output_type -> datamanager.ChangeInstanceDataResponse
	20, // 42: datamanager.Data.ChangeData:output_type -> datamanager.ChangeDataResponse
	36, // [36:43] is the sub-list for method output_type
	29, // [29:36] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_rpc_data_proto_init() }
func file_rpc_data_proto_init() {
	if File_rpc_data_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_data_proto_rawDesc), len(file_rpc_data_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_data_proto_goTypes,
		DependencyIndexes: file_rpc_data_proto_depIdxs,
		MessageInfos:      file_rpc_data_proto_msgTypes,
	}.Build()
	File_rpc_data_proto = out.File
	file_rpc_data_proto_goTypes = nil
	file_rpc_data_proto_depIdxs = nil
}
