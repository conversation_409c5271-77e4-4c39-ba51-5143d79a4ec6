package logic

import (
	"context"
	"fmt"
	"net/http"

	"strings"

	"GCF/app/Devicemanager/internal/ent"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type ListDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDeviceLogic {
	return &ListDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDeviceLogic) ListDevice(req *types.ListDeviceRequest) (resp *types.ListDeviceResponse, err error) {
	// 查询设备
	queryDevice := l.svcCtx.Db.Device.Query()
	// 根据请求参数添加查询条件
	if req != nil {
		if req.DeviceUID != "" {
			queryDevice = queryDevice.Where(device.IDEQ(req.DeviceUID))
		}
		if req.FrameMetas != nil {
			for _, frameMeta := range req.FrameMetas {
				queryDevice = queryDevice.Where(device.ProtocolContains(frameMeta.FrameType))
			}
		}
		if req.HealthStatus != "" {
			queryDevice = queryDevice.Where(device.StatusEQ(req.HealthStatus))
		}
	}
	// 2. 排序：按创建时间倒序（最新的在最上面）
	queryDevice = queryDevice.Order(ent.Desc(device.FieldCreateUnix))
	// 获取总数
	total, err := queryDevice.Count(l.ctx)
	if err != nil {
		return nil, xerrors.New(http.StatusServiceUnavailable, "查询设备总数失败")
	}

	// 分页逻辑
	if req != nil && req.PageNum > 0 && req.PageSize > 0 {
		offset := (req.PageNum - 1) * req.PageSize
		queryDevice = queryDevice.Offset(int(offset)).Limit(int(req.PageSize))
	}

	devices, err := queryDevice.All(l.ctx)
	if err != nil {
		Msg := fmt.Sprintf("分页查询设备失败: %v", err)
		return nil, xerrors.New(http.StatusServiceUnavailable, Msg)
	}

	resp = &types.ListDeviceResponse{
		Devices:  make([]types.ListDevice, 0, len(devices)),
		Total:    int64(total),
		PageNum:  req.PageNum,
		PageSize: req.PageSize,
	}

	for _, device := range devices {
		listDevice, err := utils.GetDevicesInfo(l.ctx, l.svcCtx, device)
		if err != nil {
			return nil, xerrors.New(http.StatusServiceUnavailable, "获取设备信息失败")
		}
		resp.Devices = append(resp.Devices, *listDevice)
	}
	return resp, nil
}

func string2Data(index, name, dataType string) ([]types.DataDef, error) {
	var datas []types.DataDef
	if index == "" || name == "" || dataType == "" {
		return nil, xerrors.New(http.StatusBadRequest, "empty input parameters")
	}
	dataindex := strings.Split(index, ",")
	dataname := strings.Split(name, ",")
	datatypes := strings.Split(dataType, ",")
	for i, _ := range dataindex {
		datas = append(datas, types.DataDef{Index: dataindex[i], Name: dataname[i], Type: datatypes[i]})
	}
	return datas, nil
}
