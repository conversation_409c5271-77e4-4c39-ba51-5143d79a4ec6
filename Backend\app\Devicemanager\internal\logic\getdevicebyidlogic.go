package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeviceByIDLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDeviceByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceByIDLogic {
	return &GetDeviceByIDLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetDeviceByIDLogic) GetDeviceByID(in *devicemanager.GetDeviceByIDRequest) (*devicemanager.GetDeviceByIDResponse, error) {
	//TODO: 可以修改为检查缓存
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(in.DeviceId)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	DeviceResourceInfo, err := utils.GetDeviceResourceInfo(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}

	return &devicemanager.GetDeviceByIDResponse{
		DeviceResourceInfo: &devicemanager.DeviceResourceInfo{
			Ip:   DeviceResourceInfo.IP,
			Name: DeviceResourceInfo.DeviceName,
			Type: DeviceResourceInfo.Type,
			Os:   DeviceResourceInfo.OS,
			Cpu: &devicemanager.ResourceDef{
				Amount: DeviceResourceInfo.CPU.Amount,
				Type:   DeviceResourceInfo.CPU.Type,
				Unit:   DeviceResourceInfo.CPU.Unit,
			},
			Gpu: &devicemanager.ResourceDef{
				Amount: DeviceResourceInfo.GPU.Amount,
				Type:   DeviceResourceInfo.GPU.Type,
				Unit:   DeviceResourceInfo.GPU.Unit,
			},
			Disk: &devicemanager.ResourceDef{
				Amount: DeviceResourceInfo.Disk.Amount,
				Type:   DeviceResourceInfo.Disk.Type,
				Unit:   DeviceResourceInfo.Disk.Unit,
			},
			Mem: &devicemanager.ResourceDef{
				Amount: DeviceResourceInfo.Mem.Amount,
				Type:   DeviceResourceInfo.Mem.Type,
				Unit:   DeviceResourceInfo.Mem.Unit,
			},
			Protocol: DeviceResourceInfo.Protocol,
		},
		DeviceWorkStatus: &devicemanager.DeviceWorkStatus{
			HealthStatus: DeviceWorkStatus.HealthStatus,
			Timestamp:    DeviceWorkStatus.Timestamp,
			WorkMode:     DeviceWorkStatus.WorkMode,
		},
	}, nil
}
