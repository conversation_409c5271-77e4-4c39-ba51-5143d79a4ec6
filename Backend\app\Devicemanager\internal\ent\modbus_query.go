// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ModbusQuery is the builder for querying Modbus entities.
type ModbusQuery struct {
	config
	ctx        *QueryContext
	order      []modbus.OrderOption
	inters     []Interceptor
	predicates []predicate.Modbus
	withDevice *DeviceQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ModbusQuery builder.
func (mq *ModbusQuery) Where(ps ...predicate.Modbus) *ModbusQuery {
	mq.predicates = append(mq.predicates, ps...)
	return mq
}

// Limit the number of records to be returned by this query.
func (mq *ModbusQuery) Limit(limit int) *ModbusQuery {
	mq.ctx.Limit = &limit
	return mq
}

// Offset to start from.
func (mq *ModbusQuery) Offset(offset int) *ModbusQuery {
	mq.ctx.Offset = &offset
	return mq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (mq *ModbusQuery) Unique(unique bool) *ModbusQuery {
	mq.ctx.Unique = &unique
	return mq
}

// Order specifies how the records should be ordered.
func (mq *ModbusQuery) Order(o ...modbus.OrderOption) *ModbusQuery {
	mq.order = append(mq.order, o...)
	return mq
}

// QueryDevice chains the current query on the "device" edge.
func (mq *ModbusQuery) QueryDevice() *DeviceQuery {
	query := (&DeviceClient{config: mq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := mq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := mq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(modbus.Table, modbus.FieldID, selector),
			sqlgraph.To(device.Table, device.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, modbus.DeviceTable, modbus.DeviceColumn),
		)
		fromU = sqlgraph.SetNeighbors(mq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Modbus entity from the query.
// Returns a *NotFoundError when no Modbus was found.
func (mq *ModbusQuery) First(ctx context.Context) (*Modbus, error) {
	nodes, err := mq.Limit(1).All(setContextOp(ctx, mq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{modbus.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (mq *ModbusQuery) FirstX(ctx context.Context) *Modbus {
	node, err := mq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Modbus ID from the query.
// Returns a *NotFoundError when no Modbus ID was found.
func (mq *ModbusQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = mq.Limit(1).IDs(setContextOp(ctx, mq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{modbus.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (mq *ModbusQuery) FirstIDX(ctx context.Context) string {
	id, err := mq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Modbus entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Modbus entity is found.
// Returns a *NotFoundError when no Modbus entities are found.
func (mq *ModbusQuery) Only(ctx context.Context) (*Modbus, error) {
	nodes, err := mq.Limit(2).All(setContextOp(ctx, mq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{modbus.Label}
	default:
		return nil, &NotSingularError{modbus.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (mq *ModbusQuery) OnlyX(ctx context.Context) *Modbus {
	node, err := mq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Modbus ID in the query.
// Returns a *NotSingularError when more than one Modbus ID is found.
// Returns a *NotFoundError when no entities are found.
func (mq *ModbusQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = mq.Limit(2).IDs(setContextOp(ctx, mq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{modbus.Label}
	default:
		err = &NotSingularError{modbus.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (mq *ModbusQuery) OnlyIDX(ctx context.Context) string {
	id, err := mq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Modbuses.
func (mq *ModbusQuery) All(ctx context.Context) ([]*Modbus, error) {
	ctx = setContextOp(ctx, mq.ctx, ent.OpQueryAll)
	if err := mq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Modbus, *ModbusQuery]()
	return withInterceptors[[]*Modbus](ctx, mq, qr, mq.inters)
}

// AllX is like All, but panics if an error occurs.
func (mq *ModbusQuery) AllX(ctx context.Context) []*Modbus {
	nodes, err := mq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Modbus IDs.
func (mq *ModbusQuery) IDs(ctx context.Context) (ids []string, err error) {
	if mq.ctx.Unique == nil && mq.path != nil {
		mq.Unique(true)
	}
	ctx = setContextOp(ctx, mq.ctx, ent.OpQueryIDs)
	if err = mq.Select(modbus.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (mq *ModbusQuery) IDsX(ctx context.Context) []string {
	ids, err := mq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (mq *ModbusQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, mq.ctx, ent.OpQueryCount)
	if err := mq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, mq, querierCount[*ModbusQuery](), mq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (mq *ModbusQuery) CountX(ctx context.Context) int {
	count, err := mq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (mq *ModbusQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, mq.ctx, ent.OpQueryExist)
	switch _, err := mq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (mq *ModbusQuery) ExistX(ctx context.Context) bool {
	exist, err := mq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ModbusQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (mq *ModbusQuery) Clone() *ModbusQuery {
	if mq == nil {
		return nil
	}
	return &ModbusQuery{
		config:     mq.config,
		ctx:        mq.ctx.Clone(),
		order:      append([]modbus.OrderOption{}, mq.order...),
		inters:     append([]Interceptor{}, mq.inters...),
		predicates: append([]predicate.Modbus{}, mq.predicates...),
		withDevice: mq.withDevice.Clone(),
		// clone intermediate query.
		sql:  mq.sql.Clone(),
		path: mq.path,
	}
}

// WithDevice tells the query-builder to eager-load the nodes that are connected to
// the "device" edge. The optional arguments are used to configure the query builder of the edge.
func (mq *ModbusQuery) WithDevice(opts ...func(*DeviceQuery)) *ModbusQuery {
	query := (&DeviceClient{config: mq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	mq.withDevice = query
	return mq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		DeviceID string `json:"device_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Modbus.Query().
//		GroupBy(modbus.FieldDeviceID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (mq *ModbusQuery) GroupBy(field string, fields ...string) *ModbusGroupBy {
	mq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ModbusGroupBy{build: mq}
	grbuild.flds = &mq.ctx.Fields
	grbuild.label = modbus.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		DeviceID string `json:"device_id,omitempty"`
//	}
//
//	client.Modbus.Query().
//		Select(modbus.FieldDeviceID).
//		Scan(ctx, &v)
func (mq *ModbusQuery) Select(fields ...string) *ModbusSelect {
	mq.ctx.Fields = append(mq.ctx.Fields, fields...)
	sbuild := &ModbusSelect{ModbusQuery: mq}
	sbuild.label = modbus.Label
	sbuild.flds, sbuild.scan = &mq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ModbusSelect configured with the given aggregations.
func (mq *ModbusQuery) Aggregate(fns ...AggregateFunc) *ModbusSelect {
	return mq.Select().Aggregate(fns...)
}

func (mq *ModbusQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range mq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, mq); err != nil {
				return err
			}
		}
	}
	for _, f := range mq.ctx.Fields {
		if !modbus.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if mq.path != nil {
		prev, err := mq.path(ctx)
		if err != nil {
			return err
		}
		mq.sql = prev
	}
	return nil
}

func (mq *ModbusQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Modbus, error) {
	var (
		nodes       = []*Modbus{}
		_spec       = mq.querySpec()
		loadedTypes = [1]bool{
			mq.withDevice != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Modbus).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Modbus{config: mq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, mq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := mq.withDevice; query != nil {
		if err := mq.loadDevice(ctx, query, nodes, nil,
			func(n *Modbus, e *Device) { n.Edges.Device = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (mq *ModbusQuery) loadDevice(ctx context.Context, query *DeviceQuery, nodes []*Modbus, init func(*Modbus), assign func(*Modbus, *Device)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*Modbus)
	for i := range nodes {
		fk := nodes[i].DeviceID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(device.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "device_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (mq *ModbusQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := mq.querySpec()
	_spec.Node.Columns = mq.ctx.Fields
	if len(mq.ctx.Fields) > 0 {
		_spec.Unique = mq.ctx.Unique != nil && *mq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, mq.driver, _spec)
}

func (mq *ModbusQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(modbus.Table, modbus.Columns, sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString))
	_spec.From = mq.sql
	if unique := mq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if mq.path != nil {
		_spec.Unique = true
	}
	if fields := mq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, modbus.FieldID)
		for i := range fields {
			if fields[i] != modbus.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if mq.withDevice != nil {
			_spec.Node.AddColumnOnce(modbus.FieldDeviceID)
		}
	}
	if ps := mq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := mq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := mq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := mq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (mq *ModbusQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(mq.driver.Dialect())
	t1 := builder.Table(modbus.Table)
	columns := mq.ctx.Fields
	if len(columns) == 0 {
		columns = modbus.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if mq.sql != nil {
		selector = mq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if mq.ctx.Unique != nil && *mq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range mq.predicates {
		p(selector)
	}
	for _, p := range mq.order {
		p(selector)
	}
	if offset := mq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := mq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ModbusGroupBy is the group-by builder for Modbus entities.
type ModbusGroupBy struct {
	selector
	build *ModbusQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (mgb *ModbusGroupBy) Aggregate(fns ...AggregateFunc) *ModbusGroupBy {
	mgb.fns = append(mgb.fns, fns...)
	return mgb
}

// Scan applies the selector query and scans the result into the given value.
func (mgb *ModbusGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, mgb.build.ctx, ent.OpQueryGroupBy)
	if err := mgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ModbusQuery, *ModbusGroupBy](ctx, mgb.build, mgb, mgb.build.inters, v)
}

func (mgb *ModbusGroupBy) sqlScan(ctx context.Context, root *ModbusQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(mgb.fns))
	for _, fn := range mgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*mgb.flds)+len(mgb.fns))
		for _, f := range *mgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*mgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := mgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ModbusSelect is the builder for selecting fields of Modbus entities.
type ModbusSelect struct {
	*ModbusQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ms *ModbusSelect) Aggregate(fns ...AggregateFunc) *ModbusSelect {
	ms.fns = append(ms.fns, fns...)
	return ms
}

// Scan applies the selector query and scans the result into the given value.
func (ms *ModbusSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ms.ctx, ent.OpQuerySelect)
	if err := ms.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ModbusQuery, *ModbusSelect](ctx, ms.ModbusQuery, ms, ms.inters, v)
}

func (ms *ModbusSelect) sqlScan(ctx context.Context, root *ModbusQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ms.fns))
	for _, fn := range ms.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ms.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ms.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
