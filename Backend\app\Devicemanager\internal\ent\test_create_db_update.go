// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/test_create_db"
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCreateDbUpdate is the builder for updating Test_create_db entities.
type TestCreateDbUpdate struct {
	config
	hooks    []Hook
	mutation *TestCreateDbMutation
}

// Where appends a list predicates to the TestCreateDbUpdate builder.
func (tcdu *TestCreateDbUpdate) Where(ps ...predicate.Test_create_db) *TestCreateDbUpdate {
	tcdu.mutation.Where(ps...)
	return tcdu
}

// Mutation returns the TestCreateDbMutation object of the builder.
func (tcdu *TestCreateDbUpdate) Mutation() *TestCreateDbMutation {
	return tcdu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tcdu *TestCreateDbUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tcdu.sqlSave, tcdu.mutation, tcdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tcdu *TestCreateDbUpdate) SaveX(ctx context.Context) int {
	affected, err := tcdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tcdu *TestCreateDbUpdate) Exec(ctx context.Context) error {
	_, err := tcdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcdu *TestCreateDbUpdate) ExecX(ctx context.Context) {
	if err := tcdu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (tcdu *TestCreateDbUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(test_create_db.Table, test_create_db.Columns, sqlgraph.NewFieldSpec(test_create_db.FieldID, field.TypeInt))
	if ps := tcdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tcdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{test_create_db.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tcdu.mutation.done = true
	return n, nil
}

// TestCreateDbUpdateOne is the builder for updating a single Test_create_db entity.
type TestCreateDbUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TestCreateDbMutation
}

// Mutation returns the TestCreateDbMutation object of the builder.
func (tcduo *TestCreateDbUpdateOne) Mutation() *TestCreateDbMutation {
	return tcduo.mutation
}

// Where appends a list predicates to the TestCreateDbUpdate builder.
func (tcduo *TestCreateDbUpdateOne) Where(ps ...predicate.Test_create_db) *TestCreateDbUpdateOne {
	tcduo.mutation.Where(ps...)
	return tcduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tcduo *TestCreateDbUpdateOne) Select(field string, fields ...string) *TestCreateDbUpdateOne {
	tcduo.fields = append([]string{field}, fields...)
	return tcduo
}

// Save executes the query and returns the updated Test_create_db entity.
func (tcduo *TestCreateDbUpdateOne) Save(ctx context.Context) (*Test_create_db, error) {
	return withHooks(ctx, tcduo.sqlSave, tcduo.mutation, tcduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tcduo *TestCreateDbUpdateOne) SaveX(ctx context.Context) *Test_create_db {
	node, err := tcduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tcduo *TestCreateDbUpdateOne) Exec(ctx context.Context) error {
	_, err := tcduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcduo *TestCreateDbUpdateOne) ExecX(ctx context.Context) {
	if err := tcduo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (tcduo *TestCreateDbUpdateOne) sqlSave(ctx context.Context) (_node *Test_create_db, err error) {
	_spec := sqlgraph.NewUpdateSpec(test_create_db.Table, test_create_db.Columns, sqlgraph.NewFieldSpec(test_create_db.FieldID, field.TypeInt))
	id, ok := tcduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Test_create_db.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tcduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, test_create_db.FieldID)
		for _, f := range fields {
			if !test_create_db.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != test_create_db.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tcduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	_node = &Test_create_db{config: tcduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tcduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{test_create_db.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tcduo.mutation.done = true
	return _node, nil
}
