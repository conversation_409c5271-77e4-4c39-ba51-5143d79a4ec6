package logic

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteInstanceDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteInstanceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteInstanceDataLogic {
	return &DeleteInstanceDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteInstanceDataLogic) DeleteInstanceData(in *datamanager.DeleteInstanceDataRequest) (*datamanager.DeleteInstanceDataResponse, error) {
	// todo: add your logic here and delete this line
	return l.svcCtx.UtilDataService.DeleteInstanceData(l.ctx, in)
}
