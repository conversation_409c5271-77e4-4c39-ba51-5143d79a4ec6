syntax = "proto3";
import "google/protobuf/timestamp.proto";

package modelmanager;
option go_package="./modelmanager";

message ModelInfo{
    string model_name_version = 1;
    string framework_version = 2;
    string type_desc = 3;
    string storage_path = 4;
    string created_by = 5;
}

message ModelMetricsInfo{
    double accuracy = 1;
    double precision = 2;
    double recall = 3;
    double f1_score = 4;
}

message ModelTimeStampInfo{
    google.protobuf.Timestamp ts = 1;
}

message ModelMetaData{
    string model_id = 1;
    string model_name_version = 2;
    string framework_version = 3;
    string type_desc = 4;
    string storage_path = 5;
    double accuracy = 6;
    double precision = 7;
    double recall = 8;
    double f1_score = 9;
    string created_by = 10;
    google.protobuf.Timestamp created_ts = 11;
    google.protobuf.Timestamp updated_ts = 12;
}


message CreateModelConfigRequest{
    ModelInfo model_info = 1;
    ModelMetricsInfo model_metrics_info = 2;
    bytes trained_model = 3;
}

message CreateModelConfigResponse{
    ModelMetaData model_meta_data = 1;
    string status = 2;
}

message GetTrainedModelRequest{
    string model_id = 1;
}
message GetTrainedModelResponse{
    bytes trained_model = 1;
    string status = 2;
}

service Model{
    rpc CreateModelConfig(CreateModelConfigRequest) returns (CreateModelConfigResponse);
    rpc GetTrainedModel(GetTrainedModelRequest) returns (GetTrainedModelResponse);
}