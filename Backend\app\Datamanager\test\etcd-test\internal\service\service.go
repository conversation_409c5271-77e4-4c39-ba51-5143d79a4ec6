package service

import (
	"context"
	"etcd-test/pkg/datamodel"
	"etcd-test/pkg/etcdclient"
	"log"
)

// DataService handles the business logic of writing data to etcd.
// DataService 处理将数据写入 etcd 的业务逻辑。
type DataService struct {
	EtcdClient *etcdclient.EtcdClient
}

// NewDataService creates a new DataService.
// NewDataService 创建一个新的 DataService。
func NewDataService(etcdClient *etcdclient.EtcdClient) *DataService {
	return &DataService{EtcdClient: etcdClient}
}

// WriteInitialData writes the initial data to etcd.
// WriteInitialData 将初始数据写入 etcd。
func (s *DataService) WriteInitialData(ctx context.Context) {
	// --- 1. 处理 /driver 数据 ---
	motorUIDs := []string{"12345", "10021"}
	for _, motorUID := range motorUIDs {
		s.writeDriverData(ctx, motorUID)
	}

	// --- 2. 处理 /watch_list 数据 ---
	var watchListPaths []string
	for _, motorUID := range motorUIDs {
		watchListPaths = append(watchListPaths, "/driver/motor_"+motorUID)
	}
	watchList := datamodel.WatchList{
		WatchList: watchListPaths,
	}

	// 将 watch_list 数据写入 etcd
	err := s.EtcdClient.StoreData(ctx, "/watch_list", watchList)
	if err != nil {
		log.Fatalf("Failed to store watch list: %v", err)
	}
}

func (s *DataService) writeDriverData(ctx context.Context, motorUID string) {
	// 写入驱动基本信息
	driverInfo := datamodel.DriverInfo{
		UID:   motorUID,
		Name:  "motor",
		Uname: "motor_" + motorUID,
	}
	err := s.EtcdClient.StoreData(ctx, "/driver/motor_"+motorUID, driverInfo)
	if err != nil {
		log.Fatalf("Failed to store driver info for %s: %v", motorUID, err)
	}

	// 定义 producer 数据
	dataProducer := datamodel.DataProducer{
		Uname: "motor_" + motorUID,
		UID:   motorUID,
		Name:  "motor",
		DataGroups: []datamodel.DataGroup{
			{
				Name: "motor_curr_loop",
				DataNode: []datamodel.DataNode{
					{Name: "current", Unit: "A", Desc: "电机电流", Owner: "motor"},
					{Name: "voltage", Unit: "V", Desc: "电机电压", Owner: "motor"},
				},
				Frequency: "1ms",
				Desc:      "电机一个电流环控制周期内采集的数据",
			},
			{
				Name: "motor_speed_loop",
				DataNode: []datamodel.DataNode{
					{Name: "speed", Unit: "RPM", Desc: "电机转速", Owner: "motor"},
					{Name: "temp", Unit: "℃", Desc: "电机温度", Owner: "motor"},
				},
				Frequency: "1s",
				Desc:      "电机一个转速环控制周期内采集的数据",
			},
		},
	}

	// 定义 instances wrapper with unique UIDs
	instancesWrapper := datamodel.InstancesWrapper{
		Instances: []datamodel.Instance{
			{Uname: "motor1-" + motorUID + "abc", Name: "motor1", UID: motorUID + "abc", Hostname: "motor1-host", IP: "************"},
			{Uname: "motor2-" + motorUID + "def", Name: "motor2", UID: motorUID + "def", Hostname: "motor2-host", IP: "************"},
		},
	}

	// 将 producer 数据写入 etcd
	err = s.EtcdClient.StoreData(ctx, "/driver/motor_"+motorUID+"/producer", dataProducer)
	if err != nil {
		log.Fatalf("Failed to store driver producer for %s: %v", motorUID, err)
	}

	// 将 instances 数据写入 etcd
	err = s.EtcdClient.StoreData(ctx, "/driver/motor_"+motorUID+"/instances", instancesWrapper)
	if err != nil {
		log.Fatalf("Failed to store driver instances for %s: %v", motorUID, err)
	}
}
