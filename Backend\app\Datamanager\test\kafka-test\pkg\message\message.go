package message

import (
	"encoding/json"
	"time"
)

// MetaData 消息元数据
type MetaData struct {
	TimeStamp string `json:"TimeStamp"`
	Instance  string `json:"Instance"`
	DataGroup string `json:"DataGroup"`
	Revision  int64  `json:"Revision"`
}

// Message 完整的消息结构
type Message struct {
	MetaData MetaData    `json:"MetaData"`
	Data     interface{} `json:"Data"`
}

// NewMessage 创建新消息
func NewMessage(instanceName, dataGroupName string, revision int64, data interface{}) *Message {
	return &Message{
		MetaData: MetaData{
			TimeStamp: time.Now().Format(time.RFC3339Nano),
			Instance:  instanceName,
			DataGroup: dataGroupName,
			Revision:  revision,
		},
		Data: data,
	}
}

// ToJSON 将消息转换为JSON字符串
func (m *Message) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}
