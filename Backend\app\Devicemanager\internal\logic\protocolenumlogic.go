package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProtocolEnumLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewProtocolEnumLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProtocolEnumLogic {
	return &ProtocolEnumLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ProtocolEnumLogic) ProtocolEnum(req *types.ProtocolTypeEnumRequest) (resp *types.ProtocolTypeEnumResponse, err error) {
	//遍历utils包中的设备类型常量,命名都是 FrameType
	frameTypeEnum := []string{
		utils.FrameTypeModbus,
		utils.FrameTypeUart,
		utils.FrameTypeUdp,
	}

	datas := []types.DataDefForEnum{
		{Index: "", Name: "", Type: "", Unit: "", Desc: ""},
	}

	return &types.ProtocolTypeEnumResponse{
		ProtocolTypeEnum: frameTypeEnum,
		Modbus: types.SubItemModbusInfo{
			TID: "", PID: "", Len: "", UID: "", FC: "",
			Datas: datas,
		},
		Uart: types.SubItemUartInfo{
			Header: "", Addr: "", Cmd: "", Tail: "",
			Datas: datas,
		},
		Udp: types.SubItemUdpInfo{
			Type: "", Header: "", TypeID: "",
			Datas: datas,
		},
	}, nil

}
