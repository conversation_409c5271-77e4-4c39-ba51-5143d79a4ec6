package logic

import (
	"context"

	"GCF/app/Modelmanager/internal/logic/utils"
	"GCF/app/Modelmanager/internal/modelmanager"
	"GCF/app/Modelmanager/internal/svc"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type CreateModelConfigLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateModelConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateModelConfigLogic {
	return &CreateModelConfigLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateModelConfigLogic) CreateModelConfig(in *modelmanager.CreateModelConfigRequest) (*modelmanager.CreateModelConfigResponse, error) {

	//生成model_id
	model_id := uuid.New().String()

	//创建事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, err
	}

	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()

	ts := utils.CreateTimeStamp(model_id)
	storagepath := utils.CreateStoragePath(in.ModelInfo, model_id, ts)

	//返回的模型元信息
	ModelMetaData := &modelmanager.ModelMetaData{
		ModelId:          model_id,
		ModelNameVersion: in.ModelInfo.ModelNameVersion,
		FrameworkVersion: in.ModelInfo.FrameworkVersion,
		TypeDesc:         in.ModelInfo.TypeDesc,
		StoragePath:      storagepath,
		Accuracy:         in.ModelMetricsInfo.Accuracy,
		Precision:        in.ModelMetricsInfo.Precision,
		Recall:           in.ModelMetricsInfo.Recall,
		F1Score:          in.ModelMetricsInfo.F1Score,
		CreatedBy:        in.ModelInfo.CreatedBy,
		CreatedTs:        timestamppb.New(ts),
		UpdatedTs:        timestamppb.New(ts),
	}

	_, err = tx.Model.Create().
		SetModelID(model_id).
		SetModelNameVersion(in.ModelInfo.ModelNameVersion).
		SetFrameworkVersion(in.ModelInfo.FrameworkVersion).
		SetTypeDesc(in.ModelInfo.TypeDesc).
		SetStoragePath(storagepath).
		SetAccuracy(in.ModelMetricsInfo.Accuracy).
		SetPrecision(in.ModelMetricsInfo.Precision).
		SetRecall(in.ModelMetricsInfo.Recall).
		SetF1Score(in.ModelMetricsInfo.F1Score).
		SetCreatedBy(in.ModelInfo.CreatedBy).
		SetCreateUnix(ts).
		SetUpdateUnix(ts).
		Save(l.ctx)

	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			return nil, err_rollback
		}
		return nil, err
	}

	if commitErr := tx.Commit(); commitErr != nil {
		return nil, commitErr
	}

	return &modelmanager.CreateModelConfigResponse{
		ModelMetaData: ModelMetaData,
	}, nil
}
