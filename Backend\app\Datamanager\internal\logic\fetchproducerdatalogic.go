package logic

import (
	"context"
	"errors"
	"net/http"

	xerrors "github.com/zeromicro/x/errors"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type FetchProducerDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFetchProducerDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FetchProducerDataLogic {
	return &FetchProducerDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FetchProducerDataLogic) FetchProducerData(req *types.FetchProducerDataRequest) (resp *types.FetchProducerDataResponse, err error) {
	rpcReq := &datamanager.GetProducerDataRequest{
		TableInfo:     convertTableInfo(req.TableInfo),
		Revision:      req.Revision,
		DataRowLimit:  req.DataRowLimit,
		WhereInfos:    convertWhereInfos(req.WhereInfos),
		DatagroupInfo: convertDataGroupInfo(req.DataGroupInfo),
		InstanceInfo:  convertInstanceInfo(req.InstanceInfo),
		FieldsInfo:    convertFieldsInfo(req.FieldsInfo),
	}

	rpcResp, err := l.svcCtx.UtilDataService.GetProducerData(l.ctx, rpcReq)
	if err != nil {
		Msg := xerrors.New(http.StatusBadRequest, err.Error())
		return nil, errors.New(Msg.Error())
	}

	dataRows := convertDataRows(rpcResp.DataRows)
	
	offset := (req.Page - 1) * req.PageSize

	var pagedDataRows []types.DataRow
	if offset < int64(len(dataRows)) {
		end := offset + req.PageSize
		if end > int64(len(dataRows)) {
			end = int64(len(dataRows))
		}
		pagedDataRows = dataRows[offset:end]
	} else {
		pagedDataRows = []types.DataRow{}
	}

	// 构建响应
	resp = &types.FetchProducerDataResponse{
		List:     pagedDataRows,
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    rpcResp.DataCount,
	}

	return resp, nil
}
