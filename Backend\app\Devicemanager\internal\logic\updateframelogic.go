package logic

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"GCF/app/Devicemanager/internal/ent"
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type UpdateFrameLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateFrameLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateFrameLogic {
	return &UpdateFrameLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 只更新已有的帧，不新增协议
func (l *UpdateFrameLogic) UpdateFrame(req *types.UpdateFrameRequest) (resp *types.UpdateFrameResponse, err error) {
	//检验必要项
	if req.DeviceUID == "" || req.FrameInfos == nil {
		return nil, xerrors.New(http.StatusBadRequest, "未输入必填字段")
	}

	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备失败 : %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	//TODO: 根据状态决定是否可以编辑
	if DeviceWorkStatus.HealthStatus == utils.HealthStatusRunning {
		Msg := fmt.Errorf("设备仍在运行，请先停止设备")
		return nil, xerrors.New(http.StatusForbidden, Msg.Error())
	}

	//获取帧info数据和deviceMeta数据
	Protocols := strings.Split(queryDevice.Protocol, ",")
	DeviceMeta, _, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, Protocols, queryDevice.ID)
	if err != nil {
		return nil, xerrors.New(http.StatusNotFound, "帧info数据和deviceMeta数据获取失败")
	}
	// 3. 开事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, xerrors.New(http.StatusForbidden, "更新设备帧事务启动失败")
	}
	defer func() {
		if v := recover(); v != nil {
			_ = tx.Rollback()
			panic(v)
		}
	}()

	// 4. 遍历要更新的帧
	for _, fi := range req.FrameInfos {
		FrameMeta := &types.FrameMeta{
			FrameType: fi.FrameMeta.FrameType,
		}
		Protocols = utils.AddProtocol(Protocols, fi.FrameMeta.FrameType)
		_, err = tx.Device.Update().
			SetProtocol(strings.Join(Protocols, ",")).
			Save(l.ctx)
		if err != nil {
			Msg := fmt.Errorf("设备更新失败: %w", err)
			return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
		}
		FrameMeta.FrameDescription = fi.FrameMeta.FrameDescription
		FrameMeta.FrameUID = fi.FrameMeta.FrameUID
		FrameMeta.FrameName = fi.FrameMeta.FrameName
		//添加FrameMeta到DeviceMeta
		DeviceMeta.FrameMetas = append(DeviceMeta.FrameMetas, *FrameMeta)

		switch fi.FrameMeta.FrameType {
		case utils.FrameTypeModbus:
			// 1. 先确认数据库里有这条记录且确实是 Modbus
			exists, err := tx.Modbus.Query().
				Where(modbus.IDEQ(fi.FrameMeta.FrameUID)).
				Exist(l.ctx)
			switch {
			case err != nil:
				_ = tx.Rollback()
				Msg := fmt.Errorf("Modbus帧 %s 数据查询失败 : %w", fi.FrameMeta.FrameUID, err)
				return nil, xerrors.New(http.StatusBadRequest, Msg.Error())
			case !exists:
				_ = tx.Rollback()
				Msg := fmt.Errorf("Modbus帧 %s 不存在", fi.FrameMeta.FrameUID)
				return nil, xerrors.New(http.StatusNotFound, Msg.Error())
			}

			// 2. 真正执行更新

			if err := tx.Modbus.Update().
				Where(modbus.IDEQ(fi.FrameMeta.FrameUID)).
				SetName(fi.FrameMeta.FrameName).
				SetTid(fi.FrameLibs.ModbusInfo.TID).
				SetPid(fi.FrameLibs.ModbusInfo.PID).
				SetLen(fi.FrameLibs.ModbusInfo.Len).
				SetUID(fi.FrameLibs.ModbusInfo.UID).
				SetFc(fi.FrameLibs.ModbusInfo.FC).
				SetDescription(fi.FrameMeta.FrameDescription).
				Exec(l.ctx); err != nil {
				_ = tx.Rollback()
				Msg := fmt.Errorf("更新Modbus帧 %s 失败: %v", fi.FrameMeta.FrameUID, err)
				return nil, xerrors.New(http.StatusInternalServerError, Msg.Error())
			}
			err = UpdateData(l.ctx, tx, fi.FrameMeta, fi.FrameLibs.ModbusInfo.Datas)
			if err != nil {
				_ = tx.Rollback()
				Msg := fmt.Errorf("更新Modbus.Data数据结构体 失败: %w", err)
				return nil, xerrors.New(http.StatusInternalServerError, Msg.Error())
			}

		case utils.FrameTypeUart:
			exists, err := tx.Uart.Query().
				Where(uart.IDEQ(fi.FrameMeta.FrameUID)).
				Exist(l.ctx)
			switch {
			case err != nil:
				_ = tx.Rollback()
				Msg := fmt.Errorf("Uart帧 %s 数据查询失败 : %w", fi.FrameMeta.FrameUID, err)
				return nil, xerrors.New(http.StatusBadRequest, Msg.Error())
			case !exists:
				_ = tx.Rollback()
				Msg := fmt.Errorf("Uart帧 %s 不存在", fi.FrameMeta.FrameUID)
				return nil, xerrors.New(http.StatusNotFound, Msg.Error())
			}

			if err := tx.Uart.Update().
				Where(uart.IDEQ(fi.FrameMeta.FrameUID)).
				SetName(fi.FrameMeta.FrameName).
				SetHeader(fi.FrameLibs.UartInfo.Header).
				SetAddr(fi.FrameLibs.UartInfo.Addr).
				SetCmd(fi.FrameLibs.UartInfo.Cmd).
				SetTail(fi.FrameLibs.UartInfo.Tail).
				SetDescription(fi.FrameMeta.FrameDescription).
				Exec(l.ctx); err != nil {
				_ = tx.Rollback()
				Msg := fmt.Errorf("更新Uart帧 %s 失败: %v", fi.FrameMeta.FrameUID, err)
				return nil, xerrors.New(http.StatusInternalServerError, Msg.Error())
			}

			err = UpdateData(l.ctx, tx, fi.FrameMeta, fi.FrameLibs.UartInfo.Datas)
			if err != nil {
				_ = tx.Rollback()
				Msg := fmt.Errorf("更新Uart.Data数据结构体 失败: %w", err)
				return nil, xerrors.New(http.StatusInternalServerError, Msg.Error())
			}

		case utils.FrameTypeUdp:
			exists, err := tx.Udp.Query().
				Where(udp.IDEQ(fi.FrameMeta.FrameUID)).
				Exist(l.ctx)
			switch {
			case err != nil:
				_ = tx.Rollback()
				Msg := fmt.Errorf("Udp帧 %s 数据查询失败 : %w", fi.FrameMeta.FrameUID, err)
				return nil, xerrors.New(http.StatusBadRequest, Msg.Error())
			case !exists:
				_ = tx.Rollback()
				Msg := fmt.Errorf("Udp帧 %s 不存在", fi.FrameMeta.FrameUID)
				return nil, xerrors.New(http.StatusNotFound, Msg.Error())
			}

			if err := tx.Udp.Update().
				Where(udp.IDEQ(fi.FrameMeta.FrameUID)).
				SetName(fi.FrameMeta.FrameName).
				SetType(fi.FrameLibs.UdpInfo.Type).
				SetHeader(fi.FrameLibs.UdpInfo.Header).
				SetTypeID(fi.FrameLibs.UdpInfo.TypeID).
				SetDescription(fi.FrameMeta.FrameDescription).
				Exec(l.ctx); err != nil {
				_ = tx.Rollback()
				Msg := fmt.Errorf("更新Udp帧 %s 失败: %v", fi.FrameMeta.FrameUID, err)
				return nil, xerrors.New(http.StatusInternalServerError, Msg.Error())
			}

			err = UpdateData(l.ctx, tx, fi.FrameMeta, fi.FrameLibs.UdpInfo.Datas)
			if err != nil {
				_ = tx.Rollback()
				Msg := fmt.Errorf("更新Udp.Data数据结构体 失败: %w", err)
				return nil, xerrors.New(http.StatusInternalServerError, Msg.Error())
			}

		default:
			_ = tx.Rollback()
			Msg := fmt.Errorf("不支持的帧类型： %s", fi.FrameMeta.FrameType)
			return nil, xerrors.New(http.StatusBadRequest, Msg.Error())
		}
	}
	// 5. 提交事务
	if err := tx.Commit(); err != nil {
		Msg := fmt.Errorf("更新设备帧请求提交失败: %w", err)
		return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
	}
	//更新设备信息成功，返回数据
	updatedDeviceMeta, _, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, Protocols, queryDevice.ID)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "更新后获取DeviceMeta失败")
	}

	updatedDeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "更新后获取DeviceWorkStatus失败")
	}

	// 添加到Etcd
	err = utils.Pub2Etcd(l.ctx, l.svcCtx)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "failed to publish to Etcd: "+err.Error())
	}

	return &types.UpdateFrameResponse{
		DeviceMeta:       *updatedDeviceMeta,
		DeviceWorkStatus: *updatedDeviceWorkStatus,
	}, nil
}

func UpdateData(ctx context.Context, tx *ent.Tx, FrameMeta types.FrameMeta, datas []types.DataDef) error {
	// 1. 删除该帧之前所有的 Data
	if _, err := tx.Data.Delete().
		Where(data.FrameIDEQ(FrameMeta.FrameUID)).
		Exec(ctx); err != nil {
		_ = tx.Rollback()
		Msg := fmt.Errorf("原始数据重置失败: %w", err)
		return xerrors.New(http.StatusInternalServerError, Msg.Error())
	}

	// 2. 重新插入新的 DataDef
	for _, dataDef := range datas {
		dataUID := uuid.New().String()
		_, err := tx.Data.Create().
			SetID(dataUID).
			SetFrameID(FrameMeta.FrameUID).
			SetIndex(dataDef.Index).
			SetName(dataDef.Name).
			SetType(dataDef.Type).
			SetUnit(dataDef.Unit).
			SetDescription(dataDef.Desc).
			Save(ctx)
		if err != nil {
			_ = tx.Rollback()
			Msg := fmt.Errorf("新数据覆盖失败: %s", err.Error())
			return xerrors.New(http.StatusInternalServerError, Msg.Error())
		}
	}

	return nil
}
