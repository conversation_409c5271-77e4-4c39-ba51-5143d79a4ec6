// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.3
// source: rpc/model.proto

package modelmanager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Model_CreateModelConfig_FullMethodName = "/modelmanager.Model/CreateModelConfig"
	Model_GetTrainedModel_FullMethodName   = "/modelmanager.Model/GetTrainedModel"
)

// ModelClient is the client API for Model service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModelClient interface {
	CreateModelConfig(ctx context.Context, in *CreateModelConfigRequest, opts ...grpc.CallOption) (*CreateModelConfigResponse, error)
	GetTrainedModel(ctx context.Context, in *GetTrainedModelRequest, opts ...grpc.CallOption) (*GetTrainedModelResponse, error)
}

type modelClient struct {
	cc grpc.ClientConnInterface
}

func NewModelClient(cc grpc.ClientConnInterface) ModelClient {
	return &modelClient{cc}
}

func (c *modelClient) CreateModelConfig(ctx context.Context, in *CreateModelConfigRequest, opts ...grpc.CallOption) (*CreateModelConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateModelConfigResponse)
	err := c.cc.Invoke(ctx, Model_CreateModelConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *modelClient) GetTrainedModel(ctx context.Context, in *GetTrainedModelRequest, opts ...grpc.CallOption) (*GetTrainedModelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTrainedModelResponse)
	err := c.cc.Invoke(ctx, Model_GetTrainedModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModelServer is the server API for Model service.
// All implementations must embed UnimplementedModelServer
// for forward compatibility.
type ModelServer interface {
	CreateModelConfig(context.Context, *CreateModelConfigRequest) (*CreateModelConfigResponse, error)
	GetTrainedModel(context.Context, *GetTrainedModelRequest) (*GetTrainedModelResponse, error)
	mustEmbedUnimplementedModelServer()
}

// UnimplementedModelServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedModelServer struct{}

func (UnimplementedModelServer) CreateModelConfig(context.Context, *CreateModelConfigRequest) (*CreateModelConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateModelConfig not implemented")
}
func (UnimplementedModelServer) GetTrainedModel(context.Context, *GetTrainedModelRequest) (*GetTrainedModelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTrainedModel not implemented")
}
func (UnimplementedModelServer) mustEmbedUnimplementedModelServer() {}
func (UnimplementedModelServer) testEmbeddedByValue()               {}

// UnsafeModelServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModelServer will
// result in compilation errors.
type UnsafeModelServer interface {
	mustEmbedUnimplementedModelServer()
}

func RegisterModelServer(s grpc.ServiceRegistrar, srv ModelServer) {
	// If the following call pancis, it indicates UnimplementedModelServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Model_ServiceDesc, srv)
}

func _Model_CreateModelConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateModelConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServer).CreateModelConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Model_CreateModelConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServer).CreateModelConfig(ctx, req.(*CreateModelConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Model_GetTrainedModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrainedModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModelServer).GetTrainedModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Model_GetTrainedModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModelServer).GetTrainedModel(ctx, req.(*GetTrainedModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Model_ServiceDesc is the grpc.ServiceDesc for Model service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Model_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "modelmanager.Model",
	HandlerType: (*ModelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateModelConfig",
			Handler:    _Model_CreateModelConfig_Handler,
		},
		{
			MethodName: "GetTrainedModel",
			Handler:    _Model_GetTrainedModel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/model.proto",
}
