package logic

import (
	"context"

	"GCF/app/Modelmanager/internal/modelmanager"
	"GCF/app/Modelmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTrainedModelLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetTrainedModelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTrainedModelLogic {
	return &GetTrainedModelLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetTrainedModelLogic) GetTrainedModel(in *modelmanager.GetTrainedModelRequest) (*modelmanager.GetTrainedModelResponse, error) {
	// todo: add your logic here and delete this line

	return &modelmanager.GetTrainedModelResponse{}, nil
}
