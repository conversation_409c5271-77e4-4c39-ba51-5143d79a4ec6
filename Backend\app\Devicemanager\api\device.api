syntax = "v1"

info (
	title:  "device Service"
	desc:   "device service related api"
	author: ""
	email:  ""
)

// 数据结构体
type (
	DeviceResourceInfo {
		IP          string      `json:"ip"`
		Type        string      `json:"type,options=fan|motor|virtual|pc|edge"`
		Protocol    []string    `json:"protocol,options=udp|modbus|uart"`
		DeviceName  string      `json:"devicename,optional"`
		OS          string      `json:"os,optional"`
		CPU         ResourceDef `json:"cpu,optional"`
		GPU         ResourceDef `json:"gpu,optional"`
		Disk        ResourceDef `json:"disk,optional"`
		Mem         ResourceDef `json:"mem,optional"`
		Description string      `json:"description,optional"`
	}
	ResourceDef {
		Amount int64  `json:"amount"`
		Type   string `json:"type"`
		Unit   string `json:"unit"`
	}
	DeviceWorkStatus {
		HealthStatus string `json:"healthStatus,options=init|ready|running|pending|error"`
		Timestamp    int64  `json:"timestamp"`
		WorkMode     string `json:"workMode,options=distributed|centralized"`
	}
	DeviceMeta {
		DeviceUID   string      `json:"deviceUID"`
		FrameMetas  []FrameMeta `json:"frameMetas"`
		Description string      `json:"description,optional"`
		DeviceName  string      `json:"devicename,optional"`
	}
	FrameInfo {
		FrameMeta FrameMeta `json:"frameMeta"`
		FrameLibs FrameLibs `json:"frameLibs"`
	}
	FrameLibs {
		ModbusInfo ModbusInfo `json:"modbusInfo,optional"`
		UartInfo   UartInfo   `json:"uartInfo,optional"`
		UdpInfo    UdpInfo    `json:"udpInfo,optional"`
	}
	FrameMeta {
		FrameUID         string `json:"frameUID,optional"`
		FrameType        string `json:"frameType,options=modbus|uart|udp"`
		FrameDescription string `json:"framedescription,optional"`
		FrameName        string `json:"frameName"`
	}
	ModbusInfo {
		TID   string    `json:"tid"`
		PID   string    `json:"pid"`
		Len   string    `json:"len"`
		UID   string    `json:"uid"`
		FC    string    `json:"fc"`
		Datas []DataDef `json:"datas,optional"`
	}
	UartInfo {
		Header string    `json:"header"`
		Addr   string    `json:"addr"`
		Cmd    string    `json:"cmd"`
		Tail   string    `json:"tail"`
		Datas  []DataDef `json:"datas,optional"`
	}
	UdpInfo {
		Type   string    `json:"type"`
		Header string    `json:"header"`
		TypeID string    `json:"typeID"`
		Datas  []DataDef `json:"datas,optional"`
	}
	DataDef {
		Index string `json:"index"`
		Name  string `json:"name"`
		Type  string `json:"type"`
		Unit  string `json:"unit"`
		Value string `json:"value,optional"`
		Desc  string `json:"desc,optional"`
	}
	DataDefForEnum {
		Index string `json:"index"`
		Name  string `json:"name"`
		Type  string `json:"type"`
		Unit  string `json:"unit"`
		Desc  string `json:"desc,optional"`
	}
)

//响应-请求结构体
type (
	// 创建设备资源
	CreateDeviceResourceRequest {
		DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
	}
	CreateDeviceResourceResponse {
		DeviceMeta       DeviceMeta       `json:"deviceMeta"`
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	}
	// 删除
	DeleteDeviceResourceRequest {
		DeviceUID string `json:"deviceUID"`
	}
	DeleteDeviceResourceResponse {
		DeviceMeta         DeviceMeta         `json:"deviceMeta"`
		DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
		DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
	}
	// 创建设备帧
	CreateDeviceFrameRequest {
		DeviceUID  string      `json:"deviceUID"`
		FrameInfos []FrameInfo `json:"frameInfos"`
	}
	CreateDeviceFrameResponse {
		DeviceMeta       DeviceMeta       `json:"deviceMeta"`
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	}
	// 编辑帧信息
	UpdateFrameRequest {
		DeviceUID  string      `json:"deviceUID"`
		FrameInfos []FrameInfo `json:"frameInfos"`
	}
	UpdateFrameResponse {
		DeviceMeta       DeviceMeta       `json:"deviceMeta"`
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	}
	// 删除设备帧
	DeleteDeviceFrameRequest {
		DeviceUID   string      `json:"deviceUID"`
		FrameMetas  []FrameMeta `json:"frameMetas"`
		Description string      `json:"description,optional"` // 帧描述
	}
	DeleteDeviceFrameResponse {
		DeviceMeta       DeviceMeta       `json:"deviceMeta"`
		FrameInfos       []FrameInfo      `json:"frameInfos"`
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	}
	// 获取设备信息
	GetDeviceInfoRequest {
		DeviceUID string `json:"deviceUID"`
	}
	GetDeviceInfoResponse {
		DeviceMeta       DeviceMeta       `json:"deviceMeta"`
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	}
	// 列出设备
	ListDeviceRequest {
		DeviceUID    string      `json:"deviceUID,optional"`
		FrameMetas   []FrameMeta `json:"frameMetas,optional"`
		HealthStatus string      `json:"healthStatus,options=init|ready|running|pending|error,optional"`
		PageNum      int64       `json:"pageNum,optional"`
		PageSize     int64       `json:"pageSize,optional"`
	}
	ListDevice {
		DeviceMeta         DeviceMeta         `json:"deviceMeta"`
		DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
		DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
		FrameInfos         []FrameInfo        `json:"frameInfos"`
	}
	ListDeviceResponse {
		Devices  []ListDevice `json:"devices"`
		Total    int64        `json:"total"`
		PageNum  int64        `json:"pageNum"`
		PageSize int64        `json:"pageSize"`
	}
	// 控制设备
	ControlDeviceRequest {
		DeviceUID string    `json:"deviceUID"`
		FrameInfo FrameInfo `json:"frameInfo"`
	}
	ControlDeviceResponse {
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
		FrameInfos       []FrameInfo      `json:"frameInfo"`
	}
	// 获取设备数据
	FetchDeviceDataRequest {
		DeviceUID string `json:"deviceUID"`
	}
	FetchDeviceDataResponse {
		DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
		FrameInfos       []FrameInfo      `json:"frameInfos"`
	}
	// 获取帧信息
	GetFrameInfoRequest {
		DeviceUID  string      `json:"deviceUID"`
		FrameMetas []FrameMeta `json:"frameMetas"`
	}
	GetFrameInfoResponse {
		FrameInfos []FrameInfo `json:"frameInfos"`
	}
	//编辑设备资源
	UpdateDeviceResourceRequest {
		DeviceUID          string             `json:"deviceUID"`
		DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
	}
	UpdateDeviceResourceResponse {
		DeviceMeta         DeviceMeta         `json:"deviceMeta"`
		DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
		DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
		FrameInfos         []FrameInfo        `json:"frameInfos"`
	}
	//设备下拉框信息查询
	DeviceTypeEnumRequest  {}
	DeviceTypeEnumResponse {
		DeviceTypeEnum []string `json:"deviceTypeEnum"`
	}
	//协议下拉框信息查询
	ProtocolTypeEnumRequest  {}
	ProtocolTypeEnumResponse {
		Modbus           SubItemModbusInfo `json:"modbus"`
		Uart             SubItemUartInfo   `json:"uart"`
		Udp              SubItemUdpInfo    `json:"udp"`
		ProtocolTypeEnum []string          `json:"protocolTypeEnum"`
	}
	//子项
	SubItemModbusInfo {
		TID   string           `json:"tid"`
		PID   string           `json:"pid"`
		Len   string           `json:"len"`
		UID   string           `json:"uid"`
		FC    string           `json:"fc"`
		Datas []DataDefForEnum `json:"datas"`
	}
	SubItemUartInfo {
		Header string           `json:"header"`
		Addr   string           `json:"addr"`
		Cmd    string           `json:"cmd"`
		Tail   string           `json:"tail"`
		Datas  []DataDefForEnum `json:"datas"`
	}
	SubItemUdpInfo {
		Type   string           `json:"type"`
		Header string           `json:"header"`
		TypeID string           `json:"typeID"`
		Datas  []DataDefForEnum `json:"datas"`
	}
)

//api
service device-api {
	@handler CreateDeviceResourceHandler
	post /api/v1/device/resource/create (CreateDeviceResourceRequest) returns (CreateDeviceResourceResponse)

	@handler DeleteDeviceResourceHandler
	post /api/v1/device/resource/delete (DeleteDeviceResourceRequest) returns (DeleteDeviceResourceResponse)

	@handler CreateDeviceFrameHandler
	post /api/v1/device/frame/create (CreateDeviceFrameRequest) returns (CreateDeviceFrameResponse)

	@handler DeleteDeviceFrameHandler
	post /api/v1/device/frame/delete (DeleteDeviceFrameRequest) returns (DeleteDeviceFrameResponse)

	// 更新帧信息的API接口
	@handler UpdateFrameHandler
	post /api/v1/device/frame/update (UpdateFrameRequest) returns (UpdateFrameResponse)

	@handler ListDeviceHandler
	post /api/v1/device/device/list (ListDeviceRequest) returns (ListDeviceResponse)

	// 获取设备信息的API接口
	@handler GetDeviceInfoHandler
	post /api/v1/device/info/get (GetDeviceInfoRequest) returns (GetDeviceInfoResponse)

	// 获取帧信息的API接口
	@handler GetFrameInfoHandler
	post /api/v1/device/frame/info/get (GetFrameInfoRequest) returns (GetFrameInfoResponse)

	@handler UpdateDeviceResourceHandler
	post /api/v1/device/resource/update (UpdateDeviceResourceRequest) returns (UpdateDeviceResourceResponse)

	@handler ControlDeviceHandler
	post /api/v1/device/device/control (ControlDeviceRequest) returns (ControlDeviceResponse)

	@handler FetchDeviceDataHandler
	post /api/v1/device/data/fetch (FetchDeviceDataRequest) returns (FetchDeviceDataResponse)

	@handler DeviceEnumHandler
	post /api/v1/device/resource/enum (DeviceTypeEnumRequest) returns (DeviceTypeEnumResponse)

	@handler ProtocolEnumHandler
	post /api/v1/device/resource/protocol/enum (ProtocolTypeEnumRequest) returns (ProtocolTypeEnumResponse)
}

