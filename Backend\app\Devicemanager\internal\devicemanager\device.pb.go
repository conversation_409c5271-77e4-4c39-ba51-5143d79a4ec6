// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: rpc/device.proto

package devicemanager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFramesByDeviceIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFramesByDeviceIDRequest) Reset() {
	*x = GetFramesByDeviceIDRequest{}
	mi := &file_rpc_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFramesByDeviceIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFramesByDeviceIDRequest) ProtoMessage() {}

func (x *GetFramesByDeviceIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFramesByDeviceIDRequest.ProtoReflect.Descriptor instead.
func (*GetFramesByDeviceIDRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{0}
}

func (x *GetFramesByDeviceIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetFramesByDeviceIDResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Frames        []*FrameInfo           `protobuf:"bytes,1,rep,name=frames,proto3" json:"frames,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFramesByDeviceIDResponse) Reset() {
	*x = GetFramesByDeviceIDResponse{}
	mi := &file_rpc_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFramesByDeviceIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFramesByDeviceIDResponse) ProtoMessage() {}

func (x *GetFramesByDeviceIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFramesByDeviceIDResponse.ProtoReflect.Descriptor instead.
func (*GetFramesByDeviceIDResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{1}
}

func (x *GetFramesByDeviceIDResponse) GetFrames() []*FrameInfo {
	if x != nil {
		return x.Frames
	}
	return nil
}

type FrameInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FrameMeta     *FrameMeta             `protobuf:"bytes,1,opt,name=frame_meta,json=frameMeta,proto3" json:"frame_meta,omitempty"`
	FrameLib      *FrameLibs             `protobuf:"bytes,2,opt,name=frame_lib,json=frameLib,proto3" json:"frame_lib,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameInfo) Reset() {
	*x = FrameInfo{}
	mi := &file_rpc_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameInfo) ProtoMessage() {}

func (x *FrameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameInfo.ProtoReflect.Descriptor instead.
func (*FrameInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{2}
}

func (x *FrameInfo) GetFrameMeta() *FrameMeta {
	if x != nil {
		return x.FrameMeta
	}
	return nil
}

func (x *FrameInfo) GetFrameLib() *FrameLibs {
	if x != nil {
		return x.FrameLib
	}
	return nil
}

type FrameMeta struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FrameUid      string                 `protobuf:"bytes,1,opt,name=frame_uid,json=frameUid,proto3" json:"frame_uid,omitempty"`
	FrameType     string                 `protobuf:"bytes,2,opt,name=frame_type,json=frameType,proto3" json:"frame_type,omitempty"`
	FrameName     string                 `protobuf:"bytes,3,opt,name=frame_name,json=frameName,proto3" json:"frame_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameMeta) Reset() {
	*x = FrameMeta{}
	mi := &file_rpc_device_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameMeta) ProtoMessage() {}

func (x *FrameMeta) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameMeta.ProtoReflect.Descriptor instead.
func (*FrameMeta) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{3}
}

func (x *FrameMeta) GetFrameUid() string {
	if x != nil {
		return x.FrameUid
	}
	return ""
}

func (x *FrameMeta) GetFrameType() string {
	if x != nil {
		return x.FrameType
	}
	return ""
}

func (x *FrameMeta) GetFrameName() string {
	if x != nil {
		return x.FrameName
	}
	return ""
}

type FrameLibs struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModbusInfo    *ModbusInfo            `protobuf:"bytes,1,opt,name=modbus_info,json=modbusInfo,proto3" json:"modbus_info,omitempty"`
	UartInfo      *UartInfo              `protobuf:"bytes,2,opt,name=uart_info,json=uartInfo,proto3" json:"uart_info,omitempty"`
	UdpInfo       *UdpInfo               `protobuf:"bytes,3,opt,name=udp_info,json=udpInfo,proto3" json:"udp_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameLibs) Reset() {
	*x = FrameLibs{}
	mi := &file_rpc_device_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameLibs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameLibs) ProtoMessage() {}

func (x *FrameLibs) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameLibs.ProtoReflect.Descriptor instead.
func (*FrameLibs) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{4}
}

func (x *FrameLibs) GetModbusInfo() *ModbusInfo {
	if x != nil {
		return x.ModbusInfo
	}
	return nil
}

func (x *FrameLibs) GetUartInfo() *UartInfo {
	if x != nil {
		return x.UartInfo
	}
	return nil
}

func (x *FrameLibs) GetUdpInfo() *UdpInfo {
	if x != nil {
		return x.UdpInfo
	}
	return nil
}

type ModbusInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tid           string                 `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"`
	Pid           string                 `protobuf:"bytes,2,opt,name=pid,proto3" json:"pid,omitempty"`
	Len           string                 `protobuf:"bytes,3,opt,name=len,proto3" json:"len,omitempty"`
	Uid           string                 `protobuf:"bytes,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Fc            string                 `protobuf:"bytes,5,opt,name=fc,proto3" json:"fc,omitempty"`
	Datas         []*DataDefs            `protobuf:"bytes,6,rep,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModbusInfo) Reset() {
	*x = ModbusInfo{}
	mi := &file_rpc_device_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModbusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModbusInfo) ProtoMessage() {}

func (x *ModbusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModbusInfo.ProtoReflect.Descriptor instead.
func (*ModbusInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{5}
}

func (x *ModbusInfo) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *ModbusInfo) GetPid() string {
	if x != nil {
		return x.Pid
	}
	return ""
}

func (x *ModbusInfo) GetLen() string {
	if x != nil {
		return x.Len
	}
	return ""
}

func (x *ModbusInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ModbusInfo) GetFc() string {
	if x != nil {
		return x.Fc
	}
	return ""
}

func (x *ModbusInfo) GetDatas() []*DataDefs {
	if x != nil {
		return x.Datas
	}
	return nil
}

type UartInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        string                 `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Cmd           string                 `protobuf:"bytes,3,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Tail          string                 `protobuf:"bytes,4,opt,name=tail,proto3" json:"tail,omitempty"`
	Datas         []*DataDefs            `protobuf:"bytes,5,rep,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UartInfo) Reset() {
	*x = UartInfo{}
	mi := &file_rpc_device_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UartInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UartInfo) ProtoMessage() {}

func (x *UartInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UartInfo.ProtoReflect.Descriptor instead.
func (*UartInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{6}
}

func (x *UartInfo) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *UartInfo) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *UartInfo) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *UartInfo) GetTail() string {
	if x != nil {
		return x.Tail
	}
	return ""
}

func (x *UartInfo) GetDatas() []*DataDefs {
	if x != nil {
		return x.Datas
	}
	return nil
}

type UdpInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Header        string                 `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	TypeId        string                 `protobuf:"bytes,3,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`
	Datas         []*DataDefs            `protobuf:"bytes,4,rep,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UdpInfo) Reset() {
	*x = UdpInfo{}
	mi := &file_rpc_device_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UdpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UdpInfo) ProtoMessage() {}

func (x *UdpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UdpInfo.ProtoReflect.Descriptor instead.
func (*UdpInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{7}
}

func (x *UdpInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UdpInfo) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *UdpInfo) GetTypeId() string {
	if x != nil {
		return x.TypeId
	}
	return ""
}

func (x *UdpInfo) GetDatas() []*DataDefs {
	if x != nil {
		return x.Datas
	}
	return nil
}

type DataDefs struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Index         string                 `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Unit          string                 `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	Value         string                 `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
	Desc          string                 `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataDefs) Reset() {
	*x = DataDefs{}
	mi := &file_rpc_device_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataDefs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataDefs) ProtoMessage() {}

func (x *DataDefs) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataDefs.ProtoReflect.Descriptor instead.
func (*DataDefs) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{8}
}

func (x *DataDefs) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *DataDefs) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DataDefs) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DataDefs) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *DataDefs) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *DataDefs) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type GetDeviceByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceByIDRequest) Reset() {
	*x = GetDeviceByIDRequest{}
	mi := &file_rpc_device_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceByIDRequest) ProtoMessage() {}

func (x *GetDeviceByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceByIDRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceByIDRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{9}
}

func (x *GetDeviceByIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetDeviceByIDResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	DeviceResourceInfo *DeviceResourceInfo    `protobuf:"bytes,1,opt,name=device_resource_info,json=deviceResourceInfo,proto3" json:"device_resource_info,omitempty"`
	DeviceWorkStatus   *DeviceWorkStatus      `protobuf:"bytes,2,opt,name=device_work_status,json=deviceWorkStatus,proto3" json:"device_work_status,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetDeviceByIDResponse) Reset() {
	*x = GetDeviceByIDResponse{}
	mi := &file_rpc_device_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceByIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceByIDResponse) ProtoMessage() {}

func (x *GetDeviceByIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceByIDResponse.ProtoReflect.Descriptor instead.
func (*GetDeviceByIDResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{10}
}

func (x *GetDeviceByIDResponse) GetDeviceResourceInfo() *DeviceResourceInfo {
	if x != nil {
		return x.DeviceResourceInfo
	}
	return nil
}

func (x *GetDeviceByIDResponse) GetDeviceWorkStatus() *DeviceWorkStatus {
	if x != nil {
		return x.DeviceWorkStatus
	}
	return nil
}

type DeviceResourceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Os            string                 `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	Cpu           *ResourceDef           `protobuf:"bytes,5,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Gpu           *ResourceDef           `protobuf:"bytes,6,opt,name=gpu,proto3" json:"gpu,omitempty"`
	Disk          *ResourceDef           `protobuf:"bytes,7,opt,name=disk,proto3" json:"disk,omitempty"`
	Mem           *ResourceDef           `protobuf:"bytes,8,opt,name=mem,proto3" json:"mem,omitempty"`
	Protocol      []string               `protobuf:"bytes,9,rep,name=protocol,proto3" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceResourceInfo) Reset() {
	*x = DeviceResourceInfo{}
	mi := &file_rpc_device_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceResourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceResourceInfo) ProtoMessage() {}

func (x *DeviceResourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceResourceInfo.ProtoReflect.Descriptor instead.
func (*DeviceResourceInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{11}
}

func (x *DeviceResourceInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeviceResourceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeviceResourceInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeviceResourceInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *DeviceResourceInfo) GetCpu() *ResourceDef {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *DeviceResourceInfo) GetGpu() *ResourceDef {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *DeviceResourceInfo) GetDisk() *ResourceDef {
	if x != nil {
		return x.Disk
	}
	return nil
}

func (x *DeviceResourceInfo) GetMem() *ResourceDef {
	if x != nil {
		return x.Mem
	}
	return nil
}

func (x *DeviceResourceInfo) GetProtocol() []string {
	if x != nil {
		return x.Protocol
	}
	return nil
}

type ResourceDef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Amount        int64                  `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Unit          string                 `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceDef) Reset() {
	*x = ResourceDef{}
	mi := &file_rpc_device_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDef) ProtoMessage() {}

func (x *ResourceDef) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDef.ProtoReflect.Descriptor instead.
func (*ResourceDef) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{12}
}

func (x *ResourceDef) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ResourceDef) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResourceDef) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

type DeviceWorkStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HealthStatus  string                 `protobuf:"bytes,1,opt,name=health_status,json=healthStatus,proto3" json:"health_status,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	WorkMode      string                 `protobuf:"bytes,3,opt,name=work_mode,json=workMode,proto3" json:"work_mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceWorkStatus) Reset() {
	*x = DeviceWorkStatus{}
	mi := &file_rpc_device_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceWorkStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceWorkStatus) ProtoMessage() {}

func (x *DeviceWorkStatus) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceWorkStatus.ProtoReflect.Descriptor instead.
func (*DeviceWorkStatus) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{13}
}

func (x *DeviceWorkStatus) GetHealthStatus() string {
	if x != nil {
		return x.HealthStatus
	}
	return ""
}

func (x *DeviceWorkStatus) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DeviceWorkStatus) GetWorkMode() string {
	if x != nil {
		return x.WorkMode
	}
	return ""
}

type CheckDeviceHealthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDeviceHealthRequest) Reset() {
	*x = CheckDeviceHealthRequest{}
	mi := &file_rpc_device_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDeviceHealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDeviceHealthRequest) ProtoMessage() {}

func (x *CheckDeviceHealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDeviceHealthRequest.ProtoReflect.Descriptor instead.
func (*CheckDeviceHealthRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{14}
}

func (x *CheckDeviceHealthRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type CheckDeviceHealthResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeviceWorkStatus *DeviceWorkStatus      `protobuf:"bytes,1,opt,name=device_work_status,json=deviceWorkStatus,proto3" json:"device_work_status,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CheckDeviceHealthResponse) Reset() {
	*x = CheckDeviceHealthResponse{}
	mi := &file_rpc_device_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDeviceHealthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDeviceHealthResponse) ProtoMessage() {}

func (x *CheckDeviceHealthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDeviceHealthResponse.ProtoReflect.Descriptor instead.
func (*CheckDeviceHealthResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{15}
}

func (x *CheckDeviceHealthResponse) GetDeviceWorkStatus() *DeviceWorkStatus {
	if x != nil {
		return x.DeviceWorkStatus
	}
	return nil
}

type ControlDeviceByDeviceIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	FrameInfo     *FrameInfo             `protobuf:"bytes,2,opt,name=frame_info,json=frameInfo,proto3" json:"frame_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlDeviceByDeviceIDRequest) Reset() {
	*x = ControlDeviceByDeviceIDRequest{}
	mi := &file_rpc_device_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlDeviceByDeviceIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlDeviceByDeviceIDRequest) ProtoMessage() {}

func (x *ControlDeviceByDeviceIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlDeviceByDeviceIDRequest.ProtoReflect.Descriptor instead.
func (*ControlDeviceByDeviceIDRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{16}
}

func (x *ControlDeviceByDeviceIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ControlDeviceByDeviceIDRequest) GetFrameInfo() *FrameInfo {
	if x != nil {
		return x.FrameInfo
	}
	return nil
}

type ControlDeviceByDeviceIDResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeviceWorkStatus *DeviceWorkStatus      `protobuf:"bytes,1,opt,name=device_work_status,json=deviceWorkStatus,proto3" json:"device_work_status,omitempty"`
	FrameInfos       *FrameInfo             `protobuf:"bytes,2,opt,name=frame_infos,json=frameInfos,proto3" json:"frame_infos,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ControlDeviceByDeviceIDResponse) Reset() {
	*x = ControlDeviceByDeviceIDResponse{}
	mi := &file_rpc_device_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlDeviceByDeviceIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlDeviceByDeviceIDResponse) ProtoMessage() {}

func (x *ControlDeviceByDeviceIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlDeviceByDeviceIDResponse.ProtoReflect.Descriptor instead.
func (*ControlDeviceByDeviceIDResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{17}
}

func (x *ControlDeviceByDeviceIDResponse) GetDeviceWorkStatus() *DeviceWorkStatus {
	if x != nil {
		return x.DeviceWorkStatus
	}
	return nil
}

func (x *ControlDeviceByDeviceIDResponse) GetFrameInfos() *FrameInfo {
	if x != nil {
		return x.FrameInfos
	}
	return nil
}

type PushDataConfigToEtcdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	FrameInfos    []*FrameInfo           `protobuf:"bytes,2,rep,name=frame_infos,json=frameInfos,proto3" json:"frame_infos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushDataConfigToEtcdRequest) Reset() {
	*x = PushDataConfigToEtcdRequest{}
	mi := &file_rpc_device_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushDataConfigToEtcdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushDataConfigToEtcdRequest) ProtoMessage() {}

func (x *PushDataConfigToEtcdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushDataConfigToEtcdRequest.ProtoReflect.Descriptor instead.
func (*PushDataConfigToEtcdRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{18}
}

func (x *PushDataConfigToEtcdRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PushDataConfigToEtcdRequest) GetFrameInfos() []*FrameInfo {
	if x != nil {
		return x.FrameInfos
	}
	return nil
}

type PushDataConfigToEtcdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushDataConfigToEtcdResponse) Reset() {
	*x = PushDataConfigToEtcdResponse{}
	mi := &file_rpc_device_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushDataConfigToEtcdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushDataConfigToEtcdResponse) ProtoMessage() {}

func (x *PushDataConfigToEtcdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushDataConfigToEtcdResponse.ProtoReflect.Descriptor instead.
func (*PushDataConfigToEtcdResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{19}
}

func (x *PushDataConfigToEtcdResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_rpc_device_proto protoreflect.FileDescriptor

const file_rpc_device_proto_rawDesc = "" +
	"\n" +
	"\x10rpc/device.proto\x12\rdevicemanager\"9\n" +
	"\x1aGetFramesByDeviceIDRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\"O\n" +
	"\x1bGetFramesByDeviceIDResponse\x120\n" +
	"\x06frames\x18\x01 \x03(\v2\x18.devicemanager.FrameInfoR\x06frames\"{\n" +
	"\tFrameInfo\x127\n" +
	"\n" +
	"frame_meta\x18\x01 \x01(\v2\x18.devicemanager.FrameMetaR\tframeMeta\x125\n" +
	"\tframe_lib\x18\x02 \x01(\v2\x18.devicemanager.FrameLibsR\bframeLib\"f\n" +
	"\tFrameMeta\x12\x1b\n" +
	"\tframe_uid\x18\x01 \x01(\tR\bframeUid\x12\x1d\n" +
	"\n" +
	"frame_type\x18\x02 \x01(\tR\tframeType\x12\x1d\n" +
	"\n" +
	"frame_name\x18\x03 \x01(\tR\tframeName\"\xb0\x01\n" +
	"\tFrameLibs\x12:\n" +
	"\vmodbus_info\x18\x01 \x01(\v2\x19.devicemanager.ModbusInfoR\n" +
	"modbusInfo\x124\n" +
	"\tuart_info\x18\x02 \x01(\v2\x17.devicemanager.UartInfoR\buartInfo\x121\n" +
	"\budp_info\x18\x03 \x01(\v2\x16.devicemanager.UdpInfoR\audpInfo\"\x93\x01\n" +
	"\n" +
	"ModbusInfo\x12\x10\n" +
	"\x03tid\x18\x01 \x01(\tR\x03tid\x12\x10\n" +
	"\x03pid\x18\x02 \x01(\tR\x03pid\x12\x10\n" +
	"\x03len\x18\x03 \x01(\tR\x03len\x12\x10\n" +
	"\x03uid\x18\x04 \x01(\tR\x03uid\x12\x0e\n" +
	"\x02fc\x18\x05 \x01(\tR\x02fc\x12-\n" +
	"\x05datas\x18\x06 \x03(\v2\x17.devicemanager.DataDefsR\x05datas\"\x8b\x01\n" +
	"\bUartInfo\x12\x16\n" +
	"\x06header\x18\x01 \x01(\tR\x06header\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x12\x10\n" +
	"\x03cmd\x18\x03 \x01(\tR\x03cmd\x12\x12\n" +
	"\x04tail\x18\x04 \x01(\tR\x04tail\x12-\n" +
	"\x05datas\x18\x05 \x03(\v2\x17.devicemanager.DataDefsR\x05datas\"}\n" +
	"\aUdpInfo\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x16\n" +
	"\x06header\x18\x02 \x01(\tR\x06header\x12\x17\n" +
	"\atype_id\x18\x03 \x01(\tR\x06typeId\x12-\n" +
	"\x05datas\x18\x04 \x03(\v2\x17.devicemanager.DataDefsR\x05datas\"\x86\x01\n" +
	"\bDataDefs\x12\x14\n" +
	"\x05index\x18\x01 \x01(\tR\x05index\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x12\n" +
	"\x04unit\x18\x04 \x01(\tR\x04unit\x12\x14\n" +
	"\x05value\x18\x05 \x01(\tR\x05value\x12\x12\n" +
	"\x04desc\x18\x06 \x01(\tR\x04desc\"3\n" +
	"\x14GetDeviceByIDRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\"\xbb\x01\n" +
	"\x15GetDeviceByIDResponse\x12S\n" +
	"\x14device_resource_info\x18\x01 \x01(\v2!.devicemanager.DeviceResourceInfoR\x12deviceResourceInfo\x12M\n" +
	"\x12device_work_status\x18\x02 \x01(\v2\x1f.devicemanager.DeviceWorkStatusR\x10deviceWorkStatus\"\xb2\x02\n" +
	"\x12DeviceResourceInfo\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x0e\n" +
	"\x02os\x18\x04 \x01(\tR\x02os\x12,\n" +
	"\x03cpu\x18\x05 \x01(\v2\x1a.devicemanager.ResourceDefR\x03cpu\x12,\n" +
	"\x03gpu\x18\x06 \x01(\v2\x1a.devicemanager.ResourceDefR\x03gpu\x12.\n" +
	"\x04disk\x18\a \x01(\v2\x1a.devicemanager.ResourceDefR\x04disk\x12,\n" +
	"\x03mem\x18\b \x01(\v2\x1a.devicemanager.ResourceDefR\x03mem\x12\x1a\n" +
	"\bprotocol\x18\t \x03(\tR\bprotocol\"M\n" +
	"\vResourceDef\x12\x16\n" +
	"\x06amount\x18\x01 \x01(\x03R\x06amount\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x12\n" +
	"\x04unit\x18\x03 \x01(\tR\x04unit\"r\n" +
	"\x10DeviceWorkStatus\x12#\n" +
	"\rhealth_status\x18\x01 \x01(\tR\fhealthStatus\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x1b\n" +
	"\twork_mode\x18\x03 \x01(\tR\bworkMode\"7\n" +
	"\x18CheckDeviceHealthRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\"j\n" +
	"\x19CheckDeviceHealthResponse\x12M\n" +
	"\x12device_work_status\x18\x01 \x01(\v2\x1f.devicemanager.DeviceWorkStatusR\x10deviceWorkStatus\"v\n" +
	"\x1eControlDeviceByDeviceIDRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x127\n" +
	"\n" +
	"frame_info\x18\x02 \x01(\v2\x18.devicemanager.FrameInfoR\tframeInfo\"\xab\x01\n" +
	"\x1fControlDeviceByDeviceIDResponse\x12M\n" +
	"\x12device_work_status\x18\x01 \x01(\v2\x1f.devicemanager.DeviceWorkStatusR\x10deviceWorkStatus\x129\n" +
	"\vframe_infos\x18\x02 \x01(\v2\x18.devicemanager.FrameInfoR\n" +
	"frameInfos\"u\n" +
	"\x1bPushDataConfigToEtcdRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x129\n" +
	"\vframe_infos\x18\x02 \x03(\v2\x18.devicemanager.FrameInfoR\n" +
	"frameInfos\"8\n" +
	"\x1cPushDataConfigToEtcdResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage2\xa5\x04\n" +
	"\x06Device\x12l\n" +
	"\x13GetFramesByDeviceID\x12).devicemanager.GetFramesByDeviceIDRequest\x1a*.devicemanager.GetFramesByDeviceIDResponse\x12Z\n" +
	"\rGetDeviceByID\x12#.devicemanager.GetDeviceByIDRequest\x1a$.devicemanager.GetDeviceByIDResponse\x12f\n" +
	"\x11CheckDeviceHealth\x12'.devicemanager.CheckDeviceHealthRequest\x1a(.devicemanager.CheckDeviceHealthResponse\x12x\n" +
	"\x17ControlDeviceByDeviceID\x12-.devicemanager.ControlDeviceByDeviceIDRequest\x1a..devicemanager.ControlDeviceByDeviceIDResponse\x12o\n" +
	"\x14PushDataConfigToEtcd\x12*.devicemanager.PushDataConfigToEtcdRequest\x1a+.devicemanager.PushDataConfigToEtcdResponseB\x11Z\x0f./devicemanagerb\x06proto3"

var (
	file_rpc_device_proto_rawDescOnce sync.Once
	file_rpc_device_proto_rawDescData []byte
)

func file_rpc_device_proto_rawDescGZIP() []byte {
	file_rpc_device_proto_rawDescOnce.Do(func() {
		file_rpc_device_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_device_proto_rawDesc), len(file_rpc_device_proto_rawDesc)))
	})
	return file_rpc_device_proto_rawDescData
}

var file_rpc_device_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_rpc_device_proto_goTypes = []any{
	(*GetFramesByDeviceIDRequest)(nil),      // 0: devicemanager.GetFramesByDeviceIDRequest
	(*GetFramesByDeviceIDResponse)(nil),     // 1: devicemanager.GetFramesByDeviceIDResponse
	(*FrameInfo)(nil),                       // 2: devicemanager.FrameInfo
	(*FrameMeta)(nil),                       // 3: devicemanager.FrameMeta
	(*FrameLibs)(nil),                       // 4: devicemanager.FrameLibs
	(*ModbusInfo)(nil),                      // 5: devicemanager.ModbusInfo
	(*UartInfo)(nil),                        // 6: devicemanager.UartInfo
	(*UdpInfo)(nil),                         // 7: devicemanager.UdpInfo
	(*DataDefs)(nil),                        // 8: devicemanager.DataDefs
	(*GetDeviceByIDRequest)(nil),            // 9: devicemanager.GetDeviceByIDRequest
	(*GetDeviceByIDResponse)(nil),           // 10: devicemanager.GetDeviceByIDResponse
	(*DeviceResourceInfo)(nil),              // 11: devicemanager.DeviceResourceInfo
	(*ResourceDef)(nil),                     // 12: devicemanager.ResourceDef
	(*DeviceWorkStatus)(nil),                // 13: devicemanager.DeviceWorkStatus
	(*CheckDeviceHealthRequest)(nil),        // 14: devicemanager.CheckDeviceHealthRequest
	(*CheckDeviceHealthResponse)(nil),       // 15: devicemanager.CheckDeviceHealthResponse
	(*ControlDeviceByDeviceIDRequest)(nil),  // 16: devicemanager.ControlDeviceByDeviceIDRequest
	(*ControlDeviceByDeviceIDResponse)(nil), // 17: devicemanager.ControlDeviceByDeviceIDResponse
	(*PushDataConfigToEtcdRequest)(nil),     // 18: devicemanager.PushDataConfigToEtcdRequest
	(*PushDataConfigToEtcdResponse)(nil),    // 19: devicemanager.PushDataConfigToEtcdResponse
}
var file_rpc_device_proto_depIdxs = []int32{
	2,  // 0: devicemanager.GetFramesByDeviceIDResponse.frames:type_name -> devicemanager.FrameInfo
	3,  // 1: devicemanager.FrameInfo.frame_meta:type_name -> devicemanager.FrameMeta
	4,  // 2: devicemanager.FrameInfo.frame_lib:type_name -> devicemanager.FrameLibs
	5,  // 3: devicemanager.FrameLibs.modbus_info:type_name -> devicemanager.ModbusInfo
	6,  // 4: devicemanager.FrameLibs.uart_info:type_name -> devicemanager.UartInfo
	7,  // 5: devicemanager.FrameLibs.udp_info:type_name -> devicemanager.UdpInfo
	8,  // 6: devicemanager.ModbusInfo.datas:type_name -> devicemanager.DataDefs
	8,  // 7: devicemanager.UartInfo.datas:type_name -> devicemanager.DataDefs
	8,  // 8: devicemanager.UdpInfo.datas:type_name -> devicemanager.DataDefs
	11, // 9: devicemanager.GetDeviceByIDResponse.device_resource_info:type_name -> devicemanager.DeviceResourceInfo
	13, // 10: devicemanager.GetDeviceByIDResponse.device_work_status:type_name -> devicemanager.DeviceWorkStatus
	12, // 11: devicemanager.DeviceResourceInfo.cpu:type_name -> devicemanager.ResourceDef
	12, // 12: devicemanager.DeviceResourceInfo.gpu:type_name -> devicemanager.ResourceDef
	12, // 13: devicemanager.DeviceResourceInfo.disk:type_name -> devicemanager.ResourceDef
	12, // 14: devicemanager.DeviceResourceInfo.mem:type_name -> devicemanager.ResourceDef
	13, // 15: devicemanager.CheckDeviceHealthResponse.device_work_status:type_name -> devicemanager.DeviceWorkStatus
	2,  // 16: devicemanager.ControlDeviceByDeviceIDRequest.frame_info:type_name -> devicemanager.FrameInfo
	13, // 17: devicemanager.ControlDeviceByDeviceIDResponse.device_work_status:type_name -> devicemanager.DeviceWorkStatus
	2,  // 18: devicemanager.ControlDeviceByDeviceIDResponse.frame_infos:type_name -> devicemanager.FrameInfo
	2,  // 19: devicemanager.PushDataConfigToEtcdRequest.frame_infos:type_name -> devicemanager.FrameInfo
	0,  // 20: devicemanager.Device.GetFramesByDeviceID:input_type -> devicemanager.GetFramesByDeviceIDRequest
	9,  // 21: devicemanager.Device.GetDeviceByID:input_type -> devicemanager.GetDeviceByIDRequest
	14, // 22: devicemanager.Device.CheckDeviceHealth:input_type -> devicemanager.CheckDeviceHealthRequest
	16, // 23: devicemanager.Device.ControlDeviceByDeviceID:input_type -> devicemanager.ControlDeviceByDeviceIDRequest
	18, // 24: devicemanager.Device.PushDataConfigToEtcd:input_type -> devicemanager.PushDataConfigToEtcdRequest
	1,  // 25: devicemanager.Device.GetFramesByDeviceID:output_type -> devicemanager.GetFramesByDeviceIDResponse
	10, // 26: devicemanager.Device.GetDeviceByID:output_type -> devicemanager.GetDeviceByIDResponse
	15, // 27: devicemanager.Device.CheckDeviceHealth:output_type -> devicemanager.CheckDeviceHealthResponse
	17, // 28: devicemanager.Device.ControlDeviceByDeviceID:output_type -> devicemanager.ControlDeviceByDeviceIDResponse
	19, // 29: devicemanager.Device.PushDataConfigToEtcd:output_type -> devicemanager.PushDataConfigToEtcdResponse
	25, // [25:30] is the sub-list for method output_type
	20, // [20:25] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_rpc_device_proto_init() }
func file_rpc_device_proto_init() {
	if File_rpc_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_device_proto_rawDesc), len(file_rpc_device_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_device_proto_goTypes,
		DependencyIndexes: file_rpc_device_proto_depIdxs,
		MessageInfos:      file_rpc_device_proto_msgTypes,
	}.Build()
	File_rpc_device_proto = out.File
	file_rpc_device_proto_goTypes = nil
	file_rpc_device_proto_depIdxs = nil
}
