package logic

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteProducerDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteProducerDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteProducerDataLogic {
	return &DeleteProducerDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteProducerDataLogic) DeleteProducerData(in *datamanager.DeleteProducerDataRequest) (*datamanager.DeleteProducerDataResponse, error) {
	// todo: add your logic here and delete this line
	var err error

	dp, err := l.svcCtx.DriverManager.GetRevisionProducerByProducerUID(
		in.TableInfo.TableUid,
		in.Revision,
	)
	if err != nil {
		return nil, err
	}

	// 使用SQLWrapper的DeleteData方法
	rowsAffected, err := l.svcCtx.SQLWrapper.DeleteData(
		dp,
		in.WhereInfos,
		in.TableInfo,
		in.DatagroupInfo,
		in.InstanceInfo,
		in.FieldsInfo)
	if err != nil {
		return nil, err
	}

	return &datamanager.DeleteProducerDataResponse{
		Status:       "success",
		RowsAffected: uint64(rowsAffected),
	}, nil
}
