apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: etcd-pvc
  namespace: etcd
spec:
  storageClassName: local
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: etcd
  namespace: etcd
  labels:
    app: etcd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: etcd
  template:
    metadata:
      labels:
        app: etcd
    spec:
      containers:
      - name: etcd
        image: registry.aliyuncs.com/google_containers/etcd:3.5.16-0
        command:
        - /usr/local/bin/etcd
        args:
        - --name=s1
        - --data-dir=/etcd-data
        - --listen-client-urls=http://0.0.0.0:12379
        - --advertise-client-urls=http://0.0.0.0:12379
        - --listen-peer-urls=http://0.0.0.0:12380
        - --initial-advertise-peer-urls=http://0.0.0.0:12380
        - --initial-cluster=s1=http://0.0.0.0:12380
        - --initial-cluster-token=tkn
        - --initial-cluster-state=new
        - --log-level=info
        - --logger=zap
        - --log-outputs=stderr
        ports:
        - containerPort: 12379
          name: client
        - containerPort: 12380
          name: peer
        volumeMounts:
        - name: etcd-data
          mountPath: /etcd-data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: etcd-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: etcd-svc
  namespace: etcd
  labels:
    app: etcd
spec:
  type: NodePort
  ports:
  - port: 12379
    targetPort: 12379
    nodePort: 32379
    protocol: TCP
    name: client
  - port: 12380
    targetPort: 12380
    nodePort: 32380
    protocol: TCP
    name: peer
  selector:
    app: etcd