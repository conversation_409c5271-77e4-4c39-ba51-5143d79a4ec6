package utils

import (
	"GCF/app/Datamanager/internal/datamanager"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// SQLConf represents SQL connection configuration
type SQLConf struct {
	Host     string
	Port     string
	User     string
	Password string
	Database string
}

type SQLWrapper struct {
	conn   sqlx.SqlConn
	config *SQLConf
}

// NewSQLWrapper 创建新的SQL封装实例
func NewSQLWrapper(config *SQLConf) *SQLWrapper {
	return &SQLWrapper{
		config: config,
		conn:   config.GetSqlxConn(),
	}
}

// GenerateSqlDsn 生成SQL DSN
func (cfg *SQLConf) generateSqlDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=UTC",
		cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Database)
}

// GetSqlxConn 获取Sqlx连接
func (cfg *SQLConf) GetSqlxConn() sqlx.SqlConn {
	dsn := cfg.generateSqlDSN()
	return sqlx.NewMysql(dsn)
}

// GenerateCreateTableSql 生成GreptimeDB建表SQL语句
func (sw *SQLWrapper) generateCreateTableSql(
	revisionProducer *RevisionDataProducer,
) string {

	// 表名规则: <DataProducer.Uname>_<DataProducer.revision>
	tableName, err := revisionProducer.GetNameUIDRevisionCombined()
	if err != nil {
		logx.Errorf("Failed to get table name for %s: %v", revisionProducer.UID, err)
		return ""
	}

	// 构建 CREATE TABLE 语句
	var columns []string
	var primaryKeys []string

	// 添加时间索引
	columns = append(columns, "ts TIMESTAMP DEFAULT CURRENT_TIMESTAMP() TIME INDEX")

	// 添加标签字段 (作为PRIMARY KEY)
	primaryKeys = append(primaryKeys, "DataProducerInstUID")
	primaryKeys = append(primaryKeys, "DataGroupName")
	columns = append(columns, "DataProducerInstUID STRING")
	columns = append(columns, "DataGroupName STRING")

	// 遍历所有数据组，收集所有可能的字段
	fieldMap := make(map[string]bool)
	for _, group := range revisionProducer.DataGroups {
		logx.Debugf("Processing data group: name=%s, frequency=%s", group.Name, group.Frequency)
		for _, node := range group.DataNode {
			if !fieldMap[node.Name] {
				// 使用反引号包围字段名以避免关键字冲突
				columns = append(columns, fmt.Sprintf("`%s` STRING", node.Name))
				fieldMap[node.Name] = true
			}
		}
	}

	// 构建完整的 CREATE TABLE 语句，使用正确的GreptimeDB语法
	createTableSQL := fmt.Sprintf(`CREATE TABLE IF NOT EXISTS '%s' (
	%s,
	PRIMARY KEY(%s));`,
		tableName, strings.Join(columns, ",\n\t"), strings.Join(primaryKeys, ", "))

	return createTableSQL
}

// CreateTable 为DataProducer创建GreptimeDB表
func (sw *SQLWrapper) CreateTable(revisionProducer *RevisionDataProducer) error {
	// 生成建表SQL
	createTableSQL := sw.generateCreateTableSql(revisionProducer)
	if createTableSQL == "" {
		return fmt.Errorf("failed to generate create table SQL for %s", revisionProducer.UID)
	}

	// 执行建表SQL
	_, err := sw.conn.Exec(createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create GreptimeDB table for %s: %v", revisionProducer.UID, err)
	}

	logx.Infof("Successfully created GreptimeDB table for %s with revision %d", revisionProducer.UID, revisionProducer.Revision)
	return nil
}

func (sw *SQLWrapper) generateWhereSql(
	where_infos []*datamanager.WhereInfo,
	datagroup_info *datamanager.DataGroupTagInfo,
	instance_info *datamanager.InstanceTagInfo,
) string {
	whereSql := ""
	whereFlag := false
	if where_infos != nil {
		validWhereInfos := make([]*datamanager.WhereInfo, 0)
		for _, where_info := range where_infos {
			if where_info != nil &&
				strings.TrimSpace(where_info.ColumnName) != "" &&
				strings.TrimSpace(where_info.CompareSymbol) != "" &&
				strings.TrimSpace(where_info.CompareValue) != "" {
				validWhereInfos = append(validWhereInfos, where_info)
			}
		}
		if len(validWhereInfos) > 0 {
			for i, where_info := range validWhereInfos {
				if i != 0 {
					whereSql = whereSql + " and "
				} else {
					whereSql = "where "
				}
				if where_info.ColumnName == "ts" {
					unixTime, err := time.Parse("2006-01-02 15:04:05.000", where_info.CompareValue)
					if err != nil {
						logx.Errorf("Failed to parse time: %v", err)
						return ""
					}
					compareValue := unixTime.UnixMilli()
					whereSql = whereSql + where_info.ColumnName + " " + where_info.CompareSymbol + " " + fmt.Sprintf("%d", compareValue)
				} else {
					whereSql = whereSql + where_info.ColumnName + " " + where_info.CompareSymbol + " " + fmt.Sprintf("'%s'", where_info.CompareValue)
				}
			}
			whereFlag = true
		}
	}
	if datagroup_info != nil && datagroup_info.DatagroupName != "" {
		if whereFlag {
			whereSql = whereSql + fmt.Sprintf(" and datagroupname = '%s'", datagroup_info.DatagroupName)
		} else {
			whereSql = "where " + whereSql + fmt.Sprintf("datagroupname = '%s'", datagroup_info.DatagroupName)
		}
		whereFlag = true
	}
	if instance_info != nil && instance_info.InstanceUid != "" {
		if whereFlag {
			whereSql = whereSql + fmt.Sprintf(" and dataproducerinstuid = '%s'", instance_info.InstanceUid)
		} else {
			whereSql = "where " + whereSql + fmt.Sprintf("dataproducerinstuid = '%s'", instance_info.InstanceUid)
		}
		whereFlag = true
	}
	return whereSql
}

func (sw *SQLWrapper) generateQuerySql(
	revisionProducer *RevisionDataProducer,
	where_infos []*datamanager.WhereInfo,
	data_row_limit int64,
	table_info *datamanager.TableInfo,
	datagroup_info *datamanager.DataGroupTagInfo,
	instance_info *datamanager.InstanceTagInfo,
	fields_info *datamanager.FieldsInfo,
) (
	querySql string,
	err error,
) {

	tableName, err := revisionProducer.GetNameUIDRevisionCombined()
	if err != nil {
		return "", err
	}
	if fields_info != nil && len(fields_info.FieldName) > 0 {
		querySql = fmt.Sprintf("select ts,datagroupname,dataproducerinstuid,%s from %s ", strings.Join(fields_info.FieldName, ","), tableName)
	} else {
		querySql = fmt.Sprintf("select * from '%s' ", tableName)
	}
	querySql = querySql + sw.generateWhereSql(where_infos, datagroup_info, instance_info) + " "

	if data_row_limit != 0 {
		querySql = querySql + "limit " + fmt.Sprintf("%d", data_row_limit) + " "
	}
	querySql = querySql + ";"
	return querySql, nil
}

func (sw *SQLWrapper) generateDeleteSql(
	revisionProducer *RevisionDataProducer,
	where_infos []*datamanager.WhereInfo,
	table_info *datamanager.TableInfo,
	datagroup_info *datamanager.DataGroupTagInfo,
	instance_info *datamanager.InstanceTagInfo,
	fields_info *datamanager.FieldsInfo,
) (
	deleteSql string,
	err error,
) {

	tableName, err := revisionProducer.GetNameUIDRevisionCombined()
	if err != nil {
		return "", err
	}
	deleteSql = fmt.Sprintf("delete from '%s' ", tableName)
	deleteSql = deleteSql + sw.generateWhereSql(where_infos, datagroup_info, instance_info) + " "
	deleteSql = deleteSql + ";"
	return deleteSql, nil
}

func (sw *SQLWrapper) generateInsertSql(
	revisionProducer *RevisionDataProducer,
	datarows []*datamanager.DataRow,
) (insertSql string, err error) {

	tableName, err := revisionProducer.GetNameUIDRevisionCombined()
	if err != nil {
		return "", err
	}

	var allColumns []string
	allColumns, err = revisionProducer.GetColumns()
	if err != nil {
		return "", err
	}

	if len(datarows) == 0 {
		return "", fmt.Errorf("no data rows to insert")
	}

	systemColumns := []string{"ts", "dataproducerinstuid", "datagroupname"}
	availableDataFields := make(map[string]bool)
	for _, datarow := range datarows {
		for _, singleData := range datarow.Datas {
			availableDataFields[singleData.Field] = true
		}
	}

	var insertColumns []string
	insertColumns = append(insertColumns, systemColumns...)
	for _, col := range allColumns {
		isSystemColumn := false
		for _, sysCol := range systemColumns {
			if col == sysCol {
				isSystemColumn = true
				break
			}
		}
		if !isSystemColumn && availableDataFields[col] {
			insertColumns = append(insertColumns, col)
		}
	}

	insertSql = fmt.Sprintf("insert into '%s' ", tableName)
	insertSql = insertSql + "(" + strings.Join(insertColumns, ",") + ")"
	insertSql = insertSql + " values "

	for i, datarow := range datarows {
		ts := "'" + datarow.Ts.AsTime().Format("2006-01-02 15:04:05") + "'"
		instance_info := datarow.InstanceInfo
		datagroup_info := datarow.DatagroupInfo
		dataStrings := sw.singleDatas2ColumnDataStrings(datarow.Datas, insertColumns[3:])

		valueStrings := []string{ts, "'" + instance_info.InstanceUid + "'", "'" + datagroup_info.DatagroupName + "'"}
		valueStrings = append(valueStrings, dataStrings...)

		insertSql = insertSql + "(" + strings.Join(valueStrings, ",") + ")"
		if i < len(datarows)-1 {
			insertSql = insertSql + ", "
		}
	}
	insertSql = insertSql + ";"
	return insertSql, nil
}

func (sw *SQLWrapper) singleDatas2ColumnDataStrings(
	singleDatas []*datamanager.SingleData,
	columns []string,
) (columnDataStrings []string) {
	singleDataMap := make(map[string]string)
	for _, singleData := range singleDatas {
		singleDataMap[singleData.Field] = singleData.Value
	}

	for _, column := range columns {
		if value, exists := singleDataMap[column]; exists {
			columnDataStrings = append(columnDataStrings, "'"+value+"'")
		} else {
			columnDataStrings = append(columnDataStrings, "NULL")
		}
	}
	return columnDataStrings
}

// QueryData 查询数据
func (sw *SQLWrapper) QueryData(
	revisionProducer *RevisionDataProducer,
	whereInfos []*datamanager.WhereInfo,
	dataRowLimit int64,
	tableInfo *datamanager.TableInfo,
	datagroupInfo *datamanager.DataGroupTagInfo,
	instanceInfo *datamanager.InstanceTagInfo,
	fieldsInfo *datamanager.FieldsInfo,
) ([]*datamanager.DataRow, error) {
	// 生成查询SQL
	querySql, err := sw.generateQuerySql(revisionProducer, whereInfos, dataRowLimit, tableInfo, datagroupInfo, instanceInfo, fieldsInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to generate query SQL: %v", err)
	}

	logx.Infof("Query SQL: %s", querySql)
	// 执行查询
	db, err := sw.conn.RawDB()
	if err != nil {
		return nil, fmt.Errorf("failed to get raw DB: %v", err)
	}

	rows, err := db.Query(querySql)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %v", err)
	}
	defer rows.Close()

	// 转换结果
	return SqlRows2DataRows(rows, instanceInfo, datagroupInfo)
}

// InsertData 插入数据
func (sw *SQLWrapper) InsertData(
	revisionProducer *RevisionDataProducer,
	datarows []*datamanager.DataRow,
) error {
	// 生成插入SQL
	insertSql, err := sw.generateInsertSql(revisionProducer, datarows)
	if err != nil {
		return fmt.Errorf("failed to generate insert SQL: %v", err)
	}

	// 执行插入
	_, err = sw.conn.Exec(insertSql)
	if err != nil {
		return fmt.Errorf("failed to execute insert: %v", err)
	}

	logx.Infof("Successfully inserted %d rows for %s", len(datarows), revisionProducer.UID)
	return nil
}

// DeleteData 删除数据
func (sw *SQLWrapper) DeleteData(
	revisionProducer *RevisionDataProducer,
	whereInfos []*datamanager.WhereInfo,
	tableInfo *datamanager.TableInfo,
	datagroupInfo *datamanager.DataGroupTagInfo,
	instanceInfo *datamanager.InstanceTagInfo,
	fieldsInfo *datamanager.FieldsInfo,
) (int64, error) {
	// 生成删除SQL
	deleteSql, err := sw.generateDeleteSql(revisionProducer, whereInfos, tableInfo, datagroupInfo, instanceInfo, fieldsInfo)
	if err != nil {
		return 0, fmt.Errorf("failed to generate delete SQL: %v", err)
	}

	// 执行删除
	result, err := sw.conn.Exec(deleteSql)
	if err != nil {
		return 0, fmt.Errorf("failed to execute delete: %v", err)
	}

	rowsAffected, _ := result.RowsAffected()
	logx.Infof("Successfully deleted %d rows for %s", rowsAffected, revisionProducer.UID)
	return rowsAffected, nil
}

func SqlRows2DataRows(
	rows *sql.Rows,
	instance_info *datamanager.InstanceTagInfo,
	datagroup_info *datamanager.DataGroupTagInfo,
) (dataRows []*datamanager.DataRow, err error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}

	for rows.Next() {
		values, valuePtrs, rowMap := make([]interface{}, len(columns)), make([]interface{}, len(columns)), make(map[string]interface{})
		for i := range values {
			valuePtrs[i] = &values[i]
		}
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}
		for i, col := range columns {
			if b, ok := values[i].([]byte); ok {
				rowMap[col] = string(b)
			} else {
				rowMap[col] = values[i]
			}
		}
		results = append(results, rowMap)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	for _, row := range results {
		dataRow := &datamanager.DataRow{}
		if tsVal, ok := row["ts"]; ok {
			dataRow.Ts = timestamppb.New(tsVal.(time.Time))
		}
		if instance_info != nil && instance_info.InstanceUid != "" {
			dataRow.InstanceInfo = instance_info
		} else if instanceUid, ok := row["dataproducerinstuid"]; ok {
			if instanceUidStr, ok := instanceUid.(string); ok {
				dataRow.InstanceInfo = &datamanager.InstanceTagInfo{
					InstanceUid: instanceUidStr,
				}
			}
		}
		if datagroupName, ok := row["datagroupname"]; ok {
			if datagroupNameStr, ok := datagroupName.(string); ok {
				dataRow.DatagroupInfo = &datamanager.DataGroupTagInfo{
					DatagroupName: datagroupNameStr,
				}
			}
		} else if datagroup_info != nil && datagroup_info.DatagroupName != "" {
			dataRow.DatagroupInfo = datagroup_info
		}
		var singleDataList []*datamanager.SingleData

		systemFields := map[string]bool{
			"ts":                  true,
			"datagroupname":       true,
			"dataproducerinstuid": true,
		}
		for fieldName, fieldValue := range row {
			if !systemFields[fieldName] {
				singleData := &datamanager.SingleData{
					Field: fieldName,
					Value: fmt.Sprintf("%v", fieldValue),
				}
				singleDataList = append(singleDataList, singleData)
			}
		}

		dataRow.Datas = singleDataList
		dataRows = append(dataRows, dataRow)
	}
	return dataRows, nil
}
