package utils

import (
	"context"
	"fmt"
	"sync"

	"GCF/pkg/zetcd"

	"github.com/zeromicro/go-queue/kq"
	"github.com/zeromicro/go-zero/core/logx"
	clientv3 "go.etcd.io/etcd/client/v3"
)

// DriverManager Driver管理器，管理drivers、watch_list和instances
type DriverManager struct {
	mu          sync.RWMutex // 读写锁，保护所有数据的并发访问
	WatchList   *WatchList
	Drivers     map[string]*Driver // key: producer_key, value: Driver
	SQLConn     *SQLWrapper
	ZEtcdClient *zetcd.ZEtcdClient
	KafkaConf   *kq.KqConf
}

// NewDriverManager 创建新的DriverManager实例
func NewDriverManager(sqlWrapper *SQLWrapper, ZEtcdClient *zetcd.ZEtcdClient, kafkaConf *kq.KqConf) *DriverManager {
	dm := &DriverManager{
		WatchList:   &WatchList{WatchList: []string{}},
		Drivers:     make(map[string]*Driver),
		SQLConn:     sqlWrapper,
		ZEtcdClient: ZEtcdClient,
		KafkaConf:   kafkaConf,
	}

	return dm
}

func (dm *DriverManager) GetRevisionDriverByInstanceUID(
	instanceUid string, revision int64,
) (*Driver, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var driver *Driver
	rp, err := dm.GetRevisionProducerByInstanceUID(instanceUid, revision)
	if err != nil {
		return nil, err
	}
	for _, d := range dm.Drivers {
		if d.RevisionDataProducer.DataProducer.UID == instanceUid {
			driver = &Driver{
				Instances:            d.Instances,
				RevisionDataProducer: *rp,
			}
		}
	}
	return driver, nil
}

func (dm *DriverManager) GetRevisionDriverByProducerUID(
	producerUID string, revision int64,
) (*Driver, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var driver *Driver
	rp, err := dm.GetRevisionProducerByProducerUID(producerUID, revision)
	if err != nil {
		return nil, err
	}
	for _, d := range dm.Drivers {
		if d.RevisionDataProducer.DataProducer.UID == producerUID{
			driver = &Driver{
				Instances:            d.Instances,
				RevisionDataProducer: *rp,
			}
		}
	}
	return driver, nil
}

// GetRevisionProducerByProducerUID 根据Producer名称获取版本化的Producer
func (dm *DriverManager) GetRevisionProducerByProducerUID(
	producerUID string, revision int64,
) (*RevisionDataProducer, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	// 首先查找对应的driver
	var foundDriverKey string
	var foundDriver *Driver
	for driverKey, driver := range dm.Drivers {
		if driver.RevisionDataProducer.DataProducer.UID == producerUID {
			foundDriverKey = driverKey
			foundDriver = driver
			break
		}
	}

	if foundDriver == nil {
		return nil, fmt.Errorf("driver not found for producerUID: %s", producerUID)
	}

	// 验证版本号，如果版本号匹配则直接返回
	if foundDriver.RevisionDataProducer.Revision == revision || revision == 0 {
		return &foundDriver.RevisionDataProducer, nil
	}

	// 版本号不匹配，需要从ETCD重新查询并生成对应版本的VPD
	logx.Infof("Revision mismatch for producerUID %s: current=%s, requested=%d. Querying ETCD for correct revision.",
		producerUID, foundDriver.GetRevisionString(), revision)

	// 从ETCD查询指定版本的producer数据
	client := dm.ZEtcdClient
	if client == nil {
		return nil, fmt.Errorf("etcd client is nil, cannot query for revision %d", revision)
	}
	producerKey := foundDriverKey + "/producer"

	// 从etcd获取producer数据
	resp, err := client.Get(producerKey, clientv3.WithRev(revision))
	if err != nil {
		return nil, fmt.Errorf("failed to get producer %s from etcd: %v", producerKey, err)
	}

	if len(resp.Kvs) == 0 {
		return nil, fmt.Errorf("no data found for producer: %s", producerKey)
	}

	// 解析producer数据
	producerValue := string(resp.Kvs[0].Value)
	dataProducer, err := NewDataProducerFromJSON(producerValue)
	if err != nil {
		return nil, fmt.Errorf("failed to parse data producer: %v", err)
	}

	// 创建指定版本的RevisionDataProducer
	revisionDataProducer := NewRevisionDataProducer(dataProducer, revision)

	return revisionDataProducer, nil
}

// GetRevisionProducerByInstanceUID 根据实例信息获取版本化的Producer
func (dm *DriverManager) GetRevisionProducerByInstanceUID(
	instanceUid string, revision int64) (*RevisionDataProducer, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	// 首先查找对应的driver和instance
	var foundDriverKey string
	var foundDriver *Driver
	for driverKey, driver := range dm.Drivers {
		for _, instance := range driver.Instances.Instances {
			if instance.UID == instanceUid {
				foundDriverKey = driverKey
				foundDriver = driver
				break
			}
		}
		if foundDriver != nil {
			break
		}
	}

	if foundDriver == nil {
		return nil, fmt.Errorf("driver not found for instanceUID: %s", instanceUid)
	}

	// 验证版本号，如果版本号匹配则直接返回
	if foundDriver.RevisionDataProducer.Revision == revision || revision == 0 {
		return &foundDriver.RevisionDataProducer, nil
	}

	// 版本号不匹配，需要从ETCD重新查询并生成对应版本的VPD
	logx.Infof("Revision mismatch for instanceUID %s: current=%s, requested=%d. Querying ETCD for correct revision.",
		instanceUid, foundDriver.GetRevisionString(), revision)

	// 从ETCD查询指定版本的producer数据
	client := dm.ZEtcdClient
	if client == nil {
		return nil, fmt.Errorf("etcd client is nil, cannot query for revision %d", revision)
	}

	// 构建producer key
	producerKey := foundDriverKey + "/producer"

	// 从etcd获取producer数据
	resp, err := client.Get(producerKey, clientv3.WithRev(revision))
	if err != nil {
		return nil, fmt.Errorf("failed to get producer %s from etcd: %v", producerKey, err)
	}

	if len(resp.Kvs) == 0 {
		return nil, fmt.Errorf("no data found for producer: %s", producerKey)
	}

	// 解析producer数据
	producerValue := string(resp.Kvs[0].Value)
	dataProducer, err := NewDataProducerFromJSON(producerValue)
	if err != nil {
		return nil, fmt.Errorf("failed to parse data producer: %v", err)
	}

	// 创建指定版本的RevisionDataProducer
	revisionDataProducer := NewRevisionDataProducer(dataProducer, revision)

	return revisionDataProducer, nil
}

// updateWatchList 更新监听列表
func (dm *DriverManager) updateWatchList(watchListJSON string) error {
	watchList, err := NewWatchListFromJSON(watchListJSON)
	if err != nil {
		return fmt.Errorf("failed to update watch list: %v", err)
	}
	dm.WatchList = watchList
	logx.Infof("Updated watch list with %d items", len(watchList.WatchList))
	return nil
}

// removeWatchList 清空监听列表
func (dm *DriverManager) removeWatchList() {
	dm.WatchList = &WatchList{WatchList: []string{}}
	logx.Infof("Removed all items from watch list")
}

// handleProducerUpdate 处理Producer更新（当producer变化时）
func (dm *DriverManager) handleProducerUpdate(driverKey string, dataProducerJSON string, etcdRevision int64) error {
	dataProducer, err := NewDataProducerFromJSON(dataProducerJSON)
	if err != nil {
		return fmt.Errorf("failed to parse data producer: %v", err)
	}

	// 根据etcd版本号创建版本化的DataProducer
	revisionDataProducer := NewRevisionDataProducer(dataProducer, etcdRevision)
	// Producer更新成功后，启动instances监听器

	// 检查是否已存在该Driver
	existingDriver, exists := dm.Drivers[driverKey]
	// 确定要使用的instances
	var instances *Instances
	if exists {
		// 每次PUT操作都会递增版本号，需要重新创建数据库表和Kafka队列
		logx.Infof("Producer revision changed from %s to %s, will recreate database table and Kafka queue", existingDriver.GetRevisionString(), revisionDataProducer.GetRevisionString())
		// 停止现有的Kafka队列
		dm.stopKafkaQueue(existingDriver)
		// 保留现有的instances
		instances = existingDriver.Instances
	} else {
		logx.Infof("New driver %s with revision %s", driverKey, revisionDataProducer.GetRevisionString())
		// 新的Driver使用空的instances
		if err = dm.startInstancesWatcher(driverKey); err != nil {
			logx.Errorf("[ProducerWatcher:%s]Failed to start instances watcher for %s: %v", driverKey+"/producer", driverKey, err)
			return err
		}
		instances = &Instances{Instances: []Instance{}}
	}

	// 创建新的Driver
	newDriver := &Driver{
		RevisionDataProducer: *revisionDataProducer,
		Instances:            instances,
	}

	// 创建数据库表
	dm.handleDriverTableCreation(driverKey, newDriver)

	// 启动Kafka队列
	if err := dm.startKafkaQueue(newDriver); err != nil {
		logx.Errorf("Failed to start Kafka queue for driver %s: %v", driverKey, err)
	}

	// 存储Driver
	dm.Drivers[driverKey] = newDriver

	logx.Infof("Updated driver: %s with revision %s", driverKey, revisionDataProducer.GetRevisionString())
	return nil
}

// handleInstancesUpdate 处理Instances更新（当instances变化时）
func (dm *DriverManager) handleInstancesUpdate(driverKey string, instancesJSON string) error {
	instances := NewInstancesFromJson(instancesJSON)
	if instances == nil {
		return fmt.Errorf("failed to parse instances JSON")
	}

	driver, exists := dm.Drivers[driverKey]
	if !exists {
		logx.Errorf("Driver not found for instances update: %s", driverKey)
		return fmt.Errorf("driver not found: %s", driverKey)
	}

	// 更新instances
	driver.Instances = instances
	logx.Infof("Updated instances for driver: %s, instance count: %d", driverKey, len(instances.Instances))
	return nil
}

// removeProducer 移除Driver
func (dm *DriverManager) removeProducer(key string) {
	if driver, exists := dm.Drivers[key]; exists {
		dm.stopKafkaQueue(driver)
		driver.DataProducer = nil
		logx.Infof("Removed driver: %s", key)
	} else {
		logx.Infof("Driver not found for removal: %s", key)
	}
}

// StartWatching 启动监听器，监听watch_list、producer和instances变化
func (dm *DriverManager) StartWatching() error {
	// 启动watch_list监听器
	err := dm.startWatchListWatcher()
	if err != nil {
		return fmt.Errorf("failed to start watch_list watcher: %v", err)
	}

	logx.Infof("DriverManager started watching")
	return nil
}

// startProducerWatcher 启动单个Producer的监听器
func (dm *DriverManager) startProducerWatcher(driverKey string) error {
	// 从driverKey生成producerKey
	producerKey := driverKey + "/producer"

	// 定义producer变化的回调函数
	callback := func(key, value, eventType string, metadata map[string]interface{}) error {
		dm.mu.Lock()
		defer dm.mu.Unlock()
		switch eventType {
		case "PUT":
			logx.Infof("[ProducerWatcher:%s]Producer data updated: %s", producerKey, key)
			// 从metadata中获取etcd版本号
			etcdRevision := int64(1) // 默认版本
			if revision, exists := metadata["etcd_revision"]; exists {
				if v, ok := revision.(int64); ok {
					etcdRevision = v
				}
			}
			// 处理Producer更新
			err := dm.handleProducerUpdate(driverKey, value, etcdRevision)
			if err != nil {
				logx.Errorf("[ProducerWatcher:%s]Failed to handle producer update: %v", producerKey, err)
				return err
			}

		case "DELETE":
			logx.Infof("[ProducerWatcher:%s]Producer data deleted: %s", producerKey, key)
			// 处理Producer删除
			dm.handleProducerDelete(driverKey)
		}
		return nil
	}

	// 添加到WatcherGroup
	metadata := map[string]interface{}{
		"driver_key":   driverKey,
		"watcher_type": "producer",
		"description":  fmt.Sprintf("监听driver %s的producer变化", driverKey),
	}
	return dm.ZEtcdClient.Subscriber.AddSubscriber(producerKey, callback, metadata)
}

// startInstancesWatcher 启动单个Producer对应的instances监听器
func (dm *DriverManager) startInstancesWatcher(driverKey string) error {
	// instances的etcd路径格式: {driver_key}/instances
	instancesKey := driverKey + "/instances"

	// 定义instances变化的回调函数
	callback := func(key, value, eventType string, metadata map[string]interface{}) error {
		dm.mu.Lock()
		defer dm.mu.Unlock()
		switch eventType {
		case "PUT":
			logx.Infof("[InstancesWatcher:%s]Instances data updated: %s", instancesKey, key)
			// 处理Instances更新
			err := dm.handleInstancesUpdate(driverKey, value)
			if err != nil {
				logx.Errorf("[InstancesWatcher:%s]Failed to handle instances update: %v", instancesKey, err)
				return err
			}
		case "DELETE":
			logx.Infof("[InstancesWatcher:%s]Instances data deleted: %s", instancesKey, key)
			// 清空instances
			dm.handleInstancesDelete(driverKey)
		}
		return nil
	}

	// 添加到WatcherGroup
	metadata := map[string]interface{}{
		"driver_key":   driverKey,
		"watcher_type": "instances",
		"description":  fmt.Sprintf("监听driver %s的instances变化", driverKey),
	}
	return dm.ZEtcdClient.Subscriber.AddSubscriber(instancesKey, callback, metadata)
}

// startWatchListWatcher 启动监听 /watch_list 键的监听器
func (dm *DriverManager) startWatchListWatcher() error {
	// 定义watch_list变化的回调函数
	callback := func(key, value, eventType string, metadata map[string]interface{}) error {
		dm.mu.Lock()
		defer dm.mu.Unlock()
		switch eventType {
		case "PUT":
			logx.Infof("[WatchListWatcher]Watch list updated: %s", key)
			// 更新WatchList并更新Producer和Instances监听器
			err := dm.handleWatchListUpdate(value)
			if err != nil {
				logx.Errorf("Failed to update driver watchers: %v", err)
				return err
			}
		case "DELETE":
			logx.Infof("[WatchListWatcher]Watch list deleted: %s", key)
			// 处理监听列表删除
			dm.handleWatchListDelete()
		}
		return nil
	}

	// 添加到WatcherGroup的metadata
	metadata := map[string]interface{}{
		"watcher_type": "watch_list",
		"description":  "监听watch_list变化",
	}

	return dm.ZEtcdClient.Subscriber.AddSubscriber("/watch_list", callback, metadata)
}

// handleWatchListUpdate 处理监听列表更新
func (dm *DriverManager) handleWatchListUpdate(watchListJSON string) error {
	// 更新WatchList
	err := dm.updateWatchList(watchListJSON)
	if err != nil {
		return err
	}

	// 获取当前的监听列表
	currentWatchList := dm.WatchList.WatchList

	// 移除所有旧的producer监听器
	for _, driverKey := range currentWatchList {
		dm.ZEtcdClient.Subscriber.RemoveSubscriber(driverKey + "/producer")
	}
	// 为每个driver启动producer监听器（instances监听器会在producer数据处理后自动启动）
	for _, driverKey := range currentWatchList {
		// 启动producer监听器
		err := dm.startProducerWatcher(driverKey)
		if err != nil {
			logx.Errorf("Failed to start producer watcher for %s: %v", driverKey, err)
			continue
		}
	}

	logx.Infof("Updated watchers for %d drivers", len(currentWatchList))
	return nil
}

// handleWatchListDelete 处理监听列表删除
func (dm *DriverManager) handleWatchListDelete() {
	// 停止所有Driver的Kafka队列
	for _, driver := range dm.Drivers {
		dm.stopKafkaQueue(driver)
		dm.ZEtcdClient.Subscriber.RemoveSubscriber(driver.DataProducer.UID)
	}
	dm.removeWatchList()

	logx.Infof("Handled watch list deletion")
}

// handleDriverTableCreation 处理Driver表创建/重新创建
func (dm *DriverManager) handleDriverTableCreation(driverKey string, driver *Driver) {
	// 创建GreptimeDB表
	err := dm.SQLConn.CreateTable(&driver.RevisionDataProducer)
	if err != nil {
		logx.Errorf("Failed to create table for driver %s: %v", driverKey, err)
	} else {
		logx.Infof("Handled driver table creation for: %s with revision %s", driverKey, driver.GetRevisionString())
	}
}

// handleProducerDelete 处理Producer删除
func (dm *DriverManager) handleProducerDelete(driverKey string) {
	// 停止Kafka队列
	if _, exists := dm.Drivers[driverKey]; exists {
		dm.removeProducer(driverKey)
	}
	logx.Infof("Handled producer deletion for: %s", driverKey)
}

// handleInstancesDelete 处理instances删除
func (dm *DriverManager) handleInstancesDelete(driverKey string) {
	driver, exists := dm.Drivers[driverKey]
	if exists {
		driver.Instances = &Instances{Instances: []Instance{}}
		logx.Infof("Cleared instances for driver: %s", driverKey)
	}
}

// startKafkaQueue 启动Driver的Kafka消费者队列
func (dm *DriverManager) startKafkaQueue(driver *Driver) error {
	if driver.DataProducer == nil {
		return fmt.Errorf("DataProducer is nil, cannot start Kafka queue")
	}

	// 使用DataProducer.UID作为Kafka主题
	topic := driver.DataProducer.UID
	if topic == "" {
		return fmt.Errorf("DataProducer.UID is empty, cannot determine Kafka topic")
	}
	config := *dm.KafkaConf
	// 配置Kafka消费者
	config.Topic = topic
	config.Name = topic

	// 创建Kafka队列
	Kq, err := kq.NewQueue(config, kq.WithHandle(func(ctx context.Context, key, value string) error {
		logx.Infof("Received Kafka message: key=%s, value=%s", key, value)

		err := dm.processKafkaMessage(driver, value)
		if err != nil {
			logx.Errorf("Failed to process Kafka message: %v", err)
		}
		return nil
	}))
	if err != nil {
		return err
	}

	// 启动队列
	go Kq.Start()

	driver.Kq = Kq

	logx.Infof("Started Kafka queue for driver with topic: %s, group: DataManager", topic)
	return nil
}

// stopKafkaQueue 停止Driver的Kafka消费者队列
func (dm *DriverManager) stopKafkaQueue(driver *Driver) {
	if driver.Kq != nil {
		driver.Kq.Stop()
		driver.Kq = nil
		logx.Infof("Stopped Kafka queue for driver %s", driver.DataProducer.UID)
	}
}

// processKafkaMessage 处理从Kafka接收到的消息
func (dm *DriverManager) processKafkaMessage(driver *Driver, value string) error {
	topic := ""
	if driver.DataProducer != nil {
		topic = driver.DataProducer.UID
	}

	logx.Infof("Processing Kafka message from topic %s", topic)

	// 1. 解析JSON消息
	kafkaMessage, err := NewKafkaMessageFromJSON(value)
	if err != nil {
		return fmt.Errorf("failed to parse Kafka message: %v", err)
	}
	logx.Infof("Parse success")

	d, err := dm.GetRevisionDriverByProducerUID(driver.DataProducer.UID, kafkaMessage.MetaData.Revision)	

	if err != nil {
		return fmt.Errorf("failed to get driver by producer uid: %v", err)
	}

	// 2. 验证数据格式合规性
	err = kafkaMessage.ValidateCompliance(d)
	if err != nil {
		return fmt.Errorf("message validation failed: %v", err)
	}
	logx.Infof("Revision %d, Validating Kafka message from topic %s", kafkaMessage.MetaData.Revision, topic)

	// 3. 存储到GreptimeDB
	err = kafkaMessage.StoreToGreptimeDB(d, dm.SQLConn)
	if err != nil {
		return fmt.Errorf("failed to store to GreptimeDB: %v", err)
	}
	logx.Infof("Revision %d, Storing Kafka message from topic %s", kafkaMessage.MetaData.Revision, topic)

	// 4. 触发其他业务逻辑
	return nil
}
