// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/test_create_db"
	"context"
	"fmt"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCreateDbCreate is the builder for creating a Test_create_db entity.
type TestCreateDbCreate struct {
	config
	mutation *TestCreateDbMutation
	hooks    []Hook
}

// Mutation returns the TestCreateDbMutation object of the builder.
func (tcdc *TestCreateDbCreate) Mutation() *TestCreateDbMutation {
	return tcdc.mutation
}

// Save creates the Test_create_db in the database.
func (tcdc *TestCreateDbCreate) Save(ctx context.Context) (*Test_create_db, error) {
	return withHooks(ctx, tcdc.sqlSave, tcdc.mutation, tcdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tcdc *TestCreateDbCreate) SaveX(ctx context.Context) *Test_create_db {
	v, err := tcdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcdc *TestCreateDbCreate) Exec(ctx context.Context) error {
	_, err := tcdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcdc *TestCreateDbCreate) ExecX(ctx context.Context) {
	if err := tcdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tcdc *TestCreateDbCreate) check() error {
	return nil
}

func (tcdc *TestCreateDbCreate) sqlSave(ctx context.Context) (*Test_create_db, error) {
	if err := tcdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tcdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tcdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	tcdc.mutation.id = &_node.ID
	tcdc.mutation.done = true
	return _node, nil
}

func (tcdc *TestCreateDbCreate) createSpec() (*Test_create_db, *sqlgraph.CreateSpec) {
	var (
		_node = &Test_create_db{config: tcdc.config}
		_spec = sqlgraph.NewCreateSpec(test_create_db.Table, sqlgraph.NewFieldSpec(test_create_db.FieldID, field.TypeInt))
	)
	return _node, _spec
}

// TestCreateDbCreateBulk is the builder for creating many Test_create_db entities in bulk.
type TestCreateDbCreateBulk struct {
	config
	err      error
	builders []*TestCreateDbCreate
}

// Save creates the Test_create_db entities in the database.
func (tcdcb *TestCreateDbCreateBulk) Save(ctx context.Context) ([]*Test_create_db, error) {
	if tcdcb.err != nil {
		return nil, tcdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcdcb.builders))
	nodes := make([]*Test_create_db, len(tcdcb.builders))
	mutators := make([]Mutator, len(tcdcb.builders))
	for i := range tcdcb.builders {
		func(i int, root context.Context) {
			builder := tcdcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TestCreateDbMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcdcb *TestCreateDbCreateBulk) SaveX(ctx context.Context) []*Test_create_db {
	v, err := tcdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcdcb *TestCreateDbCreateBulk) Exec(ctx context.Context) error {
	_, err := tcdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcdcb *TestCreateDbCreateBulk) ExecX(ctx context.Context) {
	if err := tcdcb.Exec(ctx); err != nil {
		panic(err)
	}
}
