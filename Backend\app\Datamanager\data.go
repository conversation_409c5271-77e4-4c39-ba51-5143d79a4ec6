package main

import (
	"flag"
	"log"

	"GCF/app/Datamanager/internal/config"
	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/handler"
	"GCF/app/Datamanager/internal/server"
	"GCF/app/Datamanager/internal/svc"
	"GCF/pkg/servicex"

	"github.com/zeromicro/go-zero/rest"
	"google.golang.org/grpc"
)

func main() {
	log.SetFlags(log.Lshortfile | log.Ldate | log.Lmsgprefix)
	flag.Parse()

	var c config.Config
	servicex.MustLoadConfigFile(&c)
	svcCtx := svc.NewServiceContext(c)

	servicex.Main(
		&c,
		func(server *rest.Server) {
			handler.RegisterHandlers(server, svcCtx)
		},
		func(grpcServer *grpc.Server) {
			datamanager.RegisterDataServer(grpcServer, server.NewDataServer(svcCtx))
		},
	)
}
