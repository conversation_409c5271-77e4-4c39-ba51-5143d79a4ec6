// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type ControlDeviceRequest struct {
	DeviceUID string    `json:"deviceUID"`
	FrameInfo FrameInfo `json:"frameInfo"`
}

type ControlDeviceResponse struct {
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	FrameInfos       []FrameInfo      `json:"frameInfo"`
}

type CreateDeviceFrameRequest struct {
	DeviceUID  string      `json:"deviceUID"`
	FrameInfos []FrameInfo `json:"frameInfos"`
}

type CreateDeviceFrameResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type CreateDeviceResourceRequest struct {
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
}

type CreateDeviceResourceResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type DataDef struct {
	Index string `json:"index"`
	Name  string `json:"name"`
	Type  string `json:"type"`
	Unit  string `json:"unit"`
	Value string `json:"value,optional"`
	Desc  string `json:"desc,optional"`
}

type DataDefForEnum struct {
	Index string `json:"index"`
	Name  string `json:"name"`
	Type  string `json:"type"`
	Unit  string `json:"unit"`
	Desc  string `json:"desc,optional"`
}

type DeleteDeviceFrameRequest struct {
	DeviceUID   string      `json:"deviceUID"`
	FrameMetas  []FrameMeta `json:"frameMetas"`
	Description string      `json:"description,optional"` // 帧描述
}

type DeleteDeviceFrameResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	FrameInfos       []FrameInfo      `json:"frameInfos"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type DeleteDeviceResourceRequest struct {
	DeviceUID string `json:"deviceUID"`
}

type DeleteDeviceResourceResponse struct {
	DeviceMeta         DeviceMeta         `json:"deviceMeta"`
	DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
}

type DeviceMeta struct {
	DeviceUID   string      `json:"deviceUID"`
	FrameMetas  []FrameMeta `json:"frameMetas"`
	Description string      `json:"description,optional"`
	DeviceName  string      `json:"devicename,optional"`
}

type DeviceResourceInfo struct {
	IP          string      `json:"ip"`
	Type        string      `json:"type,options=fan|motor|virtual|pc|edge"`
	Protocol    []string    `json:"protocol,options=udp|modbus|uart"`
	DeviceName  string      `json:"devicename,optional"`
	OS          string      `json:"os,optional"`
	CPU         ResourceDef `json:"cpu,optional"`
	GPU         ResourceDef `json:"gpu,optional"`
	Disk        ResourceDef `json:"disk,optional"`
	Mem         ResourceDef `json:"mem,optional"`
	Description string      `json:"description,optional"`
}

type DeviceTypeEnumRequest struct {
}

type DeviceTypeEnumResponse struct {
	DeviceTypeEnum []string `json:"deviceTypeEnum"`
}

type DeviceWorkStatus struct {
	HealthStatus string `json:"healthStatus,options=init|ready|running|pending|error"`
	Timestamp    int64  `json:"timestamp"`
	WorkMode     string `json:"workMode,options=distributed|centralized"`
}

type FetchDeviceDataRequest struct {
	DeviceUID string `json:"deviceUID"`
}

type FetchDeviceDataResponse struct {
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	FrameInfos       []FrameInfo      `json:"frameInfos"`
}

type FrameInfo struct {
	FrameMeta FrameMeta `json:"frameMeta"`
	FrameLibs FrameLibs `json:"frameLibs"`
}

type FrameLibs struct {
	ModbusInfo ModbusInfo `json:"modbusInfo,optional"`
	UartInfo   UartInfo   `json:"uartInfo,optional"`
	UdpInfo    UdpInfo    `json:"udpInfo,optional"`
}

type FrameMeta struct {
	FrameUID         string `json:"frameUID,optional"`
	FrameType        string `json:"frameType,options=modbus|uart|udp"`
	FrameDescription string `json:"framedescription,optional"`
	FrameName        string `json:"frameName"`
}

type GetDeviceInfoRequest struct {
	DeviceUID string `json:"deviceUID"`
}

type GetDeviceInfoResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type GetFrameInfoRequest struct {
	DeviceUID  string      `json:"deviceUID"`
	FrameMetas []FrameMeta `json:"frameMetas"`
}

type GetFrameInfoResponse struct {
	FrameInfos []FrameInfo `json:"frameInfos"`
}

type ListDevice struct {
	DeviceMeta         DeviceMeta         `json:"deviceMeta"`
	DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
	FrameInfos         []FrameInfo        `json:"frameInfos"`
}

type ListDeviceRequest struct {
	DeviceUID    string      `json:"deviceUID,optional"`
	FrameMetas   []FrameMeta `json:"frameMetas,optional"`
	HealthStatus string      `json:"healthStatus,options=init|ready|running|pending|error,optional"`
	PageNum      int64       `json:"pageNum,optional"`
	PageSize     int64       `json:"pageSize,optional"`
}

type ListDeviceResponse struct {
	Devices  []ListDevice `json:"devices"`
	Total    int64        `json:"total"`
	PageNum  int64        `json:"pageNum"`
	PageSize int64        `json:"pageSize"`
}

type ModbusInfo struct {
	TID   string    `json:"tid"`
	PID   string    `json:"pid"`
	Len   string    `json:"len"`
	UID   string    `json:"uid"`
	FC    string    `json:"fc"`
	Datas []DataDef `json:"datas,optional"`
}

type ProtocolTypeEnumRequest struct {
}

type ProtocolTypeEnumResponse struct {
	Modbus           SubItemModbusInfo `json:"modbus"`
	Uart             SubItemUartInfo   `json:"uart"`
	Udp              SubItemUdpInfo    `json:"udp"`
	ProtocolTypeEnum []string          `json:"protocolTypeEnum"`
}

type ResourceDef struct {
	Amount int64  `json:"amount"`
	Type   string `json:"type"`
	Unit   string `json:"unit"`
}

type SubItemModbusInfo struct {
	TID   string           `json:"tid"`
	PID   string           `json:"pid"`
	Len   string           `json:"len"`
	UID   string           `json:"uid"`
	FC    string           `json:"fc"`
	Datas []DataDefForEnum `json:"datas"`
}

type SubItemUartInfo struct {
	Header string           `json:"header"`
	Addr   string           `json:"addr"`
	Cmd    string           `json:"cmd"`
	Tail   string           `json:"tail"`
	Datas  []DataDefForEnum `json:"datas"`
}

type SubItemUdpInfo struct {
	Type   string           `json:"type"`
	Header string           `json:"header"`
	TypeID string           `json:"typeID"`
	Datas  []DataDefForEnum `json:"datas"`
}

type UartInfo struct {
	Header string    `json:"header"`
	Addr   string    `json:"addr"`
	Cmd    string    `json:"cmd"`
	Tail   string    `json:"tail"`
	Datas  []DataDef `json:"datas,optional"`
}

type UdpInfo struct {
	Type   string    `json:"type"`
	Header string    `json:"header"`
	TypeID string    `json:"typeID"`
	Datas  []DataDef `json:"datas,optional"`
}

type UpdateDeviceResourceRequest struct {
	DeviceUID          string             `json:"deviceUID"`
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
}

type UpdateDeviceResourceResponse struct {
	DeviceMeta         DeviceMeta         `json:"deviceMeta"`
	DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
	FrameInfos         []FrameInfo        `json:"frameInfos"`
}

type UpdateFrameRequest struct {
	DeviceUID  string      `json:"deviceUID"`
	FrameInfos []FrameInfo `json:"frameInfos"`
}

type UpdateFrameResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}
