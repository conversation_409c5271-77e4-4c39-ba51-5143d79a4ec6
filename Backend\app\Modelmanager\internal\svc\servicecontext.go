package svc

import (
	"GCF/app/Modelmanager/internal/config"
	"GCF/app/Modelmanager/internal/ent"
	"fmt"

	_ "github.com/lib/pq"
	"github.com/zeromicro/go-zero/core/logx"
)

type ServiceContext struct {
	Config config.Config
	Db     *ent.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	entClient, err := ent.Open(c.Db.Driver, c.Db.MustGetSource())
	logx.Must(err)
	fmt.Printf("Connected to database, driver %s, source %s\n", c.Db.Driver, c.Db.MustGetSource())

	return &ServiceContext{
		Config: c,
		Db:     entClient,
	}

}
