package logic

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"GCF/app/Devicemanager/internal/ent"

	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetDeviceInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeviceInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceInfoLogic {
	return &GetDeviceInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeviceInfoLogic) GetDeviceInfo(req *types.GetDeviceInfoRequest) (*types.GetDeviceInfoResponse, error) {
	// 1. Query device
	queryDevice, err := l.svcCtx.Db.Device.Query().
		Where(device.IDEQ(req.DeviceUID)).
		First(l.ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, xerrors.New(http.StatusNotFound, fmt.Sprintf("设备 %s 未找到", req.DeviceUID))
		}
		l.Logger.Errorf("查询设备 %s 失败: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusServiceUnavailable, fmt.Sprintf("设备 %s 数据库无法访问: %v", req.DeviceUID, err))
	}

	// 2. Build protocol slice
	protocols := strings.Split(queryDevice.Protocol, ",")
	if len(protocols) == 0 || (len(protocols) == 1 && protocols[0] == "") {
		return nil, xerrors.New(http.StatusBadRequest, fmt.Sprintf("设备 %s 无可用协议", req.DeviceUID))
	}

	// 3. Load frame meta + frame info
	DeviceMeta, _, err := utils.GetFrameInfosAndDeviceMeta(
		l.ctx,
		l.svcCtx,
		protocols,
		req.DeviceUID,
	)
	if err != nil {
		l.Logger.Errorf("将帧信息载入设备 %s 时出错: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusServiceUnavailable, fmt.Sprintf("未能将帧信息载入设备 %s: %v", req.DeviceUID, err))
	}

	// 4. Load device work status
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		l.Logger.Errorf("将设备工作状态载入设备 %s 时出错: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusServiceUnavailable, fmt.Sprintf("未能将设备工作状态载入设备 %s: %v", req.DeviceUID, err))
	}

	// 5. Success
	return &types.GetDeviceInfoResponse{
		DeviceMeta:       *DeviceMeta,
		DeviceWorkStatus: *DeviceWorkStatus,
	}, nil
}
