// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeData         = "Data"
	TypeDevice       = "Device"
	TypeModbus       = "Modbus"
	TypeTestCreateDb = "Test_create_db"
	TypeUart         = "Uart"
	TypeUDP          = "Udp"
)

// DataMutation represents an operation that mutates the Data nodes in the graph.
type DataMutation struct {
	config
	op            Op
	typ           string
	id            *string
	frame_id      *string
	index         *string
	name          *string
	_type         *string
	unit          *string
	description   *string
	create_unix   *time.Time
	update_unix   *time.Time
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Data, error)
	predicates    []predicate.Data
}

var _ ent.Mutation = (*DataMutation)(nil)

// dataOption allows management of the mutation configuration using functional options.
type dataOption func(*DataMutation)

// newDataMutation creates new mutation for the Data entity.
func newDataMutation(c config, op Op, opts ...dataOption) *DataMutation {
	m := &DataMutation{
		config:        c,
		op:            op,
		typ:           TypeData,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDataID sets the ID field of the mutation.
func withDataID(id string) dataOption {
	return func(m *DataMutation) {
		var (
			err   error
			once  sync.Once
			value *Data
		)
		m.oldValue = func(ctx context.Context) (*Data, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Data.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withData sets the old Data of the mutation.
func withData(node *Data) dataOption {
	return func(m *DataMutation) {
		m.oldValue = func(context.Context) (*Data, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DataMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DataMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Data entities.
func (m *DataMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DataMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DataMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Data.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetFrameID sets the "frame_id" field.
func (m *DataMutation) SetFrameID(s string) {
	m.frame_id = &s
}

// FrameID returns the value of the "frame_id" field in the mutation.
func (m *DataMutation) FrameID() (r string, exists bool) {
	v := m.frame_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFrameID returns the old "frame_id" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldFrameID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFrameID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFrameID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFrameID: %w", err)
	}
	return oldValue.FrameID, nil
}

// ResetFrameID resets all changes to the "frame_id" field.
func (m *DataMutation) ResetFrameID() {
	m.frame_id = nil
}

// SetIndex sets the "index" field.
func (m *DataMutation) SetIndex(s string) {
	m.index = &s
}

// Index returns the value of the "index" field in the mutation.
func (m *DataMutation) Index() (r string, exists bool) {
	v := m.index
	if v == nil {
		return
	}
	return *v, true
}

// OldIndex returns the old "index" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldIndex(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIndex is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIndex requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIndex: %w", err)
	}
	return oldValue.Index, nil
}

// ResetIndex resets all changes to the "index" field.
func (m *DataMutation) ResetIndex() {
	m.index = nil
}

// SetName sets the "name" field.
func (m *DataMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *DataMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *DataMutation) ResetName() {
	m.name = nil
}

// SetType sets the "type" field.
func (m *DataMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *DataMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *DataMutation) ResetType() {
	m._type = nil
}

// SetUnit sets the "unit" field.
func (m *DataMutation) SetUnit(s string) {
	m.unit = &s
}

// Unit returns the value of the "unit" field in the mutation.
func (m *DataMutation) Unit() (r string, exists bool) {
	v := m.unit
	if v == nil {
		return
	}
	return *v, true
}

// OldUnit returns the old "unit" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldUnit(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUnit is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUnit requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUnit: %w", err)
	}
	return oldValue.Unit, nil
}

// ResetUnit resets all changes to the "unit" field.
func (m *DataMutation) ResetUnit() {
	m.unit = nil
}

// SetDescription sets the "description" field.
func (m *DataMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *DataMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *DataMutation) ResetDescription() {
	m.description = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *DataMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *DataMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *DataMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *DataMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *DataMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *DataMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// Where appends a list predicates to the DataMutation builder.
func (m *DataMutation) Where(ps ...predicate.Data) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DataMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DataMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Data, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DataMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DataMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Data).
func (m *DataMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DataMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.frame_id != nil {
		fields = append(fields, data.FieldFrameID)
	}
	if m.index != nil {
		fields = append(fields, data.FieldIndex)
	}
	if m.name != nil {
		fields = append(fields, data.FieldName)
	}
	if m._type != nil {
		fields = append(fields, data.FieldType)
	}
	if m.unit != nil {
		fields = append(fields, data.FieldUnit)
	}
	if m.description != nil {
		fields = append(fields, data.FieldDescription)
	}
	if m.create_unix != nil {
		fields = append(fields, data.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, data.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DataMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case data.FieldFrameID:
		return m.FrameID()
	case data.FieldIndex:
		return m.Index()
	case data.FieldName:
		return m.Name()
	case data.FieldType:
		return m.GetType()
	case data.FieldUnit:
		return m.Unit()
	case data.FieldDescription:
		return m.Description()
	case data.FieldCreateUnix:
		return m.CreateUnix()
	case data.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DataMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case data.FieldFrameID:
		return m.OldFrameID(ctx)
	case data.FieldIndex:
		return m.OldIndex(ctx)
	case data.FieldName:
		return m.OldName(ctx)
	case data.FieldType:
		return m.OldType(ctx)
	case data.FieldUnit:
		return m.OldUnit(ctx)
	case data.FieldDescription:
		return m.OldDescription(ctx)
	case data.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case data.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown Data field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DataMutation) SetField(name string, value ent.Value) error {
	switch name {
	case data.FieldFrameID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFrameID(v)
		return nil
	case data.FieldIndex:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIndex(v)
		return nil
	case data.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case data.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case data.FieldUnit:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUnit(v)
		return nil
	case data.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case data.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case data.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown Data field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DataMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DataMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DataMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Data numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DataMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DataMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DataMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Data nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DataMutation) ResetField(name string) error {
	switch name {
	case data.FieldFrameID:
		m.ResetFrameID()
		return nil
	case data.FieldIndex:
		m.ResetIndex()
		return nil
	case data.FieldName:
		m.ResetName()
		return nil
	case data.FieldType:
		m.ResetType()
		return nil
	case data.FieldUnit:
		m.ResetUnit()
		return nil
	case data.FieldDescription:
		m.ResetDescription()
		return nil
	case data.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case data.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown Data field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DataMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DataMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DataMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DataMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DataMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DataMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DataMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Data unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DataMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Data edge %s", name)
}

// DeviceMutation represents an operation that mutates the Device nodes in the graph.
type DeviceMutation struct {
	config
	op                  Op
	typ                 string
	id                  *string
	name                *string
	ip                  *string
	_type               *string
	os                  *string
	cpu                 *string
	gpu                 *string
	memory              *string
	disk                *string
	protocol            *string
	description         *string
	status              *string
	healthtimestamp     *time.Time
	workmode            *string
	create_unix         *time.Time
	update_unix         *time.Time
	clearedFields       map[string]struct{}
	modbusConfig        map[string]struct{}
	removedmodbusConfig map[string]struct{}
	clearedmodbusConfig bool
	uartConfig          map[string]struct{}
	removeduartConfig   map[string]struct{}
	cleareduartConfig   bool
	udpConfig           map[string]struct{}
	removedudpConfig    map[string]struct{}
	clearedudpConfig    bool
	done                bool
	oldValue            func(context.Context) (*Device, error)
	predicates          []predicate.Device
}

var _ ent.Mutation = (*DeviceMutation)(nil)

// deviceOption allows management of the mutation configuration using functional options.
type deviceOption func(*DeviceMutation)

// newDeviceMutation creates new mutation for the Device entity.
func newDeviceMutation(c config, op Op, opts ...deviceOption) *DeviceMutation {
	m := &DeviceMutation{
		config:        c,
		op:            op,
		typ:           TypeDevice,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDeviceID sets the ID field of the mutation.
func withDeviceID(id string) deviceOption {
	return func(m *DeviceMutation) {
		var (
			err   error
			once  sync.Once
			value *Device
		)
		m.oldValue = func(ctx context.Context) (*Device, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Device.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDevice sets the old Device of the mutation.
func withDevice(node *Device) deviceOption {
	return func(m *DeviceMutation) {
		m.oldValue = func(context.Context) (*Device, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DeviceMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DeviceMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Device entities.
func (m *DeviceMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DeviceMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DeviceMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Device.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *DeviceMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *DeviceMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *DeviceMutation) ResetName() {
	m.name = nil
}

// SetIP sets the "ip" field.
func (m *DeviceMutation) SetIP(s string) {
	m.ip = &s
}

// IP returns the value of the "ip" field in the mutation.
func (m *DeviceMutation) IP() (r string, exists bool) {
	v := m.ip
	if v == nil {
		return
	}
	return *v, true
}

// OldIP returns the old "ip" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldIP(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIP is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIP requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIP: %w", err)
	}
	return oldValue.IP, nil
}

// ResetIP resets all changes to the "ip" field.
func (m *DeviceMutation) ResetIP() {
	m.ip = nil
}

// SetType sets the "type" field.
func (m *DeviceMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *DeviceMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *DeviceMutation) ResetType() {
	m._type = nil
}

// SetOs sets the "os" field.
func (m *DeviceMutation) SetOs(s string) {
	m.os = &s
}

// Os returns the value of the "os" field in the mutation.
func (m *DeviceMutation) Os() (r string, exists bool) {
	v := m.os
	if v == nil {
		return
	}
	return *v, true
}

// OldOs returns the old "os" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldOs(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOs is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOs requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOs: %w", err)
	}
	return oldValue.Os, nil
}

// ResetOs resets all changes to the "os" field.
func (m *DeviceMutation) ResetOs() {
	m.os = nil
}

// SetCPU sets the "cpu" field.
func (m *DeviceMutation) SetCPU(s string) {
	m.cpu = &s
}

// CPU returns the value of the "cpu" field in the mutation.
func (m *DeviceMutation) CPU() (r string, exists bool) {
	v := m.cpu
	if v == nil {
		return
	}
	return *v, true
}

// OldCPU returns the old "cpu" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldCPU(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCPU is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCPU requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCPU: %w", err)
	}
	return oldValue.CPU, nil
}

// ResetCPU resets all changes to the "cpu" field.
func (m *DeviceMutation) ResetCPU() {
	m.cpu = nil
}

// SetGpu sets the "gpu" field.
func (m *DeviceMutation) SetGpu(s string) {
	m.gpu = &s
}

// Gpu returns the value of the "gpu" field in the mutation.
func (m *DeviceMutation) Gpu() (r string, exists bool) {
	v := m.gpu
	if v == nil {
		return
	}
	return *v, true
}

// OldGpu returns the old "gpu" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldGpu(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldGpu is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldGpu requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldGpu: %w", err)
	}
	return oldValue.Gpu, nil
}

// ResetGpu resets all changes to the "gpu" field.
func (m *DeviceMutation) ResetGpu() {
	m.gpu = nil
}

// SetMemory sets the "memory" field.
func (m *DeviceMutation) SetMemory(s string) {
	m.memory = &s
}

// Memory returns the value of the "memory" field in the mutation.
func (m *DeviceMutation) Memory() (r string, exists bool) {
	v := m.memory
	if v == nil {
		return
	}
	return *v, true
}

// OldMemory returns the old "memory" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldMemory(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMemory is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMemory requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMemory: %w", err)
	}
	return oldValue.Memory, nil
}

// ResetMemory resets all changes to the "memory" field.
func (m *DeviceMutation) ResetMemory() {
	m.memory = nil
}

// SetDisk sets the "disk" field.
func (m *DeviceMutation) SetDisk(s string) {
	m.disk = &s
}

// Disk returns the value of the "disk" field in the mutation.
func (m *DeviceMutation) Disk() (r string, exists bool) {
	v := m.disk
	if v == nil {
		return
	}
	return *v, true
}

// OldDisk returns the old "disk" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldDisk(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDisk is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDisk requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDisk: %w", err)
	}
	return oldValue.Disk, nil
}

// ResetDisk resets all changes to the "disk" field.
func (m *DeviceMutation) ResetDisk() {
	m.disk = nil
}

// SetProtocol sets the "protocol" field.
func (m *DeviceMutation) SetProtocol(s string) {
	m.protocol = &s
}

// Protocol returns the value of the "protocol" field in the mutation.
func (m *DeviceMutation) Protocol() (r string, exists bool) {
	v := m.protocol
	if v == nil {
		return
	}
	return *v, true
}

// OldProtocol returns the old "protocol" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldProtocol(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProtocol is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProtocol requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProtocol: %w", err)
	}
	return oldValue.Protocol, nil
}

// ResetProtocol resets all changes to the "protocol" field.
func (m *DeviceMutation) ResetProtocol() {
	m.protocol = nil
}

// SetDescription sets the "description" field.
func (m *DeviceMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *DeviceMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *DeviceMutation) ResetDescription() {
	m.description = nil
}

// SetStatus sets the "status" field.
func (m *DeviceMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *DeviceMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *DeviceMutation) ResetStatus() {
	m.status = nil
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (m *DeviceMutation) SetHealthtimestamp(t time.Time) {
	m.healthtimestamp = &t
}

// Healthtimestamp returns the value of the "healthtimestamp" field in the mutation.
func (m *DeviceMutation) Healthtimestamp() (r time.Time, exists bool) {
	v := m.healthtimestamp
	if v == nil {
		return
	}
	return *v, true
}

// OldHealthtimestamp returns the old "healthtimestamp" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldHealthtimestamp(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHealthtimestamp is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHealthtimestamp requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHealthtimestamp: %w", err)
	}
	return oldValue.Healthtimestamp, nil
}

// ResetHealthtimestamp resets all changes to the "healthtimestamp" field.
func (m *DeviceMutation) ResetHealthtimestamp() {
	m.healthtimestamp = nil
}

// SetWorkmode sets the "workmode" field.
func (m *DeviceMutation) SetWorkmode(s string) {
	m.workmode = &s
}

// Workmode returns the value of the "workmode" field in the mutation.
func (m *DeviceMutation) Workmode() (r string, exists bool) {
	v := m.workmode
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkmode returns the old "workmode" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldWorkmode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkmode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkmode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkmode: %w", err)
	}
	return oldValue.Workmode, nil
}

// ResetWorkmode resets all changes to the "workmode" field.
func (m *DeviceMutation) ResetWorkmode() {
	m.workmode = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *DeviceMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *DeviceMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *DeviceMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *DeviceMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *DeviceMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the Device entity.
// If the Device object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *DeviceMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// AddModbusConfigIDs adds the "modbusConfig" edge to the Modbus entity by ids.
func (m *DeviceMutation) AddModbusConfigIDs(ids ...string) {
	if m.modbusConfig == nil {
		m.modbusConfig = make(map[string]struct{})
	}
	for i := range ids {
		m.modbusConfig[ids[i]] = struct{}{}
	}
}

// ClearModbusConfig clears the "modbusConfig" edge to the Modbus entity.
func (m *DeviceMutation) ClearModbusConfig() {
	m.clearedmodbusConfig = true
}

// ModbusConfigCleared reports if the "modbusConfig" edge to the Modbus entity was cleared.
func (m *DeviceMutation) ModbusConfigCleared() bool {
	return m.clearedmodbusConfig
}

// RemoveModbusConfigIDs removes the "modbusConfig" edge to the Modbus entity by IDs.
func (m *DeviceMutation) RemoveModbusConfigIDs(ids ...string) {
	if m.removedmodbusConfig == nil {
		m.removedmodbusConfig = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.modbusConfig, ids[i])
		m.removedmodbusConfig[ids[i]] = struct{}{}
	}
}

// RemovedModbusConfig returns the removed IDs of the "modbusConfig" edge to the Modbus entity.
func (m *DeviceMutation) RemovedModbusConfigIDs() (ids []string) {
	for id := range m.removedmodbusConfig {
		ids = append(ids, id)
	}
	return
}

// ModbusConfigIDs returns the "modbusConfig" edge IDs in the mutation.
func (m *DeviceMutation) ModbusConfigIDs() (ids []string) {
	for id := range m.modbusConfig {
		ids = append(ids, id)
	}
	return
}

// ResetModbusConfig resets all changes to the "modbusConfig" edge.
func (m *DeviceMutation) ResetModbusConfig() {
	m.modbusConfig = nil
	m.clearedmodbusConfig = false
	m.removedmodbusConfig = nil
}

// AddUartConfigIDs adds the "uartConfig" edge to the Uart entity by ids.
func (m *DeviceMutation) AddUartConfigIDs(ids ...string) {
	if m.uartConfig == nil {
		m.uartConfig = make(map[string]struct{})
	}
	for i := range ids {
		m.uartConfig[ids[i]] = struct{}{}
	}
}

// ClearUartConfig clears the "uartConfig" edge to the Uart entity.
func (m *DeviceMutation) ClearUartConfig() {
	m.cleareduartConfig = true
}

// UartConfigCleared reports if the "uartConfig" edge to the Uart entity was cleared.
func (m *DeviceMutation) UartConfigCleared() bool {
	return m.cleareduartConfig
}

// RemoveUartConfigIDs removes the "uartConfig" edge to the Uart entity by IDs.
func (m *DeviceMutation) RemoveUartConfigIDs(ids ...string) {
	if m.removeduartConfig == nil {
		m.removeduartConfig = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.uartConfig, ids[i])
		m.removeduartConfig[ids[i]] = struct{}{}
	}
}

// RemovedUartConfig returns the removed IDs of the "uartConfig" edge to the Uart entity.
func (m *DeviceMutation) RemovedUartConfigIDs() (ids []string) {
	for id := range m.removeduartConfig {
		ids = append(ids, id)
	}
	return
}

// UartConfigIDs returns the "uartConfig" edge IDs in the mutation.
func (m *DeviceMutation) UartConfigIDs() (ids []string) {
	for id := range m.uartConfig {
		ids = append(ids, id)
	}
	return
}

// ResetUartConfig resets all changes to the "uartConfig" edge.
func (m *DeviceMutation) ResetUartConfig() {
	m.uartConfig = nil
	m.cleareduartConfig = false
	m.removeduartConfig = nil
}

// AddUdpConfigIDs adds the "udpConfig" edge to the Udp entity by ids.
func (m *DeviceMutation) AddUdpConfigIDs(ids ...string) {
	if m.udpConfig == nil {
		m.udpConfig = make(map[string]struct{})
	}
	for i := range ids {
		m.udpConfig[ids[i]] = struct{}{}
	}
}

// ClearUdpConfig clears the "udpConfig" edge to the Udp entity.
func (m *DeviceMutation) ClearUdpConfig() {
	m.clearedudpConfig = true
}

// UdpConfigCleared reports if the "udpConfig" edge to the Udp entity was cleared.
func (m *DeviceMutation) UdpConfigCleared() bool {
	return m.clearedudpConfig
}

// RemoveUdpConfigIDs removes the "udpConfig" edge to the Udp entity by IDs.
func (m *DeviceMutation) RemoveUdpConfigIDs(ids ...string) {
	if m.removedudpConfig == nil {
		m.removedudpConfig = make(map[string]struct{})
	}
	for i := range ids {
		delete(m.udpConfig, ids[i])
		m.removedudpConfig[ids[i]] = struct{}{}
	}
}

// RemovedUdpConfig returns the removed IDs of the "udpConfig" edge to the Udp entity.
func (m *DeviceMutation) RemovedUdpConfigIDs() (ids []string) {
	for id := range m.removedudpConfig {
		ids = append(ids, id)
	}
	return
}

// UdpConfigIDs returns the "udpConfig" edge IDs in the mutation.
func (m *DeviceMutation) UdpConfigIDs() (ids []string) {
	for id := range m.udpConfig {
		ids = append(ids, id)
	}
	return
}

// ResetUdpConfig resets all changes to the "udpConfig" edge.
func (m *DeviceMutation) ResetUdpConfig() {
	m.udpConfig = nil
	m.clearedudpConfig = false
	m.removedudpConfig = nil
}

// Where appends a list predicates to the DeviceMutation builder.
func (m *DeviceMutation) Where(ps ...predicate.Device) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DeviceMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DeviceMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Device, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DeviceMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DeviceMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Device).
func (m *DeviceMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DeviceMutation) Fields() []string {
	fields := make([]string, 0, 15)
	if m.name != nil {
		fields = append(fields, device.FieldName)
	}
	if m.ip != nil {
		fields = append(fields, device.FieldIP)
	}
	if m._type != nil {
		fields = append(fields, device.FieldType)
	}
	if m.os != nil {
		fields = append(fields, device.FieldOs)
	}
	if m.cpu != nil {
		fields = append(fields, device.FieldCPU)
	}
	if m.gpu != nil {
		fields = append(fields, device.FieldGpu)
	}
	if m.memory != nil {
		fields = append(fields, device.FieldMemory)
	}
	if m.disk != nil {
		fields = append(fields, device.FieldDisk)
	}
	if m.protocol != nil {
		fields = append(fields, device.FieldProtocol)
	}
	if m.description != nil {
		fields = append(fields, device.FieldDescription)
	}
	if m.status != nil {
		fields = append(fields, device.FieldStatus)
	}
	if m.healthtimestamp != nil {
		fields = append(fields, device.FieldHealthtimestamp)
	}
	if m.workmode != nil {
		fields = append(fields, device.FieldWorkmode)
	}
	if m.create_unix != nil {
		fields = append(fields, device.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, device.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DeviceMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case device.FieldName:
		return m.Name()
	case device.FieldIP:
		return m.IP()
	case device.FieldType:
		return m.GetType()
	case device.FieldOs:
		return m.Os()
	case device.FieldCPU:
		return m.CPU()
	case device.FieldGpu:
		return m.Gpu()
	case device.FieldMemory:
		return m.Memory()
	case device.FieldDisk:
		return m.Disk()
	case device.FieldProtocol:
		return m.Protocol()
	case device.FieldDescription:
		return m.Description()
	case device.FieldStatus:
		return m.Status()
	case device.FieldHealthtimestamp:
		return m.Healthtimestamp()
	case device.FieldWorkmode:
		return m.Workmode()
	case device.FieldCreateUnix:
		return m.CreateUnix()
	case device.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DeviceMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case device.FieldName:
		return m.OldName(ctx)
	case device.FieldIP:
		return m.OldIP(ctx)
	case device.FieldType:
		return m.OldType(ctx)
	case device.FieldOs:
		return m.OldOs(ctx)
	case device.FieldCPU:
		return m.OldCPU(ctx)
	case device.FieldGpu:
		return m.OldGpu(ctx)
	case device.FieldMemory:
		return m.OldMemory(ctx)
	case device.FieldDisk:
		return m.OldDisk(ctx)
	case device.FieldProtocol:
		return m.OldProtocol(ctx)
	case device.FieldDescription:
		return m.OldDescription(ctx)
	case device.FieldStatus:
		return m.OldStatus(ctx)
	case device.FieldHealthtimestamp:
		return m.OldHealthtimestamp(ctx)
	case device.FieldWorkmode:
		return m.OldWorkmode(ctx)
	case device.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case device.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown Device field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DeviceMutation) SetField(name string, value ent.Value) error {
	switch name {
	case device.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case device.FieldIP:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIP(v)
		return nil
	case device.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case device.FieldOs:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOs(v)
		return nil
	case device.FieldCPU:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCPU(v)
		return nil
	case device.FieldGpu:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetGpu(v)
		return nil
	case device.FieldMemory:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMemory(v)
		return nil
	case device.FieldDisk:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDisk(v)
		return nil
	case device.FieldProtocol:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProtocol(v)
		return nil
	case device.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case device.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case device.FieldHealthtimestamp:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHealthtimestamp(v)
		return nil
	case device.FieldWorkmode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkmode(v)
		return nil
	case device.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case device.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown Device field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DeviceMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DeviceMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DeviceMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Device numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DeviceMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DeviceMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DeviceMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Device nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DeviceMutation) ResetField(name string) error {
	switch name {
	case device.FieldName:
		m.ResetName()
		return nil
	case device.FieldIP:
		m.ResetIP()
		return nil
	case device.FieldType:
		m.ResetType()
		return nil
	case device.FieldOs:
		m.ResetOs()
		return nil
	case device.FieldCPU:
		m.ResetCPU()
		return nil
	case device.FieldGpu:
		m.ResetGpu()
		return nil
	case device.FieldMemory:
		m.ResetMemory()
		return nil
	case device.FieldDisk:
		m.ResetDisk()
		return nil
	case device.FieldProtocol:
		m.ResetProtocol()
		return nil
	case device.FieldDescription:
		m.ResetDescription()
		return nil
	case device.FieldStatus:
		m.ResetStatus()
		return nil
	case device.FieldHealthtimestamp:
		m.ResetHealthtimestamp()
		return nil
	case device.FieldWorkmode:
		m.ResetWorkmode()
		return nil
	case device.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case device.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown Device field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DeviceMutation) AddedEdges() []string {
	edges := make([]string, 0, 3)
	if m.modbusConfig != nil {
		edges = append(edges, device.EdgeModbusConfig)
	}
	if m.uartConfig != nil {
		edges = append(edges, device.EdgeUartConfig)
	}
	if m.udpConfig != nil {
		edges = append(edges, device.EdgeUdpConfig)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DeviceMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case device.EdgeModbusConfig:
		ids := make([]ent.Value, 0, len(m.modbusConfig))
		for id := range m.modbusConfig {
			ids = append(ids, id)
		}
		return ids
	case device.EdgeUartConfig:
		ids := make([]ent.Value, 0, len(m.uartConfig))
		for id := range m.uartConfig {
			ids = append(ids, id)
		}
		return ids
	case device.EdgeUdpConfig:
		ids := make([]ent.Value, 0, len(m.udpConfig))
		for id := range m.udpConfig {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DeviceMutation) RemovedEdges() []string {
	edges := make([]string, 0, 3)
	if m.removedmodbusConfig != nil {
		edges = append(edges, device.EdgeModbusConfig)
	}
	if m.removeduartConfig != nil {
		edges = append(edges, device.EdgeUartConfig)
	}
	if m.removedudpConfig != nil {
		edges = append(edges, device.EdgeUdpConfig)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DeviceMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case device.EdgeModbusConfig:
		ids := make([]ent.Value, 0, len(m.removedmodbusConfig))
		for id := range m.removedmodbusConfig {
			ids = append(ids, id)
		}
		return ids
	case device.EdgeUartConfig:
		ids := make([]ent.Value, 0, len(m.removeduartConfig))
		for id := range m.removeduartConfig {
			ids = append(ids, id)
		}
		return ids
	case device.EdgeUdpConfig:
		ids := make([]ent.Value, 0, len(m.removedudpConfig))
		for id := range m.removedudpConfig {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DeviceMutation) ClearedEdges() []string {
	edges := make([]string, 0, 3)
	if m.clearedmodbusConfig {
		edges = append(edges, device.EdgeModbusConfig)
	}
	if m.cleareduartConfig {
		edges = append(edges, device.EdgeUartConfig)
	}
	if m.clearedudpConfig {
		edges = append(edges, device.EdgeUdpConfig)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DeviceMutation) EdgeCleared(name string) bool {
	switch name {
	case device.EdgeModbusConfig:
		return m.clearedmodbusConfig
	case device.EdgeUartConfig:
		return m.cleareduartConfig
	case device.EdgeUdpConfig:
		return m.clearedudpConfig
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DeviceMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Device unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DeviceMutation) ResetEdge(name string) error {
	switch name {
	case device.EdgeModbusConfig:
		m.ResetModbusConfig()
		return nil
	case device.EdgeUartConfig:
		m.ResetUartConfig()
		return nil
	case device.EdgeUdpConfig:
		m.ResetUdpConfig()
		return nil
	}
	return fmt.Errorf("unknown Device edge %s", name)
}

// ModbusMutation represents an operation that mutates the Modbus nodes in the graph.
type ModbusMutation struct {
	config
	op            Op
	typ           string
	id            *string
	tid           *string
	pid           *string
	len           *string
	uid           *string
	fc            *string
	description   *string
	name          *string
	create_unix   *time.Time
	update_unix   *time.Time
	clearedFields map[string]struct{}
	device        *string
	cleareddevice bool
	done          bool
	oldValue      func(context.Context) (*Modbus, error)
	predicates    []predicate.Modbus
}

var _ ent.Mutation = (*ModbusMutation)(nil)

// modbusOption allows management of the mutation configuration using functional options.
type modbusOption func(*ModbusMutation)

// newModbusMutation creates new mutation for the Modbus entity.
func newModbusMutation(c config, op Op, opts ...modbusOption) *ModbusMutation {
	m := &ModbusMutation{
		config:        c,
		op:            op,
		typ:           TypeModbus,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withModbusID sets the ID field of the mutation.
func withModbusID(id string) modbusOption {
	return func(m *ModbusMutation) {
		var (
			err   error
			once  sync.Once
			value *Modbus
		)
		m.oldValue = func(ctx context.Context) (*Modbus, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Modbus.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withModbus sets the old Modbus of the mutation.
func withModbus(node *Modbus) modbusOption {
	return func(m *ModbusMutation) {
		m.oldValue = func(context.Context) (*Modbus, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ModbusMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ModbusMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Modbus entities.
func (m *ModbusMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ModbusMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ModbusMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Modbus.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetDeviceID sets the "device_id" field.
func (m *ModbusMutation) SetDeviceID(s string) {
	m.device = &s
}

// DeviceID returns the value of the "device_id" field in the mutation.
func (m *ModbusMutation) DeviceID() (r string, exists bool) {
	v := m.device
	if v == nil {
		return
	}
	return *v, true
}

// OldDeviceID returns the old "device_id" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldDeviceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeviceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeviceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeviceID: %w", err)
	}
	return oldValue.DeviceID, nil
}

// ResetDeviceID resets all changes to the "device_id" field.
func (m *ModbusMutation) ResetDeviceID() {
	m.device = nil
}

// SetTid sets the "tid" field.
func (m *ModbusMutation) SetTid(s string) {
	m.tid = &s
}

// Tid returns the value of the "tid" field in the mutation.
func (m *ModbusMutation) Tid() (r string, exists bool) {
	v := m.tid
	if v == nil {
		return
	}
	return *v, true
}

// OldTid returns the old "tid" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldTid(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTid is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTid requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTid: %w", err)
	}
	return oldValue.Tid, nil
}

// ResetTid resets all changes to the "tid" field.
func (m *ModbusMutation) ResetTid() {
	m.tid = nil
}

// SetPid sets the "pid" field.
func (m *ModbusMutation) SetPid(s string) {
	m.pid = &s
}

// Pid returns the value of the "pid" field in the mutation.
func (m *ModbusMutation) Pid() (r string, exists bool) {
	v := m.pid
	if v == nil {
		return
	}
	return *v, true
}

// OldPid returns the old "pid" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldPid(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPid is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPid requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPid: %w", err)
	}
	return oldValue.Pid, nil
}

// ResetPid resets all changes to the "pid" field.
func (m *ModbusMutation) ResetPid() {
	m.pid = nil
}

// SetLen sets the "len" field.
func (m *ModbusMutation) SetLen(s string) {
	m.len = &s
}

// Len returns the value of the "len" field in the mutation.
func (m *ModbusMutation) Len() (r string, exists bool) {
	v := m.len
	if v == nil {
		return
	}
	return *v, true
}

// OldLen returns the old "len" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldLen(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLen is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLen requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLen: %w", err)
	}
	return oldValue.Len, nil
}

// ResetLen resets all changes to the "len" field.
func (m *ModbusMutation) ResetLen() {
	m.len = nil
}

// SetUID sets the "uid" field.
func (m *ModbusMutation) SetUID(s string) {
	m.uid = &s
}

// UID returns the value of the "uid" field in the mutation.
func (m *ModbusMutation) UID() (r string, exists bool) {
	v := m.uid
	if v == nil {
		return
	}
	return *v, true
}

// OldUID returns the old "uid" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldUID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUID: %w", err)
	}
	return oldValue.UID, nil
}

// ResetUID resets all changes to the "uid" field.
func (m *ModbusMutation) ResetUID() {
	m.uid = nil
}

// SetFc sets the "fc" field.
func (m *ModbusMutation) SetFc(s string) {
	m.fc = &s
}

// Fc returns the value of the "fc" field in the mutation.
func (m *ModbusMutation) Fc() (r string, exists bool) {
	v := m.fc
	if v == nil {
		return
	}
	return *v, true
}

// OldFc returns the old "fc" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldFc(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFc is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFc requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFc: %w", err)
	}
	return oldValue.Fc, nil
}

// ResetFc resets all changes to the "fc" field.
func (m *ModbusMutation) ResetFc() {
	m.fc = nil
}

// SetDescription sets the "description" field.
func (m *ModbusMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *ModbusMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *ModbusMutation) ResetDescription() {
	m.description = nil
}

// SetName sets the "name" field.
func (m *ModbusMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *ModbusMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *ModbusMutation) ResetName() {
	m.name = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *ModbusMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *ModbusMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *ModbusMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *ModbusMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *ModbusMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the Modbus entity.
// If the Modbus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModbusMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *ModbusMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// ClearDevice clears the "device" edge to the Device entity.
func (m *ModbusMutation) ClearDevice() {
	m.cleareddevice = true
	m.clearedFields[modbus.FieldDeviceID] = struct{}{}
}

// DeviceCleared reports if the "device" edge to the Device entity was cleared.
func (m *ModbusMutation) DeviceCleared() bool {
	return m.cleareddevice
}

// DeviceIDs returns the "device" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// DeviceID instead. It exists only for internal usage by the builders.
func (m *ModbusMutation) DeviceIDs() (ids []string) {
	if id := m.device; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetDevice resets all changes to the "device" edge.
func (m *ModbusMutation) ResetDevice() {
	m.device = nil
	m.cleareddevice = false
}

// Where appends a list predicates to the ModbusMutation builder.
func (m *ModbusMutation) Where(ps ...predicate.Modbus) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ModbusMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ModbusMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Modbus, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ModbusMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ModbusMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Modbus).
func (m *ModbusMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ModbusMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.device != nil {
		fields = append(fields, modbus.FieldDeviceID)
	}
	if m.tid != nil {
		fields = append(fields, modbus.FieldTid)
	}
	if m.pid != nil {
		fields = append(fields, modbus.FieldPid)
	}
	if m.len != nil {
		fields = append(fields, modbus.FieldLen)
	}
	if m.uid != nil {
		fields = append(fields, modbus.FieldUID)
	}
	if m.fc != nil {
		fields = append(fields, modbus.FieldFc)
	}
	if m.description != nil {
		fields = append(fields, modbus.FieldDescription)
	}
	if m.name != nil {
		fields = append(fields, modbus.FieldName)
	}
	if m.create_unix != nil {
		fields = append(fields, modbus.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, modbus.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ModbusMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case modbus.FieldDeviceID:
		return m.DeviceID()
	case modbus.FieldTid:
		return m.Tid()
	case modbus.FieldPid:
		return m.Pid()
	case modbus.FieldLen:
		return m.Len()
	case modbus.FieldUID:
		return m.UID()
	case modbus.FieldFc:
		return m.Fc()
	case modbus.FieldDescription:
		return m.Description()
	case modbus.FieldName:
		return m.Name()
	case modbus.FieldCreateUnix:
		return m.CreateUnix()
	case modbus.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ModbusMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case modbus.FieldDeviceID:
		return m.OldDeviceID(ctx)
	case modbus.FieldTid:
		return m.OldTid(ctx)
	case modbus.FieldPid:
		return m.OldPid(ctx)
	case modbus.FieldLen:
		return m.OldLen(ctx)
	case modbus.FieldUID:
		return m.OldUID(ctx)
	case modbus.FieldFc:
		return m.OldFc(ctx)
	case modbus.FieldDescription:
		return m.OldDescription(ctx)
	case modbus.FieldName:
		return m.OldName(ctx)
	case modbus.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case modbus.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown Modbus field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ModbusMutation) SetField(name string, value ent.Value) error {
	switch name {
	case modbus.FieldDeviceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeviceID(v)
		return nil
	case modbus.FieldTid:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTid(v)
		return nil
	case modbus.FieldPid:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPid(v)
		return nil
	case modbus.FieldLen:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLen(v)
		return nil
	case modbus.FieldUID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUID(v)
		return nil
	case modbus.FieldFc:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFc(v)
		return nil
	case modbus.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case modbus.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case modbus.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case modbus.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown Modbus field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ModbusMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ModbusMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ModbusMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Modbus numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ModbusMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ModbusMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ModbusMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Modbus nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ModbusMutation) ResetField(name string) error {
	switch name {
	case modbus.FieldDeviceID:
		m.ResetDeviceID()
		return nil
	case modbus.FieldTid:
		m.ResetTid()
		return nil
	case modbus.FieldPid:
		m.ResetPid()
		return nil
	case modbus.FieldLen:
		m.ResetLen()
		return nil
	case modbus.FieldUID:
		m.ResetUID()
		return nil
	case modbus.FieldFc:
		m.ResetFc()
		return nil
	case modbus.FieldDescription:
		m.ResetDescription()
		return nil
	case modbus.FieldName:
		m.ResetName()
		return nil
	case modbus.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case modbus.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown Modbus field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ModbusMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.device != nil {
		edges = append(edges, modbus.EdgeDevice)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ModbusMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case modbus.EdgeDevice:
		if id := m.device; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ModbusMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ModbusMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ModbusMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cleareddevice {
		edges = append(edges, modbus.EdgeDevice)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ModbusMutation) EdgeCleared(name string) bool {
	switch name {
	case modbus.EdgeDevice:
		return m.cleareddevice
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ModbusMutation) ClearEdge(name string) error {
	switch name {
	case modbus.EdgeDevice:
		m.ClearDevice()
		return nil
	}
	return fmt.Errorf("unknown Modbus unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ModbusMutation) ResetEdge(name string) error {
	switch name {
	case modbus.EdgeDevice:
		m.ResetDevice()
		return nil
	}
	return fmt.Errorf("unknown Modbus edge %s", name)
}

// TestCreateDbMutation represents an operation that mutates the Test_create_db nodes in the graph.
type TestCreateDbMutation struct {
	config
	op            Op
	typ           string
	id            *int
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Test_create_db, error)
	predicates    []predicate.Test_create_db
}

var _ ent.Mutation = (*TestCreateDbMutation)(nil)

// testCreateDbOption allows management of the mutation configuration using functional options.
type testCreateDbOption func(*TestCreateDbMutation)

// newTestCreateDbMutation creates new mutation for the Test_create_db entity.
func newTestCreateDbMutation(c config, op Op, opts ...testCreateDbOption) *TestCreateDbMutation {
	m := &TestCreateDbMutation{
		config:        c,
		op:            op,
		typ:           TypeTestCreateDb,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTest_create_dbID sets the ID field of the mutation.
func withTest_create_dbID(id int) testCreateDbOption {
	return func(m *TestCreateDbMutation) {
		var (
			err   error
			once  sync.Once
			value *Test_create_db
		)
		m.oldValue = func(ctx context.Context) (*Test_create_db, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Test_create_db.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTest_create_db sets the old Test_create_db of the mutation.
func withTest_create_db(node *Test_create_db) testCreateDbOption {
	return func(m *TestCreateDbMutation) {
		m.oldValue = func(context.Context) (*Test_create_db, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TestCreateDbMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TestCreateDbMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TestCreateDbMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TestCreateDbMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Test_create_db.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the TestCreateDbMutation builder.
func (m *TestCreateDbMutation) Where(ps ...predicate.Test_create_db) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TestCreateDbMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TestCreateDbMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Test_create_db, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TestCreateDbMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TestCreateDbMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Test_create_db).
func (m *TestCreateDbMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TestCreateDbMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TestCreateDbMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TestCreateDbMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Test_create_db field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TestCreateDbMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Test_create_db field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TestCreateDbMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TestCreateDbMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TestCreateDbMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Test_create_db numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TestCreateDbMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TestCreateDbMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TestCreateDbMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Test_create_db nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TestCreateDbMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Test_create_db field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TestCreateDbMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TestCreateDbMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TestCreateDbMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TestCreateDbMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TestCreateDbMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TestCreateDbMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TestCreateDbMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Test_create_db unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TestCreateDbMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Test_create_db edge %s", name)
}

// UartMutation represents an operation that mutates the Uart nodes in the graph.
type UartMutation struct {
	config
	op            Op
	typ           string
	id            *string
	header        *string
	addr          *string
	cmd           *string
	tail          *string
	description   *string
	name          *string
	create_unix   *time.Time
	update_unix   *time.Time
	clearedFields map[string]struct{}
	device        *string
	cleareddevice bool
	done          bool
	oldValue      func(context.Context) (*Uart, error)
	predicates    []predicate.Uart
}

var _ ent.Mutation = (*UartMutation)(nil)

// uartOption allows management of the mutation configuration using functional options.
type uartOption func(*UartMutation)

// newUartMutation creates new mutation for the Uart entity.
func newUartMutation(c config, op Op, opts ...uartOption) *UartMutation {
	m := &UartMutation{
		config:        c,
		op:            op,
		typ:           TypeUart,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUartID sets the ID field of the mutation.
func withUartID(id string) uartOption {
	return func(m *UartMutation) {
		var (
			err   error
			once  sync.Once
			value *Uart
		)
		m.oldValue = func(ctx context.Context) (*Uart, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Uart.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUart sets the old Uart of the mutation.
func withUart(node *Uart) uartOption {
	return func(m *UartMutation) {
		m.oldValue = func(context.Context) (*Uart, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UartMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UartMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Uart entities.
func (m *UartMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UartMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UartMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Uart.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetDeviceID sets the "device_id" field.
func (m *UartMutation) SetDeviceID(s string) {
	m.device = &s
}

// DeviceID returns the value of the "device_id" field in the mutation.
func (m *UartMutation) DeviceID() (r string, exists bool) {
	v := m.device
	if v == nil {
		return
	}
	return *v, true
}

// OldDeviceID returns the old "device_id" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldDeviceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeviceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeviceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeviceID: %w", err)
	}
	return oldValue.DeviceID, nil
}

// ResetDeviceID resets all changes to the "device_id" field.
func (m *UartMutation) ResetDeviceID() {
	m.device = nil
}

// SetHeader sets the "header" field.
func (m *UartMutation) SetHeader(s string) {
	m.header = &s
}

// Header returns the value of the "header" field in the mutation.
func (m *UartMutation) Header() (r string, exists bool) {
	v := m.header
	if v == nil {
		return
	}
	return *v, true
}

// OldHeader returns the old "header" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldHeader(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHeader is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHeader requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHeader: %w", err)
	}
	return oldValue.Header, nil
}

// ResetHeader resets all changes to the "header" field.
func (m *UartMutation) ResetHeader() {
	m.header = nil
}

// SetAddr sets the "addr" field.
func (m *UartMutation) SetAddr(s string) {
	m.addr = &s
}

// Addr returns the value of the "addr" field in the mutation.
func (m *UartMutation) Addr() (r string, exists bool) {
	v := m.addr
	if v == nil {
		return
	}
	return *v, true
}

// OldAddr returns the old "addr" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldAddr(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAddr is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAddr requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAddr: %w", err)
	}
	return oldValue.Addr, nil
}

// ResetAddr resets all changes to the "addr" field.
func (m *UartMutation) ResetAddr() {
	m.addr = nil
}

// SetCmd sets the "cmd" field.
func (m *UartMutation) SetCmd(s string) {
	m.cmd = &s
}

// Cmd returns the value of the "cmd" field in the mutation.
func (m *UartMutation) Cmd() (r string, exists bool) {
	v := m.cmd
	if v == nil {
		return
	}
	return *v, true
}

// OldCmd returns the old "cmd" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldCmd(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCmd is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCmd requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCmd: %w", err)
	}
	return oldValue.Cmd, nil
}

// ResetCmd resets all changes to the "cmd" field.
func (m *UartMutation) ResetCmd() {
	m.cmd = nil
}

// SetTail sets the "tail" field.
func (m *UartMutation) SetTail(s string) {
	m.tail = &s
}

// Tail returns the value of the "tail" field in the mutation.
func (m *UartMutation) Tail() (r string, exists bool) {
	v := m.tail
	if v == nil {
		return
	}
	return *v, true
}

// OldTail returns the old "tail" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldTail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTail: %w", err)
	}
	return oldValue.Tail, nil
}

// ResetTail resets all changes to the "tail" field.
func (m *UartMutation) ResetTail() {
	m.tail = nil
}

// SetDescription sets the "description" field.
func (m *UartMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *UartMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *UartMutation) ResetDescription() {
	m.description = nil
}

// SetName sets the "name" field.
func (m *UartMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *UartMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *UartMutation) ResetName() {
	m.name = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *UartMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *UartMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *UartMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *UartMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *UartMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the Uart entity.
// If the Uart object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UartMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *UartMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// ClearDevice clears the "device" edge to the Device entity.
func (m *UartMutation) ClearDevice() {
	m.cleareddevice = true
	m.clearedFields[uart.FieldDeviceID] = struct{}{}
}

// DeviceCleared reports if the "device" edge to the Device entity was cleared.
func (m *UartMutation) DeviceCleared() bool {
	return m.cleareddevice
}

// DeviceIDs returns the "device" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// DeviceID instead. It exists only for internal usage by the builders.
func (m *UartMutation) DeviceIDs() (ids []string) {
	if id := m.device; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetDevice resets all changes to the "device" edge.
func (m *UartMutation) ResetDevice() {
	m.device = nil
	m.cleareddevice = false
}

// Where appends a list predicates to the UartMutation builder.
func (m *UartMutation) Where(ps ...predicate.Uart) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UartMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UartMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Uart, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UartMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UartMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Uart).
func (m *UartMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UartMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.device != nil {
		fields = append(fields, uart.FieldDeviceID)
	}
	if m.header != nil {
		fields = append(fields, uart.FieldHeader)
	}
	if m.addr != nil {
		fields = append(fields, uart.FieldAddr)
	}
	if m.cmd != nil {
		fields = append(fields, uart.FieldCmd)
	}
	if m.tail != nil {
		fields = append(fields, uart.FieldTail)
	}
	if m.description != nil {
		fields = append(fields, uart.FieldDescription)
	}
	if m.name != nil {
		fields = append(fields, uart.FieldName)
	}
	if m.create_unix != nil {
		fields = append(fields, uart.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, uart.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UartMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case uart.FieldDeviceID:
		return m.DeviceID()
	case uart.FieldHeader:
		return m.Header()
	case uart.FieldAddr:
		return m.Addr()
	case uart.FieldCmd:
		return m.Cmd()
	case uart.FieldTail:
		return m.Tail()
	case uart.FieldDescription:
		return m.Description()
	case uart.FieldName:
		return m.Name()
	case uart.FieldCreateUnix:
		return m.CreateUnix()
	case uart.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UartMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case uart.FieldDeviceID:
		return m.OldDeviceID(ctx)
	case uart.FieldHeader:
		return m.OldHeader(ctx)
	case uart.FieldAddr:
		return m.OldAddr(ctx)
	case uart.FieldCmd:
		return m.OldCmd(ctx)
	case uart.FieldTail:
		return m.OldTail(ctx)
	case uart.FieldDescription:
		return m.OldDescription(ctx)
	case uart.FieldName:
		return m.OldName(ctx)
	case uart.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case uart.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown Uart field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UartMutation) SetField(name string, value ent.Value) error {
	switch name {
	case uart.FieldDeviceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeviceID(v)
		return nil
	case uart.FieldHeader:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHeader(v)
		return nil
	case uart.FieldAddr:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAddr(v)
		return nil
	case uart.FieldCmd:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCmd(v)
		return nil
	case uart.FieldTail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTail(v)
		return nil
	case uart.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case uart.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case uart.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case uart.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown Uart field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UartMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UartMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UartMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Uart numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UartMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UartMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UartMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Uart nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UartMutation) ResetField(name string) error {
	switch name {
	case uart.FieldDeviceID:
		m.ResetDeviceID()
		return nil
	case uart.FieldHeader:
		m.ResetHeader()
		return nil
	case uart.FieldAddr:
		m.ResetAddr()
		return nil
	case uart.FieldCmd:
		m.ResetCmd()
		return nil
	case uart.FieldTail:
		m.ResetTail()
		return nil
	case uart.FieldDescription:
		m.ResetDescription()
		return nil
	case uart.FieldName:
		m.ResetName()
		return nil
	case uart.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case uart.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown Uart field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UartMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.device != nil {
		edges = append(edges, uart.EdgeDevice)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UartMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case uart.EdgeDevice:
		if id := m.device; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UartMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UartMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UartMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cleareddevice {
		edges = append(edges, uart.EdgeDevice)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UartMutation) EdgeCleared(name string) bool {
	switch name {
	case uart.EdgeDevice:
		return m.cleareddevice
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UartMutation) ClearEdge(name string) error {
	switch name {
	case uart.EdgeDevice:
		m.ClearDevice()
		return nil
	}
	return fmt.Errorf("unknown Uart unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UartMutation) ResetEdge(name string) error {
	switch name {
	case uart.EdgeDevice:
		m.ResetDevice()
		return nil
	}
	return fmt.Errorf("unknown Uart edge %s", name)
}

// UDPMutation represents an operation that mutates the Udp nodes in the graph.
type UDPMutation struct {
	config
	op            Op
	typ           string
	id            *string
	_type         *string
	header        *string
	type_id       *string
	description   *string
	name          *string
	create_unix   *time.Time
	update_unix   *time.Time
	clearedFields map[string]struct{}
	device        *string
	cleareddevice bool
	done          bool
	oldValue      func(context.Context) (*Udp, error)
	predicates    []predicate.Udp
}

var _ ent.Mutation = (*UDPMutation)(nil)

// udpOption allows management of the mutation configuration using functional options.
type udpOption func(*UDPMutation)

// newUDPMutation creates new mutation for the Udp entity.
func newUDPMutation(c config, op Op, opts ...udpOption) *UDPMutation {
	m := &UDPMutation{
		config:        c,
		op:            op,
		typ:           TypeUDP,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUdpID sets the ID field of the mutation.
func withUdpID(id string) udpOption {
	return func(m *UDPMutation) {
		var (
			err   error
			once  sync.Once
			value *Udp
		)
		m.oldValue = func(ctx context.Context) (*Udp, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Udp.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUdp sets the old Udp of the mutation.
func withUdp(node *Udp) udpOption {
	return func(m *UDPMutation) {
		m.oldValue = func(context.Context) (*Udp, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UDPMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UDPMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Udp entities.
func (m *UDPMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UDPMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UDPMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Udp.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetDeviceID sets the "device_id" field.
func (m *UDPMutation) SetDeviceID(s string) {
	m.device = &s
}

// DeviceID returns the value of the "device_id" field in the mutation.
func (m *UDPMutation) DeviceID() (r string, exists bool) {
	v := m.device
	if v == nil {
		return
	}
	return *v, true
}

// OldDeviceID returns the old "device_id" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldDeviceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeviceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeviceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeviceID: %w", err)
	}
	return oldValue.DeviceID, nil
}

// ResetDeviceID resets all changes to the "device_id" field.
func (m *UDPMutation) ResetDeviceID() {
	m.device = nil
}

// SetType sets the "type" field.
func (m *UDPMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *UDPMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *UDPMutation) ResetType() {
	m._type = nil
}

// SetHeader sets the "header" field.
func (m *UDPMutation) SetHeader(s string) {
	m.header = &s
}

// Header returns the value of the "header" field in the mutation.
func (m *UDPMutation) Header() (r string, exists bool) {
	v := m.header
	if v == nil {
		return
	}
	return *v, true
}

// OldHeader returns the old "header" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldHeader(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHeader is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHeader requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHeader: %w", err)
	}
	return oldValue.Header, nil
}

// ResetHeader resets all changes to the "header" field.
func (m *UDPMutation) ResetHeader() {
	m.header = nil
}

// SetTypeID sets the "type_id" field.
func (m *UDPMutation) SetTypeID(s string) {
	m.type_id = &s
}

// TypeID returns the value of the "type_id" field in the mutation.
func (m *UDPMutation) TypeID() (r string, exists bool) {
	v := m.type_id
	if v == nil {
		return
	}
	return *v, true
}

// OldTypeID returns the old "type_id" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldTypeID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTypeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTypeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTypeID: %w", err)
	}
	return oldValue.TypeID, nil
}

// ResetTypeID resets all changes to the "type_id" field.
func (m *UDPMutation) ResetTypeID() {
	m.type_id = nil
}

// SetDescription sets the "description" field.
func (m *UDPMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *UDPMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *UDPMutation) ResetDescription() {
	m.description = nil
}

// SetName sets the "name" field.
func (m *UDPMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *UDPMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *UDPMutation) ResetName() {
	m.name = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *UDPMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *UDPMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *UDPMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *UDPMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *UDPMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the Udp entity.
// If the Udp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UDPMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *UDPMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// ClearDevice clears the "device" edge to the Device entity.
func (m *UDPMutation) ClearDevice() {
	m.cleareddevice = true
	m.clearedFields[udp.FieldDeviceID] = struct{}{}
}

// DeviceCleared reports if the "device" edge to the Device entity was cleared.
func (m *UDPMutation) DeviceCleared() bool {
	return m.cleareddevice
}

// DeviceIDs returns the "device" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// DeviceID instead. It exists only for internal usage by the builders.
func (m *UDPMutation) DeviceIDs() (ids []string) {
	if id := m.device; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetDevice resets all changes to the "device" edge.
func (m *UDPMutation) ResetDevice() {
	m.device = nil
	m.cleareddevice = false
}

// Where appends a list predicates to the UDPMutation builder.
func (m *UDPMutation) Where(ps ...predicate.Udp) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UDPMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UDPMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Udp, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UDPMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UDPMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Udp).
func (m *UDPMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UDPMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.device != nil {
		fields = append(fields, udp.FieldDeviceID)
	}
	if m._type != nil {
		fields = append(fields, udp.FieldType)
	}
	if m.header != nil {
		fields = append(fields, udp.FieldHeader)
	}
	if m.type_id != nil {
		fields = append(fields, udp.FieldTypeID)
	}
	if m.description != nil {
		fields = append(fields, udp.FieldDescription)
	}
	if m.name != nil {
		fields = append(fields, udp.FieldName)
	}
	if m.create_unix != nil {
		fields = append(fields, udp.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, udp.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UDPMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case udp.FieldDeviceID:
		return m.DeviceID()
	case udp.FieldType:
		return m.GetType()
	case udp.FieldHeader:
		return m.Header()
	case udp.FieldTypeID:
		return m.TypeID()
	case udp.FieldDescription:
		return m.Description()
	case udp.FieldName:
		return m.Name()
	case udp.FieldCreateUnix:
		return m.CreateUnix()
	case udp.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UDPMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case udp.FieldDeviceID:
		return m.OldDeviceID(ctx)
	case udp.FieldType:
		return m.OldType(ctx)
	case udp.FieldHeader:
		return m.OldHeader(ctx)
	case udp.FieldTypeID:
		return m.OldTypeID(ctx)
	case udp.FieldDescription:
		return m.OldDescription(ctx)
	case udp.FieldName:
		return m.OldName(ctx)
	case udp.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case udp.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown Udp field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UDPMutation) SetField(name string, value ent.Value) error {
	switch name {
	case udp.FieldDeviceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeviceID(v)
		return nil
	case udp.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case udp.FieldHeader:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHeader(v)
		return nil
	case udp.FieldTypeID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTypeID(v)
		return nil
	case udp.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case udp.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case udp.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case udp.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown Udp field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UDPMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UDPMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UDPMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Udp numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UDPMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UDPMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UDPMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Udp nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UDPMutation) ResetField(name string) error {
	switch name {
	case udp.FieldDeviceID:
		m.ResetDeviceID()
		return nil
	case udp.FieldType:
		m.ResetType()
		return nil
	case udp.FieldHeader:
		m.ResetHeader()
		return nil
	case udp.FieldTypeID:
		m.ResetTypeID()
		return nil
	case udp.FieldDescription:
		m.ResetDescription()
		return nil
	case udp.FieldName:
		m.ResetName()
		return nil
	case udp.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case udp.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown Udp field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UDPMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.device != nil {
		edges = append(edges, udp.EdgeDevice)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UDPMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case udp.EdgeDevice:
		if id := m.device; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UDPMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UDPMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UDPMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.cleareddevice {
		edges = append(edges, udp.EdgeDevice)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UDPMutation) EdgeCleared(name string) bool {
	switch name {
	case udp.EdgeDevice:
		return m.cleareddevice
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UDPMutation) ClearEdge(name string) error {
	switch name {
	case udp.EdgeDevice:
		m.ClearDevice()
		return nil
	}
	return fmt.Errorf("unknown Udp unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UDPMutation) ResetEdge(name string) error {
	switch name {
	case udp.EdgeDevice:
		m.ResetDevice()
		return nil
	}
	return fmt.Errorf("unknown Udp edge %s", name)
}
