#!/usr/bin/env bash
set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

if [[ -z "${MIGRATION_NAME:-}" \
   || -z "${SCHEMA_DIRECTORY:-}" \
   || -z "${GENERATION_SCRIPT:-}" \
   || -z "${OUTPUT_DIR:-}" \
]]; then
  echo "Generate migration SQL for ent ORM, more to see https://github.com/ent/ent"
  echo "Usage:"
  echo "MIGRATION_NAME=init_db SCHEMA_DIRECTORY=internal/ent/schema GENERATION_SCRIPT=db/generate.go OUTPUT_DIR=db/migrations/postgres $0"
fi

MIGRATION_NAME=${MIGRATION_NAME}
SCHEMA_DIRECTORY=$(realpath "${SCHEMA_DIRECTORY}")
GENERATION_SCRIPT=${GENERATION_SCRIPT}
OUTPUT_DIR=${OUTPUT_DIR}
VERSION_FILE=$(realpath "${SCHEMA_DIRECTORY}/../version.go")

echo "Generating migration SQL in $OUTPUT_DIR"
go run -mod=mod "$GENERATION_SCRIPT" -n "$MIGRATION_NAME" -o "$OUTPUT_DIR" -postgres-url "${POSTGRES_URL}"
echo "Generating migration SQL in $OUTPUT_DIR"

echo "Writing version to $VERSION_FILE"

# Extract the highest timestamp of version file.
# Files: V20231121055737__create_users.sql  V20231121060516__user_add_name_age.sql
# Print: 20231121060516
VERSION=$(python3 "$SCRIPT_DIR/get_highest_version.py" "$OUTPUT_DIR")

echo "Latest version $VERSION, files:"
ls "$OUTPUT_DIR" | grep "$VERSION"

echo "Write version $VERSION to $VERSION_FILE"
cat <<EOF > "$VERSION_FILE"
package ent

const (
	FlywaySchemaVersion = "$VERSION" 
)
EOF
