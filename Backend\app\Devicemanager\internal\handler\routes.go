// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package handler

import (
	"net/http"

	"GCF/app/Devicemanager/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/data/fetch",
				Handler: FetchDeviceDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/device/control",
				Handler: ControlDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/device/list",
				Handler: ListDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/frame/create",
				Handler: CreateDeviceFrameHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/frame/delete",
				Handler: DeleteDevice<PERSON><PERSON><PERSON><PERSON>and<PERSON>(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/frame/info/get",
				Handler: GetFrameInfoHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/frame/update",
				Handler: UpdateFrameHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/info/get",
				Handler: GetDeviceInfoHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/resource/create",
				Handler: CreateDeviceResourceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/resource/delete",
				Handler: DeleteDeviceResourceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/resource/enum",
				Handler: DeviceEnumHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/resource/protocol/enum",
				Handler: ProtocolEnumHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/device/resource/update",
				Handler: UpdateDeviceResourceHandler(serverCtx),
			},
		},
	)
}
