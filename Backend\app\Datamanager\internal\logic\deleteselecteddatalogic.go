package logic

import (
	"context"
	"errors"
	"net/http"
	"time"

	xerrors "github.com/zeromicro/x/errors"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteSelectedDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteSelectedDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteSelectedDataLogic {
	return &DeleteSelectedDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteSelectedDataLogic) DeleteSelectedData(req *types.DeleteSelectedDataRequest) (resp *types.DeleteSelectedDataResponse, err error) {
	// todo: add your logic here and delete this line
	var rowsAffected uint64 = 0
	for _, row := range req.List {

		timeStr := time.UnixMilli(row.TimeStamp).UTC().Format("2006-01-02 15:04:05.000")
		whereinfo := &datamanager.WhereInfo{
			ColumnName:    "ts",
			CompareSymbol: "=",
			CompareValue:  timeStr,
		}
		rpcReq := &datamanager.DeleteInstanceDataRequest{
			Revision:      req.Revision,
			InstanceInfo:  convertInstanceInfo(row.InstanceInfo),
			DatagroupInfo: convertDataGroupInfo(row.DataGroupInfo),
			WhereInfos:    []*datamanager.WhereInfo{whereinfo},
		}
		rpcResp, err := l.svcCtx.UtilDataService.DeleteInstanceData(l.ctx, rpcReq)
		if err != nil {
			Msg := xerrors.New(http.StatusBadRequest, err.Error())
			return nil, errors.New(Msg.Error())
		}
		if rpcResp.Status != "success" {
			Msg := xerrors.New(http.StatusBadRequest, rpcResp.Status)
			return nil, errors.New(Msg.Error())
		}
		rowsAffected += rpcResp.RowsAffected
	}
	return &types.DeleteSelectedDataResponse{
		Status:       "success",
		RowsAffected: rowsAffected,
	}, nil
}
