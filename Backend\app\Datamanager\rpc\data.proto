syntax = "proto3";

import "google/protobuf/timestamp.proto";

package datamanager;
option go_package="./datamanager";

message TableInfo {
    string table_uid = 1;
}

message InstanceTagInfo {
    string instance_name = 1;
    string instance_uid = 2;
}

message DataGroupTagInfo {
    string datagroup_name = 1;
}

message FieldsInfo {
    repeated string field_name = 1;
}

message WhereInfo {
    string column_name = 1;
    string compare_symbol = 2;
    string compare_value = 3;
}

message DataRow {
    google.protobuf.Timestamp ts = 1;
    InstanceTagInfo instance_info = 2;
    DataGroupTagInfo datagroup_info = 3;
    repeated SingleData datas = 4;
}

message SingleData {
    string field = 1;
    string value = 2;
}

message GetProducerDataRequest {
    TableInfo table_info = 1;
    int64 revision = 2;
    repeated WhereInfo where_infos = 3;
    uint64 data_row_limit = 4;
    DataGroupTagInfo datagroup_info = 5;
    InstanceTagInfo instance_info = 6;
    FieldsInfo fields_info = 7;
}

message GetProducerDataResponse {
    string status = 1;
    uint64 data_count = 2;
    repeated DataRow data_rows = 3;
}

message DeleteProducerDataRequest {
    TableInfo table_info = 1;
    int64 revision = 2;
    repeated WhereInfo where_infos = 3;
    DataGroupTagInfo datagroup_info = 4;
    InstanceTagInfo instance_info = 5;
    FieldsInfo fields_info = 6;
}

message DeleteProducerDataResponse {
    string status = 1;
    uint64 rows_affected = 2;
}

message ChangeProducerDataRequest {
    TableInfo table_info = 1;
    int64 revision = 2;
    repeated DataRow data_rows = 3;
}

message ChangeProducerDataResponse {
    string status = 1;
}

message GetInstanceDataRequest {
    DataGroupTagInfo datagroup_info = 1;
    int64 revision = 2;
    uint64 data_row_limit = 3;
    InstanceTagInfo instance_info = 4;
    FieldsInfo fields_info = 5;
    repeated WhereInfo where_infos = 6;
}

message GetInstanceDataResponse {
    string status = 1;
    uint64 data_count = 2;
    repeated DataRow data_rows = 3;
}

message DeleteInstanceDataRequest {
    InstanceTagInfo instance_info = 1;
    int64 revision = 2;
    repeated WhereInfo where_infos = 3;
    DataGroupTagInfo datagroup_info = 4;
    FieldsInfo fields_info = 5;
}

message DeleteInstanceDataResponse {
    string status = 1;
    uint64 rows_affected = 2;
}

message ChangeInstanceDataRequest {
    InstanceTagInfo instance_info = 1;
    int64 revision = 2;
    repeated DataRow data_rows = 3;
}

message ChangeInstanceDataResponse {
    string status = 1;
}

message ChangeDataRequest {
    int64 revision = 1;
    repeated DataRow data_rows = 2;
}

message ChangeDataResponse {
    string status = 1;
}

service Data{
    rpc GetProducerData(GetProducerDataRequest) returns (GetProducerDataResponse);
    rpc DeleteProducerData(DeleteProducerDataRequest) returns (DeleteProducerDataResponse);
    rpc ChangeProducerData(ChangeProducerDataRequest) returns (ChangeProducerDataResponse);
    rpc GetInstanceData(GetInstanceDataRequest) returns (GetInstanceDataResponse);
    rpc DeleteInstanceData(DeleteInstanceDataRequest) returns (DeleteInstanceDataResponse);
    rpc ChangeInstanceData(ChangeInstanceDataRequest) returns (ChangeInstanceDataResponse);
    rpc ChangeData(ChangeDataRequest) returns (ChangeDataResponse);
}
