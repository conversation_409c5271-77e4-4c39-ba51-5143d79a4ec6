package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Data holds the schema definition for the Data entity.
type Data struct {
	ent.Schema
}

// Fields of the Data.
func (Data) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("数据UID"),
		field.String("frame_id").
			Comment("frame id").
			NotEmpty(),
		field.String("index").
			Comment("Index of the data").
			NotEmpty(),
		field.String("name").
			Comment("name of the index data").
			NotEmpty(),
		field.String("type").
			Comment("type of the index data").
			Default(""),
		field.String("unit").
			Comment("Unit of the index data").
			Default(""),
		field.String("description").
			Comment("Description of the device").
			Default(""),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("timestamp of the index data"),
	}
}
