package kafka

import (
	"context"
	"fmt"
	"kafka-producer/pkg/message"
	"log"

	"github.com/zeromicro/go-queue/kq"
)

// Producer kafka生产者
type Producer struct {
	pusher *kq.Pusher
	topic  string
}

// NewProducer 创建新的kafka生产者
func NewProducer(brokers []string, topic string) *Producer {
	pusher := kq.<PERSON><PERSON>er(brokers, topic)

	return &Producer{
		pusher: pusher,
		topic:  topic,
	}
}

// SendMessage 发送消息到kafka
func (p *Producer) SendMessage(ctx context.Context, key string, msg *message.Message) error {
	jsonData, err := msg.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	err = p.pusher.Push(ctx, jsonData)
	if err != nil {
		return fmt.Errorf("failed to push message to kafka: %w", err)
	}

	log.Printf("Message sent to topic %s with key %s", p.topic, key)
	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	return p.pusher.Close()
}
