// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.4
// Source: model.proto

package server

import (
	"context"

	"GCF/app/Modelmanager/internal/logic"
	"GCF/app/Modelmanager/internal/modelmanager"
	"GCF/app/Modelmanager/internal/svc"
)

type ModelServer struct {
	svcCtx *svc.ServiceContext
	modelmanager.UnimplementedModelServer
}

func NewModelServer(svcCtx *svc.ServiceContext) *ModelServer {
	return &ModelServer{
		svcCtx: svcCtx,
	}
}

func (s *ModelServer) CreateModelConfig(ctx context.Context, in *modelmanager.CreateModelConfigRequest) (*modelmanager.CreateModelConfigResponse, error) {
	l := logic.NewCreateModelConfigLogic(ctx, s.svcCtx)
	return l.CreateModelConfig(in)
}

func (s *ModelServer) GetTrainedModel(ctx context.Context, in *modelmanager.GetTrainedModelRequest) (*modelmanager.GetTrainedModelResponse, error) {
	l := logic.NewGetTrainedModelLogic(ctx, s.svcCtx)
	return l.GetTrainedModel(in)
}
