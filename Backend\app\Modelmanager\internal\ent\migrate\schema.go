// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// ModelsColumns holds the columns for the "models" table.
	ModelsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "model_id", Type: field.TypeString, Unique: true},
		{Name: "model_name_version", Type: field.TypeString, Unique: true},
		{Name: "framework_version", Type: field.TypeString, Default: ""},
		{Name: "type_desc", Type: field.TypeString, Default: ""},
		{Name: "storage_path", Type: field.TypeString, Unique: true},
		{Name: "accuracy", Type: field.TypeFloat64, Default: 0},
		{Name: "precision", Type: field.TypeFloat64, Default: 0},
		{Name: "recall", Type: field.TypeFloat64, Default: 0},
		{Name: "f1_score", Type: field.TypeFloat64, Default: 0},
		{Name: "created_by", Type: field.TypeString, Default: ""},
		{Name: "create_unix", Type: field.TypeTime},
		{Name: "update_unix", Type: field.TypeTime},
	}
	// ModelsTable holds the schema information for the "models" table.
	ModelsTable = &schema.Table{
		Name:       "models",
		Columns:    ModelsColumns,
		PrimaryKey: []*schema.Column{ModelsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		ModelsTable,
	}
)

func init() {
}
