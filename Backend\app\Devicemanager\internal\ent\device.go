// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Device is the model entity for the Device schema.
type Device struct {
	config `json:"-"`
	// ID of the ent.
	// 设备UID
	ID string `json:"id,omitempty"`
	// name of the machine
	Name string `json:"name,omitempty"`
	// IP address of the machine
	IP string `json:"ip,omitempty"`
	// Type of the machine
	Type string `json:"type,omitempty"`
	// OS of the machine
	Os string `json:"os,omitempty"`
	// CPU model of the machine
	CPU string `json:"cpu,omitempty"`
	// GPU model of the machine
	Gpu string `json:"gpu,omitempty"`
	// Memory size of the machine
	Memory string `json:"memory,omitempty"`
	// Disk size of the machine
	Disk string `json:"disk,omitempty"`
	// protocol of the communication
	Protocol string `json:"protocol,omitempty"`
	// Description of the device
	Description string `json:"description,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// Health check timestamp
	Healthtimestamp time.Time `json:"healthtimestamp,omitempty"`
	// Workmode holds the value of the "workmode" field.
	Workmode string `json:"workmode,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// Update timestamp
	UpdateUnix time.Time `json:"update_unix,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DeviceQuery when eager-loading is set.
	Edges        DeviceEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DeviceEdges holds the relations/edges for other nodes in the graph.
type DeviceEdges struct {
	// ModbusConfig holds the value of the modbusConfig edge.
	ModbusConfig []*Modbus `json:"modbusConfig,omitempty"`
	// UartConfig holds the value of the uartConfig edge.
	UartConfig []*Uart `json:"uartConfig,omitempty"`
	// UdpConfig holds the value of the udpConfig edge.
	UdpConfig []*Udp `json:"udpConfig,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// ModbusConfigOrErr returns the ModbusConfig value or an error if the edge
// was not loaded in eager-loading.
func (e DeviceEdges) ModbusConfigOrErr() ([]*Modbus, error) {
	if e.loadedTypes[0] {
		return e.ModbusConfig, nil
	}
	return nil, &NotLoadedError{edge: "modbusConfig"}
}

// UartConfigOrErr returns the UartConfig value or an error if the edge
// was not loaded in eager-loading.
func (e DeviceEdges) UartConfigOrErr() ([]*Uart, error) {
	if e.loadedTypes[1] {
		return e.UartConfig, nil
	}
	return nil, &NotLoadedError{edge: "uartConfig"}
}

// UdpConfigOrErr returns the UdpConfig value or an error if the edge
// was not loaded in eager-loading.
func (e DeviceEdges) UdpConfigOrErr() ([]*Udp, error) {
	if e.loadedTypes[2] {
		return e.UdpConfig, nil
	}
	return nil, &NotLoadedError{edge: "udpConfig"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Device) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case device.FieldID, device.FieldName, device.FieldIP, device.FieldType, device.FieldOs, device.FieldCPU, device.FieldGpu, device.FieldMemory, device.FieldDisk, device.FieldProtocol, device.FieldDescription, device.FieldStatus, device.FieldWorkmode:
			values[i] = new(sql.NullString)
		case device.FieldHealthtimestamp, device.FieldCreateUnix, device.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Device fields.
func (d *Device) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case device.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				d.ID = value.String
			}
		case device.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				d.Name = value.String
			}
		case device.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				d.IP = value.String
			}
		case device.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				d.Type = value.String
			}
		case device.FieldOs:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field os", values[i])
			} else if value.Valid {
				d.Os = value.String
			}
		case device.FieldCPU:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field cpu", values[i])
			} else if value.Valid {
				d.CPU = value.String
			}
		case device.FieldGpu:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field gpu", values[i])
			} else if value.Valid {
				d.Gpu = value.String
			}
		case device.FieldMemory:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field memory", values[i])
			} else if value.Valid {
				d.Memory = value.String
			}
		case device.FieldDisk:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field disk", values[i])
			} else if value.Valid {
				d.Disk = value.String
			}
		case device.FieldProtocol:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field protocol", values[i])
			} else if value.Valid {
				d.Protocol = value.String
			}
		case device.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				d.Description = value.String
			}
		case device.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				d.Status = value.String
			}
		case device.FieldHealthtimestamp:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field healthtimestamp", values[i])
			} else if value.Valid {
				d.Healthtimestamp = value.Time
			}
		case device.FieldWorkmode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field workmode", values[i])
			} else if value.Valid {
				d.Workmode = value.String
			}
		case device.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				d.CreateUnix = value.Time
			}
		case device.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				d.UpdateUnix = value.Time
			}
		default:
			d.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Device.
// This includes values selected through modifiers, order, etc.
func (d *Device) Value(name string) (ent.Value, error) {
	return d.selectValues.Get(name)
}

// QueryModbusConfig queries the "modbusConfig" edge of the Device entity.
func (d *Device) QueryModbusConfig() *ModbusQuery {
	return NewDeviceClient(d.config).QueryModbusConfig(d)
}

// QueryUartConfig queries the "uartConfig" edge of the Device entity.
func (d *Device) QueryUartConfig() *UartQuery {
	return NewDeviceClient(d.config).QueryUartConfig(d)
}

// QueryUdpConfig queries the "udpConfig" edge of the Device entity.
func (d *Device) QueryUdpConfig() *UDPQuery {
	return NewDeviceClient(d.config).QueryUdpConfig(d)
}

// Update returns a builder for updating this Device.
// Note that you need to call Device.Unwrap() before calling this method if this Device
// was returned from a transaction, and the transaction was committed or rolled back.
func (d *Device) Update() *DeviceUpdateOne {
	return NewDeviceClient(d.config).UpdateOne(d)
}

// Unwrap unwraps the Device entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (d *Device) Unwrap() *Device {
	_tx, ok := d.config.driver.(*txDriver)
	if !ok {
		panic("ent: Device is not a transactional entity")
	}
	d.config.driver = _tx.drv
	return d
}

// String implements the fmt.Stringer.
func (d *Device) String() string {
	var builder strings.Builder
	builder.WriteString("Device(")
	builder.WriteString(fmt.Sprintf("id=%v, ", d.ID))
	builder.WriteString("name=")
	builder.WriteString(d.Name)
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(d.IP)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(d.Type)
	builder.WriteString(", ")
	builder.WriteString("os=")
	builder.WriteString(d.Os)
	builder.WriteString(", ")
	builder.WriteString("cpu=")
	builder.WriteString(d.CPU)
	builder.WriteString(", ")
	builder.WriteString("gpu=")
	builder.WriteString(d.Gpu)
	builder.WriteString(", ")
	builder.WriteString("memory=")
	builder.WriteString(d.Memory)
	builder.WriteString(", ")
	builder.WriteString("disk=")
	builder.WriteString(d.Disk)
	builder.WriteString(", ")
	builder.WriteString("protocol=")
	builder.WriteString(d.Protocol)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(d.Description)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(d.Status)
	builder.WriteString(", ")
	builder.WriteString("healthtimestamp=")
	builder.WriteString(d.Healthtimestamp.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("workmode=")
	builder.WriteString(d.Workmode)
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(d.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(d.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Devices is a parsable slice of Device.
type Devices []*Device
