package handler

import (
	"net/http"

	"GCF/app/Datamanager/internal/logic"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"

	xhttp "github.com/zeromicro/x/http"
)

func FetchInstanceDataHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.FetchInstanceDataRequest
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
			return
		}

		l := logic.NewFetchInstanceDataLogic(r.Context(), svcCtx)
		resp, err := l.FetchInstanceData(&req)
		if err != nil {
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
		} else {
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
