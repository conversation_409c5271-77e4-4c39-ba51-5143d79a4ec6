# Kafka Producer Service

基于设计文档实现的Kafka生产者服务，使用go-queue的kq模块，用于监听ETCD配置变化并向Kafka发送结构化消息。

## 特性

- **符合设计文档的消息结构**：实现MetaData和Message结构
- **基于Producer.Uname的Topic组织**：动态使用Producer.Uname作为Topic名称
- **双Driver支持**：同时支持motor_12345和motor_10021两个驱动
- **ETCD监听**：实时监听配置变化并重新加载
- **数据生成**：自动生成电流、电压、转速、温度等模拟数据
- **Kafka发送**：使用go-queue/kq库发送消息，提供更好的性能和稳定性
- **版本管理**：消息包含修订版本信息用于追踪
- **示例代码**：提供go-queue使用示例

## 项目结构

```
kafka-test/
├── main.go                           # 主程序入口
├── go.mod                            # Go模块定义
├── examples/
│   └── kq_example.go                 # go-queue使用示例
├── internal/
│   └── service/
│       └── producer_service.go       # 生产者服务核心逻辑
└── pkg/
    ├── data/
    │   └── generator.go              # 数据生成器
    ├── etcd/
    │   ├── client.go                 # ETCD客户端
    │   └── models.go                 # ETCD数据模型
    ├── kafka/
    │   └── producer.go               # Kafka生产者(基于go-queue/kq)
    └── message/
        └── message.go                # 消息结构定义
```

## 功能特性

### 1. DataGroup 设计原则
- 数据按照Group组织，支持同一数据帧中的多个数据
- 支持有时序关系的数据组合（如电机相电流和控制输出量）

### 2. Topic 组织
- 根据Producer.name组织Topic
- 通过ETCD Watch机制监听配置变更

### 3. Message 组织
- 包含MetaData和Data两部分
- MetaData包含：TimeStamp、Instance.name、DataGroup.name、Producer.revision
- Data部分遵循DataGroup.structure

## 消息格式

```json
{
    "MetaData": {
        "TimeStamp": "2024-01-01T12:00:00.000Z",
        "Name": "motor1",
        "DataGroup": "motor_curr_loop",
        "Revision": 123
    },
    "Data": {
        "current": 8.5,
        "voltage": 220.3
    }
}
```

## 依赖

- Go 1.16+
- github.com/zeromicro/go-queue - Kafka队列库
- go.etcd.io/etcd/client/v3 - ETCD客户端

## 使用方法

### 1. 安装依赖
```bash
go mod tidy
```

### 2. 确保服务运行
- ETCD服务：***************:2379
- Kafka服务：localhost:9092

### 3. 运行程序
```bash
go run main.go
```

### 4. 运行go-queue示例
```bash
go run examples/kq_example.go
```

### 5. 验证
程序会为两个driver（motor_12345、motor_10021）创建生产者服务：
- 监听ETCD中的配置变化
- 为每个实例的每个数据组生成模拟数据
- 发送结构化消息到Kafka

## 配置说明

### ETCD配置结构
- `/driver/{driverUID}/producer` - 生产者配置
- `/driver/{driverUID}/instances` - 实例配置

### Kafka Topic
- Topic名称基于Producer.name
- 消息Key格式：`{driverUID}_{instanceName}_{dataGroupName}`

## 监控和日志

程序会输出详细的日志信息，包括：
- 服务启动状态
- 配置加载情况
- ETCD配置变更
- Kafka消息发送状态

使用Ctrl+C可以优雅地停止所有服务。