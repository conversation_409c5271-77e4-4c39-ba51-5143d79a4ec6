from kafka import KafkaProducer
from datetime import datetime
import time
import json

# Kafka连接配置
KAFKA_BOOTSTRAP_SERVERS = ['localhost:9092']

# 固定值
DATAPRODUCERINSTUID = '65eb01ad-90b2-4ad5-b83c-cddab25e2286'
DATAGROUPNAME = 'modbus_851fda3f-669c-4840-ab62-cc08b7d02ef5'
Uname = f'fan_{DATAPRODUCERINSTUID}'
version = 1253

KAFKA_TOPIC = f'fan_{DATAPRODUCERINSTUID}'
# 发送数据到Ka<PERSON><PERSON>
def send_data(producer, topic, ts, a, b, c):
    data = {
        'b': str(0),
        'ba': str(a),
        'bba': str(b),
        'bbba': str(c)
    }
    metadata={
            "TimeStamp": str(ts),
            "Instance": Uname,
            "DataGroup": DATAGROUPNAME,
            "Revision": version
        }
    value={
        "Data": data,
        "MetaData": metadata
    }
    try:
        producer.send(topic, value=json.dumps(value).encode('utf-8'))
        print(f"Sent data: {data}")
    except Exception as e:
        print("Kafka send error:", e)

# 示例用法
if __name__ == '__main__':
    producer = KafkaProducer(bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS)
    for i in range(10000):
        time.sleep(0.01)
        ts = datetime.now().isoformat()
        a = i
        b = i+10
        c = i+100
        send_data(producer, KAFKA_TOPIC, ts, a, b, c)
    producer.flush()
