API:
  Name: data-api
  Host: 0.0.0.0
  Port: 8888

RPC:
  Name: data.rpc
  ListenOn: 0.0.0.0:8080
  Etcd:
    Hosts:
    - 127.0.0.1:12379
    Key: data.rpc

ZEtcdConf:
  Endpoints:
  - 127.0.0.1:12379

SqlConf:
  Host: 127.0.0.1
  Port: "4002"
  User: "root"
  Password: ""
  Database: public


KafkaConf:
  Name: data-manager
  Topic: 
  Brokers:
  - 127.0.0.1:9092
  Group: data-manager
  Offset: last
  Consumers: 8