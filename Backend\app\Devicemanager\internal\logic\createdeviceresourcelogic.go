package logic

import (
	"context"
	"net/http"

	xerrors "github.com/zeromicro/x/errors"

	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"GCF/app/Devicemanager/internal/logic/utils"
	"strings"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDeviceResourceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDeviceResourceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDeviceResourceLogic {
	return &CreateDeviceResourceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDeviceResourceLogic) CreateDeviceResource(req *types.CreateDeviceResourceRequest) (resp *types.CreateDeviceResourceResponse, err error) {
	//生成UID
	uid := uuid.New().String()
	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, xerrors.New(http.StatusForbidden, "创建设备事务启动失败")
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()

	//生成DeviceMeta
	DeviceMeta := &types.DeviceMeta{
		DeviceUID:   uid,
		Description: req.DeviceResourceInfo.Description,
		DeviceName:  req.DeviceResourceInfo.DeviceName,
	}
	//生成DeviceWorkStatus
	//TODO: 根据设备类型设置健康状态
	HealthStatus := utils.HealthStatusReady
	WorkMode := utils.WorkModeCentralized
	DeviceWorkStatus := &types.DeviceWorkStatus{
		HealthStatus: HealthStatus,
		WorkMode:     WorkMode,
	}

	cpuResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.CPU)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "CPU数据转换失败")
	}
	gpuResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.GPU)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "GPU数据转换失败")
	}
	diskResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.Disk)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "DISK资源转换失败")
	}
	memResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.Mem)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "MEM资源转换失败")
	}

	//插入数据库
	_, err = tx.Device.Create().
		SetID(DeviceMeta.DeviceUID).
		SetName(req.DeviceResourceInfo.DeviceName).
		SetIP(req.DeviceResourceInfo.IP).
		SetOs(req.DeviceResourceInfo.OS).
		SetType(req.DeviceResourceInfo.Type).
		SetCPU(cpuResource).
		SetGpu(gpuResource).
		SetMemory(memResource).
		SetDisk(diskResource).
		SetProtocol(strings.Join(req.DeviceResourceInfo.Protocol, ",")).
		SetWorkmode(DeviceWorkStatus.WorkMode).
		SetStatus(DeviceWorkStatus.HealthStatus).
		SetDescription(req.DeviceResourceInfo.Description).
		Save(l.ctx)
	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			return nil, xerrors.New(http.StatusNotAcceptable, "撤销操作失败: "+err_rollback.Error())
		}
		return nil, xerrors.New(http.StatusNotAcceptable, "设备更新失败: "+err.Error())
	}

	//添加Frame记录
	for _, protocal := range req.DeviceResourceInfo.Protocol {
		FrameMeta := &types.FrameMeta{
			FrameType: protocal,
		}
		DeviceMeta.FrameMetas = append(DeviceMeta.FrameMetas, *FrameMeta)
	}

	tx.Commit()
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "创建设备请求提交失败: "+err.Error())
	}
	// 添加到Etcd
	err = utils.Pub2Etcd(l.ctx, l.svcCtx)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "failed to publish to Etcd: "+err.Error())
	}
	return &types.CreateDeviceResourceResponse{
		DeviceMeta:       *DeviceMeta,
		DeviceWorkStatus: *DeviceWorkStatus,
	}, nil

}
