package logic

import (
	"context"
	"errors"
	"net/http"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	xerrors "github.com/zeromicro/x/errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type FetchInstanceDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFetchInstanceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FetchInstanceDataLogic {
	return &FetchInstanceDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FetchInstanceDataLogic) FetchInstanceData(req *types.FetchInstanceDataRequest) (resp *types.FetchInstanceDataResponse, err error) {
	rpcReq := &datamanager.GetInstanceDataRequest{
		DatagroupInfo: convertDataGroupInfo(req.DataGroupInfo),
		Revision:      req.Revision,
		DataRowLimit:  req.DataRowLimit,
		WhereInfos:    convertWhereInfos(req.WhereInfos),
		InstanceInfo:  convertInstanceInfo(req.InstanceInfo),
		FieldsInfo:    convertFieldsInfo(req.FieldsInfo),
	}
	rpcResp, err := l.svcCtx.UtilDataService.GetInstanceData(l.ctx, rpcReq)
	if err != nil {
		Msg := xerrors.New(http.StatusBadRequest, err.Error())
		return nil, errors.New(Msg.Error())
	}

	dataRows := convertDataRows(rpcResp.DataRows)

	offset := (req.Page - 1) * req.PageSize

	var pagedDataRows []types.DataRow
	if offset < int64(len(dataRows)) {
		end := offset + req.PageSize
		if end > int64(len(dataRows)) {
			end = int64(len(dataRows))
		}
		pagedDataRows = dataRows[offset:end]
	} else {
		pagedDataRows = []types.DataRow{}
	}

	// 构建响应
	resp = &types.FetchInstanceDataResponse{
		List:     pagedDataRows,
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    rpcResp.DataCount,
	}

	return resp, nil
}
