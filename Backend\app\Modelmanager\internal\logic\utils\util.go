package utils

import (
	"fmt"
	"time"

	"GCF/app/Modelmanager/internal/modelmanager"
)

func CreateStoragePath(model_info *modelmanager.ModelInfo, id string, TimeStamp time.Time) string {
	storage_path := fmt.Sprintf("/models/%s/%s/%s/%s", model_info.GetCreatedBy(), model_info.GetTypeDesc(), TimeStamp.String(), id)
	return storage_path
}

func CreateTimeStamp(id string) time.Time {
	TimeStamp := time.Now()
	return TimeStamp
}
