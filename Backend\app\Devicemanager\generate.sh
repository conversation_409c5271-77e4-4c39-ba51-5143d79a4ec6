#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# CHANGE THIS: If we move the script.
SERVICE_ROOT=$SCRIPT_DIR

cd "$SERVICE_ROOT"

# Generate API，注意API 服务名与api-server.go一致
echo "Generating API in $(pwd)"
goctl api go -api api/device.api -dir .

# Generate RPC,注意**.proto名与rpc-server.go一致
echo "Generating RPC in $(pwd)"
goctl rpc protoc rpc/device.proto \
  --go_out="$(pwd)/internal" \
  --go-grpc_out="$(pwd)/internal" \
  --zrpc_out="$(pwd)"

# Generate device mock
# pushd device/ > /dev/null
#   mkdir -p mock
#   mockgen -destination mock/device_mock.go -package mock . Device
# popd > /dev/null

# # Generate docker image
# goctl docker -go device.go
