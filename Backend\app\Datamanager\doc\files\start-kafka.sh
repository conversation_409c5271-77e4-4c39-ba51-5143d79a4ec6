#!/bin/bash

echo "正在启动Kafka KRaft集群..."

# 启动集群
docker compose up -d

echo "等待Kafka集群启动..."
sleep 30

# 检查集群状态
echo "检查集群状态..."
docker compose ps

echo ""
echo "集群已启动！"
echo "访问地址:"
echo "  Kafka Broker 1: localhost:9092"
echo "  Kafka Broker 2: localhost:9094"
echo "  Kafka Broker 3: localhost:9096"
echo "  Kafka UI: http://localhost:8080"
echo ""
echo "使用以下命令测试集群:"
echo "  创建topic: docker exec kafka1 kafka-topics --create --topic test-topic --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3"
echo "  查看topic: docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092"
echo "  查看集群信息: docker exec kafka1 kafka-metadata-shell --snapshot /tmp/kraft-combined-logs/__cluster_metadata-0/00000000000000000000.log"
