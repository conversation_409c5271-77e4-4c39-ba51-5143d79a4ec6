apiVersion: v1
kind: ConfigMap
metadata:
  name: datamanager-config
data:
  data.yaml: |
    API:
      Name: data-api
      Host: 0.0.0.0
      Port: 3333

    RPC:
      Name: data.rpc
      ListenOn: 0.0.0.0:3030

    ZEtcdConf:
      Endpoints:
      - etcd-svc.etcd.svc.cluster.local:12379

    SqlConf:
      Host: *************
      Port: "30954"
      User: "root"
      Password: ""
      Database: public


    KafkaConf:
      Name: data-manager
      Topic: 
      Brokers:
      - kafka-svc.kafka.svc.cluster.local:9092  
      Group: data-manager
      Offset: last
      Consumers: 8