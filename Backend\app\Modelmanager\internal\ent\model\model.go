// Code generated by ent, DO NOT EDIT.

package model

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the model type in the database.
	Label = "model"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldModelID holds the string denoting the model_id field in the database.
	FieldModelID = "model_id"
	// FieldModelNameVersion holds the string denoting the model_name_version field in the database.
	FieldModelNameVersion = "model_name_version"
	// FieldFrameworkVersion holds the string denoting the framework_version field in the database.
	FieldFrameworkVersion = "framework_version"
	// FieldTypeDesc holds the string denoting the type_desc field in the database.
	FieldTypeDesc = "type_desc"
	// FieldStoragePath holds the string denoting the storage_path field in the database.
	FieldStoragePath = "storage_path"
	// FieldAccuracy holds the string denoting the accuracy field in the database.
	FieldAccuracy = "accuracy"
	// FieldPrecision holds the string denoting the precision field in the database.
	FieldPrecision = "precision"
	// FieldRecall holds the string denoting the recall field in the database.
	FieldRecall = "recall"
	// FieldF1Score holds the string denoting the f1_score field in the database.
	FieldF1Score = "f1_score"
	// FieldCreatedBy holds the string denoting the created_by field in the database.
	FieldCreatedBy = "created_by"
	// FieldCreateUnix holds the string denoting the create_unix field in the database.
	FieldCreateUnix = "create_unix"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// Table holds the table name of the model in the database.
	Table = "models"
)

// Columns holds all SQL columns for model fields.
var Columns = []string{
	FieldID,
	FieldModelID,
	FieldModelNameVersion,
	FieldFrameworkVersion,
	FieldTypeDesc,
	FieldStoragePath,
	FieldAccuracy,
	FieldPrecision,
	FieldRecall,
	FieldF1Score,
	FieldCreatedBy,
	FieldCreateUnix,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// ModelIDValidator is a validator for the "model_id" field. It is called by the builders before save.
	ModelIDValidator func(string) error
	// DefaultFrameworkVersion holds the default value on creation for the "framework_version" field.
	DefaultFrameworkVersion string
	// DefaultTypeDesc holds the default value on creation for the "type_desc" field.
	DefaultTypeDesc string
	// StoragePathValidator is a validator for the "storage_path" field. It is called by the builders before save.
	StoragePathValidator func(string) error
	// DefaultAccuracy holds the default value on creation for the "accuracy" field.
	DefaultAccuracy float64
	// AccuracyValidator is a validator for the "accuracy" field. It is called by the builders before save.
	AccuracyValidator func(float64) error
	// DefaultPrecision holds the default value on creation for the "precision" field.
	DefaultPrecision float64
	// PrecisionValidator is a validator for the "precision" field. It is called by the builders before save.
	PrecisionValidator func(float64) error
	// DefaultRecall holds the default value on creation for the "recall" field.
	DefaultRecall float64
	// RecallValidator is a validator for the "recall" field. It is called by the builders before save.
	RecallValidator func(float64) error
	// DefaultF1Score holds the default value on creation for the "f1_score" field.
	DefaultF1Score float64
	// F1ScoreValidator is a validator for the "f1_score" field. It is called by the builders before save.
	F1ScoreValidator func(float64) error
	// DefaultCreatedBy holds the default value on creation for the "created_by" field.
	DefaultCreatedBy string
	// DefaultCreateUnix holds the default value on creation for the "create_unix" field.
	DefaultCreateUnix func() time.Time
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
)

// OrderOption defines the ordering options for the Model queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByModelID orders the results by the model_id field.
func ByModelID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelID, opts...).ToFunc()
}

// ByModelNameVersion orders the results by the model_name_version field.
func ByModelNameVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelNameVersion, opts...).ToFunc()
}

// ByFrameworkVersion orders the results by the framework_version field.
func ByFrameworkVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFrameworkVersion, opts...).ToFunc()
}

// ByTypeDesc orders the results by the type_desc field.
func ByTypeDesc(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTypeDesc, opts...).ToFunc()
}

// ByStoragePath orders the results by the storage_path field.
func ByStoragePath(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStoragePath, opts...).ToFunc()
}

// ByAccuracy orders the results by the accuracy field.
func ByAccuracy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAccuracy, opts...).ToFunc()
}

// ByPrecision orders the results by the precision field.
func ByPrecision(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrecision, opts...).ToFunc()
}

// ByRecall orders the results by the recall field.
func ByRecall(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRecall, opts...).ToFunc()
}

// ByF1Score orders the results by the f1_score field.
func ByF1Score(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldF1Score, opts...).ToFunc()
}

// ByCreatedBy orders the results by the created_by field.
func ByCreatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedBy, opts...).ToFunc()
}

// ByCreateUnix orders the results by the create_unix field.
func ByCreateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUnix, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}
