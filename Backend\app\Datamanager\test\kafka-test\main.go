package main

import (
	"kafka-producer/internal/service"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	// 配置信息
	etcdEndpoints := []string{"localhost:2379"}
	kafkaBrokers := []string{
		"localhost:9092",
		"localhost:9094",
		"localhost:9096",
	}
	driverUIDs := []string{"motor_12345", "motor_10021"}

	var services []*service.ProducerService

	// 为每个driver创建生产者服务
	for _, driverUID := range driverUIDs {
		producerService, err := service.NewProducerService(etcdEndpoints, kafkaBrokers, driverUID)
		if err != nil {
			log.Fatalf("Failed to create producer service for %s: %v", driverUID, err)
		}

		services = append(services, producerService)

		// 启动服务
		err = producerService.Start()
		if err != nil {
			log.Fatalf("Failed to start producer service for %s: %v", driverUID, err)
		}

		log.Printf("Started producer service for driver: %s", driverUID)
	}

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Println("Kafka producers are running. Press Ctrl+C to stop.")
	<-sigChan

	log.Println("Shutting down...")

	// 停止所有服务
	for _, service := range services {
		service.Stop()
	}

	log.Println("All services stopped.")
}
