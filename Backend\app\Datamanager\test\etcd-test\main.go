package main

import (
	"context"
	"etcd-test/internal/service"
	"etcd-test/pkg/etcdclient"
	"log"
	"time"
)

func main() {
	// 假设etcd服务正在本地运行
	cli, err := etcdclient.NewEtcdClient([]string{"localhost:2379"}, 5*time.Second)
	if err != nil {
		log.Fatalf("Failed to create etcd client: %v", err)
	}
	defer cli.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	dataService := service.NewDataService(cli)
	dataService.WriteInitialData(ctx)
}
