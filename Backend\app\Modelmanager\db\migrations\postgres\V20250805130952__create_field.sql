-- create "models" table
CREATE TABLE "models" ("id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY, "model_id" character varying NOT NULL, "model_name_version" character varying NOT NULL, "framework_version" character varying NOT NULL DEFAULT '', "type_desc" character varying NOT NULL DEFAULT '', "storage_path" character varying NOT NULL, "accuracy" double precision NOT NULL DEFAULT 0, "precision" double precision NOT NULL DEFAULT 0, "recall" double precision NOT NULL DEFAULT 0, "f1_score" double precision NOT NULL DEFAULT 0, "created_by" character varying NOT NULL DEFAULT '', "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, PRIMARY KEY ("id"));
-- create index "models_model_id_key" to table: "models"
CREATE UNIQUE INDEX "models_model_id_key" ON "models" ("model_id");
-- create index "models_model_name_version_key" to table: "models"
CREATE UNIQUE INDEX "models_model_name_version_key" ON "models" ("model_name_version");
-- create index "models_storage_path_key" to table: "models"
CREATE UNIQUE INDEX "models_storage_path_key" ON "models" ("storage_path");
