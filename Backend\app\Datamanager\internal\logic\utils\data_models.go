package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"GCF/app/Datamanager/internal/datamanager"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/queue"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// DataNode 数据节点结构
type DataNode struct {
	Name  string `json:"name"`
	Unit  string `json:"unit"`
	Desc  string `json:"desc"`
	Owner string `json:"owner"`
}

// DataGroup 数据组结构
type DataGroup struct {
	DataNode  []DataNode `json:"data_node"`
	Frequency string     `json:"frequency"`
	Desc      string     `json:"desc"`
	Name      string     `json:"name"`
}

// DataProducer 数据生产者结构
type DataProducer struct {
	DataGroups []DataGroup `json:"data_groups"`
	UID        string      `json:"Uid"`
}

type Instance struct {
	Name     string `json:"name"`
	UID      string `json:"UID"`
	IP       string `json:"IP,omitempty"`       // omitempty for model instances
	Desc     string `json:"desc,omitempty"`     // omitempty for driver instances
}

// RevisionDataProducer 版本数据生产者结构
type RevisionDataProducer struct {
	*DataProducer
	Revision int64
}

type Instances struct {
	Instances []Instance `json:"instances"`
}

type Driver struct {
	RevisionDataProducer
	*Instances
	Kq queue.MessageQueue
}

func NewInstancesFromJson(jsonValue string) *Instances {
	var instances Instances
	err := json.Unmarshal([]byte(jsonValue), &instances)
	if err != nil {
		return nil
	}
	return &instances
}

func NewRevisionDataProducer(dataProducer *DataProducer, revision int64) *RevisionDataProducer {
	return &RevisionDataProducer{
		DataProducer: dataProducer,
		Revision:     revision,
	}
}

func (vp *RevisionDataProducer) GetNameUIDRevisionCombined() (string, error) {
	return vp.UID + "_" + vp.GetRevisionString(), nil
}

// GetRevisionString 获取带'v'前缀的版本字符串
func (vp *RevisionDataProducer) GetRevisionString() string {
	return "v" + fmt.Sprintf("%d", vp.Revision)
}

// NewDataProducerFromJSON 从JSON字符串解析并创建DataProducer
func NewDataProducerFromJSON(jsonValue string) (*DataProducer, error) {
	var dataProducer DataProducer
	err := json.Unmarshal([]byte(jsonValue), &dataProducer)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal data producer JSON: %v", err)
	}
	return &dataProducer, nil
}

// GetColumns 获取RevisionDataProducer的列信息
func (vp *RevisionDataProducer) GetColumns() ([]string, error) {
	// 基础列
	columns := []string{"timestamp", "instance_name", "instance_uid"}

	// 添加数据字段列
	for _, dataGroup := range vp.DataGroups {
		for _, dataNode := range dataGroup.DataNode {
			columns = append(columns, dataNode.Name)
		}
	}

	return columns, nil
}

// WatchList 监听列表结构
type WatchList struct {
	WatchList []string `json:"watch_list"`
}

// NewWatchListFromJSON 从JSON字符串解析并创建WatchList
func NewWatchListFromJSON(jsonValue string) (*WatchList, error) {
	var watchList WatchList
	err := json.Unmarshal([]byte(jsonValue), &watchList)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal watch_list JSON: %v", err)
	}
	return &watchList, nil
}

// IsInWatchList 检查指定的key是否在监听列表中
func (wl *WatchList) IsInWatchList(key string) bool {
	if wl == nil {
		return false
	}
	for _, watchKey := range wl.WatchList {
		if watchKey == key {
			return true
		}
	}
	return false
}

type KafkaMessage struct {
	MetaData KafkaMetaData          `json:"MetaData"`
	Data     map[string]interface{} `json:"Data"`
}

// KafkaMetaData Kafka消息元数据结构体
type KafkaMetaData struct {
	TimeStamp string `json:"TimeStamp"`
	Instance  string `json:"Instance"`
	DataGroup string `json:"DataGroup"`
	Revision  int64  `json:"Revision"`
}

// ParseFromJSON 从JSON字符串解析Kafka消息
func (km *KafkaMessage) ParseFromJSON(value string) error {
	err := json.Unmarshal([]byte(value), km)
	if err != nil {
		return fmt.Errorf("failed to unmarshal JSON: %v", err)
	}

	// 验证必要字段
	if km.MetaData.TimeStamp == "" {
		return fmt.Errorf("missing required field: MetaData.TimeStamp")
	}
	if km.MetaData.Instance == "" {
		return fmt.Errorf("missing required field: MetaData.Instance")
	}
	if km.MetaData.DataGroup == "" {
		return fmt.Errorf("missing required field: MetaData.DataGroup")
	}
	if km.MetaData.Revision == 0 {
		return fmt.Errorf("missing required field: MetaData.Revision")
	}
	if km.Data == nil {
		return fmt.Errorf("missing required field: Data")
	}

	return nil
}

// ValidateCompliance 验证消息数据格式合规性
func (km *KafkaMessage) ValidateCompliance(driver *Driver) error {
	instanceFound := false
	for _, instance := range driver.Instances.Instances {
		if instance.UID == km.MetaData.Instance { 
			instanceFound = true
			break
		}
	}
	if !instanceFound {
		return fmt.Errorf("instance '%s' not found in driver instances", km.MetaData.Instance)
	}

	dataGroup, err := km.getDataGroupFromDriver(km.MetaData.DataGroup, driver)
	if err != nil {
		return fmt.Errorf("failed to get DataGroup configuration: %v", err)
	}

	err = km.validateDataFields(km.Data, dataGroup)
	if err != nil {
		return fmt.Errorf("data field validation failed: %v", err)
	}

	return nil
}

// getDataGroupFromDriver 从driver中获取DataGroup配置
func (km *KafkaMessage) getDataGroupFromDriver(dataGroupName string, driver *Driver) (*DataGroup, error) {
	if driver == nil {
		return nil, fmt.Errorf("driver is nil")
	}
	if driver.DataProducer == nil {
		return nil, fmt.Errorf("driver DataProducer is nil")
	}

	for _, dataGroup := range driver.DataProducer.DataGroups {
		if dataGroup.Name == dataGroupName {
			logx.Debugf("Retrieved DataGroup '%s' from driver with %d data nodes", dataGroupName, len(dataGroup.DataNode))
			return &dataGroup, nil
		}
	}

	return nil, fmt.Errorf("DataGroup '%s' not found in driver configuration", dataGroupName)
}

// validateDataFields 验证数据字段
func (km *KafkaMessage) validateDataFields(data map[string]interface{}, dataGroup *DataGroup) error {
	expectedFields := make(map[string]DataNode)
	for _, dataNode := range dataGroup.DataNode {
		expectedFields[dataNode.Name] = dataNode
	}

	for fieldName, fieldValue := range data {
		dataNode, exists := expectedFields[fieldName]
		if !exists {
			return fmt.Errorf("unexpected field '%s' not defined in DataGroup", fieldName)
		}

		err := km.validateFieldType(fieldName, fieldValue, dataNode)
		if err != nil {
			return err
		}
	}
	logx.Debugf("Data field validation passed for %d fields", len(data))
	return nil
}

// validateFieldType 验证字段类型
func (km *KafkaMessage) validateFieldType(fieldName string, fieldValue interface{}, dataNode DataNode) error {
	fieldType := reflect.TypeOf(fieldValue)
	if fieldType == nil {
		return fmt.Errorf("field '%s' has nil value", fieldName)
	}

	switch fieldType.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		logx.Debugf("Field '%s' is integer type: %v", fieldName, fieldValue)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		logx.Debugf("Field '%s' is unsigned integer type: %v", fieldName, fieldValue)
	case reflect.Float32, reflect.Float64:
		logx.Debugf("Field '%s' is float type: %v", fieldName, fieldValue)
	case reflect.String:
		logx.Debugf("Field '%s' is string type: %v", fieldName, fieldValue)
	case reflect.Bool:
		logx.Debugf("Field '%s' is boolean type: %v", fieldName, fieldValue)
	default:
		return fmt.Errorf("field '%s' has unsupported type: %v", fieldName, fieldType.Kind())
	}

	return nil
}

// StoreToGreptimeDB 将Kafka消息存储到GreptimeDB
func (km *KafkaMessage) StoreToGreptimeDB(driver *Driver, sqlConn *SQLWrapper) error {
	// 将kafkaMessage.Data转换为datarows格式
	dataRows, err := km.ConvertToDataRows(driver)
	if err != nil {
		return fmt.Errorf("failed to convert Kafka data to DataRows: %v", err)
	}
	logx.Infof("Revision %d, Storing %d data rows to GreptimeDB", driver.RevisionDataProducer.Revision, len(dataRows))
	err = sqlConn.InsertData(&driver.RevisionDataProducer, dataRows)
	if err != nil {
		return fmt.Errorf("failed to insert data: %v", err)
	}

	return nil
}

// ConvertToDataRows 将Kafka消息数据转换为DataRow格式
func (km *KafkaMessage) ConvertToDataRows(driver *Driver) ([]*datamanager.DataRow, error) {
	// 解析时间戳
	timestamp, err := time.Parse(time.RFC3339, km.MetaData.TimeStamp)
	if err != nil {
		// 尝试其他时间格式
		timeFormats := []string{
			"2006-01-02T15:04:05.000000",    
			"2006-01-02T15:04:05.000000Z",   
			"2006-01-02T15:04:05.000",       
			"2006-01-02 15:04:05",
			"2006-01-02T15:04:05Z",
			time.RFC3339,
			"2006-01-02 15:04:05.000",
			"2006-01-02 15:04:05 -0700 MST", // 支持带时区的格式
			"2006-01-02 15:04:05 MST",       // 支持仅带时区名称的格式
			time.RFC822,                     // Mon Jan _2 15:04:05 MST 2006
			time.RFC822Z,                    // Mon Jan _2 15:04:05 -0700 2006
		}
		for _, format := range timeFormats {
			if timestamp, err = time.Parse(format, km.MetaData.TimeStamp); err == nil {
				break
			}
		}
		if err != nil {
			return nil, fmt.Errorf("failed to parse timestamp '%s': %v", km.MetaData.TimeStamp, err)
		}
	}

	// 创建SingleData列表
	var singleDataList []*datamanager.SingleData
	for fieldName, fieldValue := range km.Data {
		singleData := &datamanager.SingleData{
			Field: fieldName,
			Value: fmt.Sprintf("%v", fieldValue),
		}
		singleDataList = append(singleDataList, singleData)
	}

	// 根据instanceUID查找对应的InstanceName
	instanceUID := km.MetaData.Instance
	instanceName := ""
	for _, instance := range driver.Instances.Instances {
		if instance.UID == instanceUID {
			instanceName = instance.Name
			break
		}
	}

	dataRow := &datamanager.DataRow{
		Ts: timestamppb.New(timestamp),
		InstanceInfo: &datamanager.InstanceTagInfo{
			InstanceName: instanceName,
			InstanceUid:  instanceUID,
		},
		DatagroupInfo: &datamanager.DataGroupTagInfo{
			DatagroupName: km.MetaData.DataGroup,
		},
		Datas: singleDataList,
	}

	return []*datamanager.DataRow{dataRow}, nil
}

// NewKafkaMessageFromJSON 从JSON字符串创建KafkaMessage实例
func NewKafkaMessageFromJSON(value string) (*KafkaMessage, error) {
	var kafkaMessage KafkaMessage
	err := kafkaMessage.ParseFromJSON(value)
	if err != nil {
		return nil, err
	}
	return &kafkaMessage, nil
}
