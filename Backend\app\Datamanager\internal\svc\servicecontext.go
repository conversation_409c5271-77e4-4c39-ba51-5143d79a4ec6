package svc

import (
	"GCF/app/Datamanager/internal/config"
	"GCF/app/Datamanager/internal/logic/utils"
	"GCF/pkg/zetcd"
	"log"
)

type ServiceContext struct {
	Config        config.Config
	SQLWrapper    *utils.SQLWrapper
	DriverManager *utils.DriverManager // Driver管理器，管理drivers、watch_list和instances
	UtilDataService *utils.UtilDataService
}

func NewServiceContext(c config.Config) *ServiceContext {
	// 初始化数据库连接 - 使用SQLWrapper封装
	sqlWrapper := utils.NewSQLWrapper(&c.SqlConf)

	// 初始化etcd客户端
	ZEtcdClient, err := zetcd.NewZEtcdClient(c.ZEtcdConf)
	if err != nil {
		log.Fatalf("Failed to connect to etcd: %v", err)
	}

	driverManager := utils.NewDriverManager(sqlWrapper, ZEtcdClient, &c.KafkaConf)

	svcCtx := &ServiceContext{
		Config:        c,
		SQLWrapper:    sqlWrapper,
		DriverManager: driverManager,
		UtilDataService: utils.NewUtilDataService(sqlWrapper, driverManager),
	}

	// 启动DriverManager的监听器
	err = svcCtx.DriverManager.StartWatching()
	if err != nil {
		log.Fatalf("Failed to start DriverManager watching: %v", err)
	}

	return svcCtx
}
