// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Data is the model entity for the Data schema.
type Data struct {
	config `json:"-"`
	// ID of the ent.
	// 数据UID
	ID string `json:"id,omitempty"`
	// frame id
	FrameID string `json:"frame_id,omitempty"`
	// Index of the data
	Index string `json:"index,omitempty"`
	// name of the index data
	Name string `json:"name,omitempty"`
	// type of the index data
	Type string `json:"type,omitempty"`
	// Unit of the index data
	Unit string `json:"unit,omitempty"`
	// Description of the device
	Description string `json:"description,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// timestamp of the index data
	UpdateUnix   time.Time `json:"update_unix,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Data) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case data.FieldID, data.FieldFrameID, data.FieldIndex, data.FieldName, data.FieldType, data.FieldUnit, data.FieldDescription:
			values[i] = new(sql.NullString)
		case data.FieldCreateUnix, data.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Data fields.
func (d *Data) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case data.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				d.ID = value.String
			}
		case data.FieldFrameID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field frame_id", values[i])
			} else if value.Valid {
				d.FrameID = value.String
			}
		case data.FieldIndex:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field index", values[i])
			} else if value.Valid {
				d.Index = value.String
			}
		case data.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				d.Name = value.String
			}
		case data.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				d.Type = value.String
			}
		case data.FieldUnit:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field unit", values[i])
			} else if value.Valid {
				d.Unit = value.String
			}
		case data.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				d.Description = value.String
			}
		case data.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				d.CreateUnix = value.Time
			}
		case data.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				d.UpdateUnix = value.Time
			}
		default:
			d.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Data.
// This includes values selected through modifiers, order, etc.
func (d *Data) Value(name string) (ent.Value, error) {
	return d.selectValues.Get(name)
}

// Update returns a builder for updating this Data.
// Note that you need to call Data.Unwrap() before calling this method if this Data
// was returned from a transaction, and the transaction was committed or rolled back.
func (d *Data) Update() *DataUpdateOne {
	return NewDataClient(d.config).UpdateOne(d)
}

// Unwrap unwraps the Data entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (d *Data) Unwrap() *Data {
	_tx, ok := d.config.driver.(*txDriver)
	if !ok {
		panic("ent: Data is not a transactional entity")
	}
	d.config.driver = _tx.drv
	return d
}

// String implements the fmt.Stringer.
func (d *Data) String() string {
	var builder strings.Builder
	builder.WriteString("Data(")
	builder.WriteString(fmt.Sprintf("id=%v, ", d.ID))
	builder.WriteString("frame_id=")
	builder.WriteString(d.FrameID)
	builder.WriteString(", ")
	builder.WriteString("index=")
	builder.WriteString(d.Index)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(d.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(d.Type)
	builder.WriteString(", ")
	builder.WriteString("unit=")
	builder.WriteString(d.Unit)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(d.Description)
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(d.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(d.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// DataSlice is a parsable slice of Data.
type DataSlice []*Data
