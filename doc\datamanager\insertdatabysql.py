import pymysql
from datetime import datetime
import time,random,math

# MySQL连接配置
MYSQL_HOST = 'localhost'
MYSQL_PORT = 4002
MYSQL_USER = ''
MYSQL_PASSWORD = ''
MYSQL_DB = 'public'

# 固定值
DATAPRODUCERINSTUID = '09bf1d87-2306-4e29-8551-bdcb2b0e356e'
DATAGROUPNAME = 'udp_14ad7f64-383b-4787-a017-e5c64889cb28'
version='1366'
DATAPRODUCERINSTUname=f'edge_{DATAPRODUCERINSTUID}'
def insert_data(conn, table, ts, a, b, c, d, e):
    try:
        with conn.cursor() as cursor:
            sql = f"""
            INSERT INTO `{table}` (ts, dataproducerinstuid, datagroupname, a, b, c, d, e)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (ts, DATAPRODUCERINSTUID, DATAGROUPNAME, str(a), str(b), str(c), str(d), str(e)))
        conn.commit()
    except Exception as e:
        print("Insert error:", e)

# 示例用法
if __name__ == '__main__':
    conn = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4'
    )
    table_nm=f'{DATAPRODUCERINSTUname}_v{version}'
    for i in range(10000):
        #ts用unix时间戳
        time.sleep(0.1)
        ts = int(datetime.now().timestamp())
        # 模拟数据，a为 sin（i），b为 cos（i），c为 tan（i），d为累加，e为5000-10000的随机整数
        a = math.sin(2*math.pi*(i%10))
        b = math.cos(2*math.pi*(i%10))+0.5
        c = math.tan(i)
        d = i
        e = random.randint(5000, 10000)
        insert_data(conn, table_nm, ts, a, b, c, d, e)  # Update to use tan(i)
        print(f"Inserted data: ts={ts}, a={a}, b={b}, c={c}, d={d}, e={e}")
