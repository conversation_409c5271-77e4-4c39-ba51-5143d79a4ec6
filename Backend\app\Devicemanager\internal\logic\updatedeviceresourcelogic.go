package logic

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type UpdateDeviceResourceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDeviceResourceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDeviceResourceLogic {
	return &UpdateDeviceResourceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDeviceResourceLogic) UpdateDeviceResource(req *types.UpdateDeviceResourceRequest) (resp *types.UpdateDeviceResourceResponse, err error) {
	//检验必要项
	if req.DeviceResourceInfo.IP == "" || req.DeviceResourceInfo.Type == "" || req.DeviceResourceInfo.Protocol == nil || req.DeviceUID == "" {
		return nil, xerrors.New(http.StatusBadRequest, "未输入必填字段")
	}

	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备失败 : %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}

	//TODO: 根据状态决定是否可以编辑
	if DeviceWorkStatus.HealthStatus == utils.HealthStatusRunning {
		Msg := fmt.Errorf("设备仍在运行，请先停止设备")
		return nil, xerrors.New(http.StatusForbidden, Msg.Error())
	}

	/*DeviceResourceInfo, err := utils.GetDeviceResourceInfo(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, xerrors.New(http.StatusNotFound, "设备资源信息获取失败")
	}*/

	//获取帧info数据和deviceMeta数据
	//Protocols := strings.Split(queryDevice.Protocol, ",")
	/*DeviceMeta, _, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, Protocols, queryDevice.ID)
	if err != nil {
		return nil, xerrors.New(http.StatusNotFound, "帧info数据和deviceMeta数据获取失败")
	}*/

	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, xerrors.New(http.StatusForbidden, "更新设备事务启动失败")
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()
	cpuResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.CPU)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "CPU数据转换失败")
	}
	gpuResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.GPU)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "GPU数据转换失败")
	}
	diskResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.Disk)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "DISK资源转换失败")
	}
	memResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.Mem)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "MEM资源转换失败")
	}
	devUpd := tx.Device.Update().Where(device.IDEQ(req.DeviceUID)).
		SetName(req.DeviceResourceInfo.DeviceName).
		SetIP(req.DeviceResourceInfo.IP).
		SetOs(req.DeviceResourceInfo.OS).
		SetType(req.DeviceResourceInfo.Type).
		SetCPU(cpuResource).
		SetGpu(gpuResource).
		SetMemory(memResource).
		SetDisk(diskResource).
		SetProtocol(strings.Join(req.DeviceResourceInfo.Protocol, ",")).
		SetWorkmode(DeviceWorkStatus.WorkMode).
		SetStatus(DeviceWorkStatus.HealthStatus).
		SetDescription(req.DeviceResourceInfo.Description)
	if _, err = devUpd.Save(l.ctx); err != nil {
		_ = tx.Rollback()
		Msg := fmt.Errorf("设备更新失败: %w", err)
		return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
	}

	if err = tx.Commit(); err != nil {
		Msg := fmt.Errorf("更新设备请求提交失败: %w", err)
		return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
	}

	// 1. 事务提交成功后，重新查设备
	updatedQueryDevice, err := l.svcCtx.Db.Device.Get(l.ctx, req.DeviceUID)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "重新获取设备失败")
	}

	// 2. 用最新的设备实体去查
	updatedDeviceMeta, _, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, strings.Split(updatedQueryDevice.Protocol, ","), updatedQueryDevice.ID)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "更新后获取DeviceMeta失败")
	}

	updatedDeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, updatedQueryDevice)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "更新后获取DeviceWorkStatus失败")
	}

	updatedDeviceResourceInfo, err := utils.GetDeviceResourceInfo(l.ctx, l.svcCtx, updatedQueryDevice)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "更新后获取DeviceResourceInfo失败")
	}

	// 添加到Etcd
	err = utils.Pub2Etcd(l.ctx, l.svcCtx)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "failed to publish to Etcd: "+err.Error())
	}

	return &types.UpdateDeviceResourceResponse{
		DeviceMeta:         *updatedDeviceMeta,
		DeviceWorkStatus:   *updatedDeviceWorkStatus,
		DeviceResourceInfo: *updatedDeviceResourceInfo,
	}, nil
}
