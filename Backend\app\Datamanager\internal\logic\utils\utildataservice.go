package utils

import (
	"GCF/app/Datamanager/internal/datamanager"
	"context"
)

type UtilDataService struct {
	sqlWrapper    *SQLWrapper
	driverManager *DriverManager
}

func NewUtilDataService(sqlWrapper *SQLWrapper, driverManager *DriverManager) *UtilDataService {
	return &UtilDataService{
		sqlWrapper:    sqlWrapper,
		driverManager: driverManager,
	}
}

func (s *UtilDataService) GetInstanceData(ctx context.Context, req *datamanager.GetInstanceDataRequest) (*datamanager.GetInstanceDataResponse, error) {
	var err error

	revisionProducer, err := s.driverManager.GetRevisionProducerByInstanceUID(
		req.InstanceInfo.InstanceUid,
		req.Revision,
	)
	if err != nil {
		return nil, err
	}

	dataRows, err := s.sqlWrapper.QueryData(
		revisionProducer,
		req.WhereInfos,
		int64(req.DataRowLimit),
		nil, // tableInfo
		req.DatagroupInfo,
		req.InstanceInfo,
		req.FieldsInfo,
	)
	if err != nil {
		return nil, err
	}
	
	return &datamanager.GetInstanceDataResponse{
		Status:    "success",
		DataCount: uint64(len(dataRows)),
		DataRows:  dataRows,
	}, nil
}

func (s *UtilDataService) GetProducerData(ctx context.Context, req *datamanager.GetProducerDataRequest) (*datamanager.GetProducerDataResponse, error) {
	var err error

	revisionedProducer, err := s.driverManager.GetRevisionProducerByProducerUID(
		req.TableInfo.TableUid,
		req.Revision,
	)
	if err != nil {
		return nil, err
	}

	// 使用SQLWrapper的QueryData方法
	dataRows, err := s.sqlWrapper.QueryData(
		revisionedProducer,
		req.WhereInfos,
		int64(req.DataRowLimit),
		req.TableInfo,
		req.DatagroupInfo,
		req.InstanceInfo,
		req.FieldsInfo,
	)
	if err != nil {
		return nil, err
	}

	return &datamanager.GetProducerDataResponse{
		Status:    "success",
		DataCount: uint64(len(dataRows)),
		DataRows:  dataRows,
	}, nil
}

func (s *UtilDataService) DeleteInstanceData(ctx context.Context, req *datamanager.DeleteInstanceDataRequest) (*datamanager.DeleteInstanceDataResponse, error) {
	var err error

	revisionedProducer, err := s.driverManager.GetRevisionProducerByInstanceUID(
		req.InstanceInfo.InstanceUid,
		req.Revision,
	)
	if err != nil {
		return nil, err
	}

	// 使用SQLWrapper的DeleteData方法
	rowsAffected, err := s.sqlWrapper.DeleteData(revisionedProducer, req.WhereInfos, nil, req.DatagroupInfo, req.InstanceInfo, req.FieldsInfo)
	if err != nil {
		return nil, err
	}

	return &datamanager.DeleteInstanceDataResponse{
		Status:       "success",
		RowsAffected: uint64(rowsAffected),
	}, nil
}