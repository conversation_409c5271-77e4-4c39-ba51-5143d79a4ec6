// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Modelmanager/internal/ent/model"
	"GCF/app/Modelmanager/internal/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ModelUpdate is the builder for updating Model entities.
type ModelUpdate struct {
	config
	hooks    []Hook
	mutation *ModelMutation
}

// Where appends a list predicates to the ModelUpdate builder.
func (mu *ModelUpdate) Where(ps ...predicate.Model) *ModelUpdate {
	mu.mutation.Where(ps...)
	return mu
}

// SetModelNameVersion sets the "model_name_version" field.
func (mu *ModelUpdate) SetModelNameVersion(s string) *ModelUpdate {
	mu.mutation.SetModelNameVersion(s)
	return mu
}

// SetNillableModelNameVersion sets the "model_name_version" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableModelNameVersion(s *string) *ModelUpdate {
	if s != nil {
		mu.SetModelNameVersion(*s)
	}
	return mu
}

// SetFrameworkVersion sets the "framework_version" field.
func (mu *ModelUpdate) SetFrameworkVersion(s string) *ModelUpdate {
	mu.mutation.SetFrameworkVersion(s)
	return mu
}

// SetNillableFrameworkVersion sets the "framework_version" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableFrameworkVersion(s *string) *ModelUpdate {
	if s != nil {
		mu.SetFrameworkVersion(*s)
	}
	return mu
}

// SetTypeDesc sets the "type_desc" field.
func (mu *ModelUpdate) SetTypeDesc(s string) *ModelUpdate {
	mu.mutation.SetTypeDesc(s)
	return mu
}

// SetNillableTypeDesc sets the "type_desc" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableTypeDesc(s *string) *ModelUpdate {
	if s != nil {
		mu.SetTypeDesc(*s)
	}
	return mu
}

// SetStoragePath sets the "storage_path" field.
func (mu *ModelUpdate) SetStoragePath(s string) *ModelUpdate {
	mu.mutation.SetStoragePath(s)
	return mu
}

// SetNillableStoragePath sets the "storage_path" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableStoragePath(s *string) *ModelUpdate {
	if s != nil {
		mu.SetStoragePath(*s)
	}
	return mu
}

// SetAccuracy sets the "accuracy" field.
func (mu *ModelUpdate) SetAccuracy(f float64) *ModelUpdate {
	mu.mutation.ResetAccuracy()
	mu.mutation.SetAccuracy(f)
	return mu
}

// SetNillableAccuracy sets the "accuracy" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableAccuracy(f *float64) *ModelUpdate {
	if f != nil {
		mu.SetAccuracy(*f)
	}
	return mu
}

// AddAccuracy adds f to the "accuracy" field.
func (mu *ModelUpdate) AddAccuracy(f float64) *ModelUpdate {
	mu.mutation.AddAccuracy(f)
	return mu
}

// SetPrecision sets the "precision" field.
func (mu *ModelUpdate) SetPrecision(f float64) *ModelUpdate {
	mu.mutation.ResetPrecision()
	mu.mutation.SetPrecision(f)
	return mu
}

// SetNillablePrecision sets the "precision" field if the given value is not nil.
func (mu *ModelUpdate) SetNillablePrecision(f *float64) *ModelUpdate {
	if f != nil {
		mu.SetPrecision(*f)
	}
	return mu
}

// AddPrecision adds f to the "precision" field.
func (mu *ModelUpdate) AddPrecision(f float64) *ModelUpdate {
	mu.mutation.AddPrecision(f)
	return mu
}

// SetRecall sets the "recall" field.
func (mu *ModelUpdate) SetRecall(f float64) *ModelUpdate {
	mu.mutation.ResetRecall()
	mu.mutation.SetRecall(f)
	return mu
}

// SetNillableRecall sets the "recall" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableRecall(f *float64) *ModelUpdate {
	if f != nil {
		mu.SetRecall(*f)
	}
	return mu
}

// AddRecall adds f to the "recall" field.
func (mu *ModelUpdate) AddRecall(f float64) *ModelUpdate {
	mu.mutation.AddRecall(f)
	return mu
}

// SetF1Score sets the "f1_score" field.
func (mu *ModelUpdate) SetF1Score(f float64) *ModelUpdate {
	mu.mutation.ResetF1Score()
	mu.mutation.SetF1Score(f)
	return mu
}

// SetNillableF1Score sets the "f1_score" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableF1Score(f *float64) *ModelUpdate {
	if f != nil {
		mu.SetF1Score(*f)
	}
	return mu
}

// AddF1Score adds f to the "f1_score" field.
func (mu *ModelUpdate) AddF1Score(f float64) *ModelUpdate {
	mu.mutation.AddF1Score(f)
	return mu
}

// SetCreatedBy sets the "created_by" field.
func (mu *ModelUpdate) SetCreatedBy(s string) *ModelUpdate {
	mu.mutation.SetCreatedBy(s)
	return mu
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (mu *ModelUpdate) SetNillableCreatedBy(s *string) *ModelUpdate {
	if s != nil {
		mu.SetCreatedBy(*s)
	}
	return mu
}

// SetUpdateUnix sets the "update_unix" field.
func (mu *ModelUpdate) SetUpdateUnix(t time.Time) *ModelUpdate {
	mu.mutation.SetUpdateUnix(t)
	return mu
}

// Mutation returns the ModelMutation object of the builder.
func (mu *ModelUpdate) Mutation() *ModelMutation {
	return mu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (mu *ModelUpdate) Save(ctx context.Context) (int, error) {
	mu.defaults()
	return withHooks(ctx, mu.sqlSave, mu.mutation, mu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (mu *ModelUpdate) SaveX(ctx context.Context) int {
	affected, err := mu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (mu *ModelUpdate) Exec(ctx context.Context) error {
	_, err := mu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mu *ModelUpdate) ExecX(ctx context.Context) {
	if err := mu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mu *ModelUpdate) defaults() {
	if _, ok := mu.mutation.UpdateUnix(); !ok {
		v := model.UpdateDefaultUpdateUnix()
		mu.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mu *ModelUpdate) check() error {
	if v, ok := mu.mutation.StoragePath(); ok {
		if err := model.StoragePathValidator(v); err != nil {
			return &ValidationError{Name: "storage_path", err: fmt.Errorf(`ent: validator failed for field "Model.storage_path": %w`, err)}
		}
	}
	if v, ok := mu.mutation.Accuracy(); ok {
		if err := model.AccuracyValidator(v); err != nil {
			return &ValidationError{Name: "accuracy", err: fmt.Errorf(`ent: validator failed for field "Model.accuracy": %w`, err)}
		}
	}
	if v, ok := mu.mutation.Precision(); ok {
		if err := model.PrecisionValidator(v); err != nil {
			return &ValidationError{Name: "precision", err: fmt.Errorf(`ent: validator failed for field "Model.precision": %w`, err)}
		}
	}
	if v, ok := mu.mutation.Recall(); ok {
		if err := model.RecallValidator(v); err != nil {
			return &ValidationError{Name: "recall", err: fmt.Errorf(`ent: validator failed for field "Model.recall": %w`, err)}
		}
	}
	if v, ok := mu.mutation.F1Score(); ok {
		if err := model.F1ScoreValidator(v); err != nil {
			return &ValidationError{Name: "f1_score", err: fmt.Errorf(`ent: validator failed for field "Model.f1_score": %w`, err)}
		}
	}
	return nil
}

func (mu *ModelUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := mu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(model.Table, model.Columns, sqlgraph.NewFieldSpec(model.FieldID, field.TypeInt))
	if ps := mu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := mu.mutation.ModelNameVersion(); ok {
		_spec.SetField(model.FieldModelNameVersion, field.TypeString, value)
	}
	if value, ok := mu.mutation.FrameworkVersion(); ok {
		_spec.SetField(model.FieldFrameworkVersion, field.TypeString, value)
	}
	if value, ok := mu.mutation.TypeDesc(); ok {
		_spec.SetField(model.FieldTypeDesc, field.TypeString, value)
	}
	if value, ok := mu.mutation.StoragePath(); ok {
		_spec.SetField(model.FieldStoragePath, field.TypeString, value)
	}
	if value, ok := mu.mutation.Accuracy(); ok {
		_spec.SetField(model.FieldAccuracy, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.AddedAccuracy(); ok {
		_spec.AddField(model.FieldAccuracy, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.Precision(); ok {
		_spec.SetField(model.FieldPrecision, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.AddedPrecision(); ok {
		_spec.AddField(model.FieldPrecision, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.Recall(); ok {
		_spec.SetField(model.FieldRecall, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.AddedRecall(); ok {
		_spec.AddField(model.FieldRecall, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.F1Score(); ok {
		_spec.SetField(model.FieldF1Score, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.AddedF1Score(); ok {
		_spec.AddField(model.FieldF1Score, field.TypeFloat64, value)
	}
	if value, ok := mu.mutation.CreatedBy(); ok {
		_spec.SetField(model.FieldCreatedBy, field.TypeString, value)
	}
	if value, ok := mu.mutation.UpdateUnix(); ok {
		_spec.SetField(model.FieldUpdateUnix, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, mu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{model.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	mu.mutation.done = true
	return n, nil
}

// ModelUpdateOne is the builder for updating a single Model entity.
type ModelUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ModelMutation
}

// SetModelNameVersion sets the "model_name_version" field.
func (muo *ModelUpdateOne) SetModelNameVersion(s string) *ModelUpdateOne {
	muo.mutation.SetModelNameVersion(s)
	return muo
}

// SetNillableModelNameVersion sets the "model_name_version" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableModelNameVersion(s *string) *ModelUpdateOne {
	if s != nil {
		muo.SetModelNameVersion(*s)
	}
	return muo
}

// SetFrameworkVersion sets the "framework_version" field.
func (muo *ModelUpdateOne) SetFrameworkVersion(s string) *ModelUpdateOne {
	muo.mutation.SetFrameworkVersion(s)
	return muo
}

// SetNillableFrameworkVersion sets the "framework_version" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableFrameworkVersion(s *string) *ModelUpdateOne {
	if s != nil {
		muo.SetFrameworkVersion(*s)
	}
	return muo
}

// SetTypeDesc sets the "type_desc" field.
func (muo *ModelUpdateOne) SetTypeDesc(s string) *ModelUpdateOne {
	muo.mutation.SetTypeDesc(s)
	return muo
}

// SetNillableTypeDesc sets the "type_desc" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableTypeDesc(s *string) *ModelUpdateOne {
	if s != nil {
		muo.SetTypeDesc(*s)
	}
	return muo
}

// SetStoragePath sets the "storage_path" field.
func (muo *ModelUpdateOne) SetStoragePath(s string) *ModelUpdateOne {
	muo.mutation.SetStoragePath(s)
	return muo
}

// SetNillableStoragePath sets the "storage_path" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableStoragePath(s *string) *ModelUpdateOne {
	if s != nil {
		muo.SetStoragePath(*s)
	}
	return muo
}

// SetAccuracy sets the "accuracy" field.
func (muo *ModelUpdateOne) SetAccuracy(f float64) *ModelUpdateOne {
	muo.mutation.ResetAccuracy()
	muo.mutation.SetAccuracy(f)
	return muo
}

// SetNillableAccuracy sets the "accuracy" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableAccuracy(f *float64) *ModelUpdateOne {
	if f != nil {
		muo.SetAccuracy(*f)
	}
	return muo
}

// AddAccuracy adds f to the "accuracy" field.
func (muo *ModelUpdateOne) AddAccuracy(f float64) *ModelUpdateOne {
	muo.mutation.AddAccuracy(f)
	return muo
}

// SetPrecision sets the "precision" field.
func (muo *ModelUpdateOne) SetPrecision(f float64) *ModelUpdateOne {
	muo.mutation.ResetPrecision()
	muo.mutation.SetPrecision(f)
	return muo
}

// SetNillablePrecision sets the "precision" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillablePrecision(f *float64) *ModelUpdateOne {
	if f != nil {
		muo.SetPrecision(*f)
	}
	return muo
}

// AddPrecision adds f to the "precision" field.
func (muo *ModelUpdateOne) AddPrecision(f float64) *ModelUpdateOne {
	muo.mutation.AddPrecision(f)
	return muo
}

// SetRecall sets the "recall" field.
func (muo *ModelUpdateOne) SetRecall(f float64) *ModelUpdateOne {
	muo.mutation.ResetRecall()
	muo.mutation.SetRecall(f)
	return muo
}

// SetNillableRecall sets the "recall" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableRecall(f *float64) *ModelUpdateOne {
	if f != nil {
		muo.SetRecall(*f)
	}
	return muo
}

// AddRecall adds f to the "recall" field.
func (muo *ModelUpdateOne) AddRecall(f float64) *ModelUpdateOne {
	muo.mutation.AddRecall(f)
	return muo
}

// SetF1Score sets the "f1_score" field.
func (muo *ModelUpdateOne) SetF1Score(f float64) *ModelUpdateOne {
	muo.mutation.ResetF1Score()
	muo.mutation.SetF1Score(f)
	return muo
}

// SetNillableF1Score sets the "f1_score" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableF1Score(f *float64) *ModelUpdateOne {
	if f != nil {
		muo.SetF1Score(*f)
	}
	return muo
}

// AddF1Score adds f to the "f1_score" field.
func (muo *ModelUpdateOne) AddF1Score(f float64) *ModelUpdateOne {
	muo.mutation.AddF1Score(f)
	return muo
}

// SetCreatedBy sets the "created_by" field.
func (muo *ModelUpdateOne) SetCreatedBy(s string) *ModelUpdateOne {
	muo.mutation.SetCreatedBy(s)
	return muo
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (muo *ModelUpdateOne) SetNillableCreatedBy(s *string) *ModelUpdateOne {
	if s != nil {
		muo.SetCreatedBy(*s)
	}
	return muo
}

// SetUpdateUnix sets the "update_unix" field.
func (muo *ModelUpdateOne) SetUpdateUnix(t time.Time) *ModelUpdateOne {
	muo.mutation.SetUpdateUnix(t)
	return muo
}

// Mutation returns the ModelMutation object of the builder.
func (muo *ModelUpdateOne) Mutation() *ModelMutation {
	return muo.mutation
}

// Where appends a list predicates to the ModelUpdate builder.
func (muo *ModelUpdateOne) Where(ps ...predicate.Model) *ModelUpdateOne {
	muo.mutation.Where(ps...)
	return muo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (muo *ModelUpdateOne) Select(field string, fields ...string) *ModelUpdateOne {
	muo.fields = append([]string{field}, fields...)
	return muo
}

// Save executes the query and returns the updated Model entity.
func (muo *ModelUpdateOne) Save(ctx context.Context) (*Model, error) {
	muo.defaults()
	return withHooks(ctx, muo.sqlSave, muo.mutation, muo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (muo *ModelUpdateOne) SaveX(ctx context.Context) *Model {
	node, err := muo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (muo *ModelUpdateOne) Exec(ctx context.Context) error {
	_, err := muo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (muo *ModelUpdateOne) ExecX(ctx context.Context) {
	if err := muo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (muo *ModelUpdateOne) defaults() {
	if _, ok := muo.mutation.UpdateUnix(); !ok {
		v := model.UpdateDefaultUpdateUnix()
		muo.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (muo *ModelUpdateOne) check() error {
	if v, ok := muo.mutation.StoragePath(); ok {
		if err := model.StoragePathValidator(v); err != nil {
			return &ValidationError{Name: "storage_path", err: fmt.Errorf(`ent: validator failed for field "Model.storage_path": %w`, err)}
		}
	}
	if v, ok := muo.mutation.Accuracy(); ok {
		if err := model.AccuracyValidator(v); err != nil {
			return &ValidationError{Name: "accuracy", err: fmt.Errorf(`ent: validator failed for field "Model.accuracy": %w`, err)}
		}
	}
	if v, ok := muo.mutation.Precision(); ok {
		if err := model.PrecisionValidator(v); err != nil {
			return &ValidationError{Name: "precision", err: fmt.Errorf(`ent: validator failed for field "Model.precision": %w`, err)}
		}
	}
	if v, ok := muo.mutation.Recall(); ok {
		if err := model.RecallValidator(v); err != nil {
			return &ValidationError{Name: "recall", err: fmt.Errorf(`ent: validator failed for field "Model.recall": %w`, err)}
		}
	}
	if v, ok := muo.mutation.F1Score(); ok {
		if err := model.F1ScoreValidator(v); err != nil {
			return &ValidationError{Name: "f1_score", err: fmt.Errorf(`ent: validator failed for field "Model.f1_score": %w`, err)}
		}
	}
	return nil
}

func (muo *ModelUpdateOne) sqlSave(ctx context.Context) (_node *Model, err error) {
	if err := muo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(model.Table, model.Columns, sqlgraph.NewFieldSpec(model.FieldID, field.TypeInt))
	id, ok := muo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Model.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := muo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, model.FieldID)
		for _, f := range fields {
			if !model.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != model.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := muo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := muo.mutation.ModelNameVersion(); ok {
		_spec.SetField(model.FieldModelNameVersion, field.TypeString, value)
	}
	if value, ok := muo.mutation.FrameworkVersion(); ok {
		_spec.SetField(model.FieldFrameworkVersion, field.TypeString, value)
	}
	if value, ok := muo.mutation.TypeDesc(); ok {
		_spec.SetField(model.FieldTypeDesc, field.TypeString, value)
	}
	if value, ok := muo.mutation.StoragePath(); ok {
		_spec.SetField(model.FieldStoragePath, field.TypeString, value)
	}
	if value, ok := muo.mutation.Accuracy(); ok {
		_spec.SetField(model.FieldAccuracy, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.AddedAccuracy(); ok {
		_spec.AddField(model.FieldAccuracy, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.Precision(); ok {
		_spec.SetField(model.FieldPrecision, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.AddedPrecision(); ok {
		_spec.AddField(model.FieldPrecision, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.Recall(); ok {
		_spec.SetField(model.FieldRecall, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.AddedRecall(); ok {
		_spec.AddField(model.FieldRecall, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.F1Score(); ok {
		_spec.SetField(model.FieldF1Score, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.AddedF1Score(); ok {
		_spec.AddField(model.FieldF1Score, field.TypeFloat64, value)
	}
	if value, ok := muo.mutation.CreatedBy(); ok {
		_spec.SetField(model.FieldCreatedBy, field.TypeString, value)
	}
	if value, ok := muo.mutation.UpdateUnix(); ok {
		_spec.SetField(model.FieldUpdateUnix, field.TypeTime, value)
	}
	_node = &Model{config: muo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, muo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{model.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	muo.mutation.done = true
	return _node, nil
}
