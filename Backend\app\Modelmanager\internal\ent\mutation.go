// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Modelmanager/internal/ent/model"
	"GCF/app/Modelmanager/internal/ent/predicate"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeModel = "Model"
)

// ModelMutation represents an operation that mutates the Model nodes in the graph.
type ModelMutation struct {
	config
	op                 Op
	typ                string
	id                 *int
	model_id           *string
	model_name_version *string
	framework_version  *string
	type_desc          *string
	storage_path       *string
	accuracy           *float64
	addaccuracy        *float64
	precision          *float64
	addprecision       *float64
	recall             *float64
	addrecall          *float64
	f1_score           *float64
	addf1_score        *float64
	created_by         *string
	create_unix        *time.Time
	update_unix        *time.Time
	clearedFields      map[string]struct{}
	done               bool
	oldValue           func(context.Context) (*Model, error)
	predicates         []predicate.Model
}

var _ ent.Mutation = (*ModelMutation)(nil)

// modelOption allows management of the mutation configuration using functional options.
type modelOption func(*ModelMutation)

// newModelMutation creates new mutation for the Model entity.
func newModelMutation(c config, op Op, opts ...modelOption) *ModelMutation {
	m := &ModelMutation{
		config:        c,
		op:            op,
		typ:           TypeModel,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withModelID sets the ID field of the mutation.
func withModelID(id int) modelOption {
	return func(m *ModelMutation) {
		var (
			err   error
			once  sync.Once
			value *Model
		)
		m.oldValue = func(ctx context.Context) (*Model, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Model.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withModel sets the old Model of the mutation.
func withModel(node *Model) modelOption {
	return func(m *ModelMutation) {
		m.oldValue = func(context.Context) (*Model, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ModelMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ModelMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ModelMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ModelMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Model.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetModelID sets the "model_id" field.
func (m *ModelMutation) SetModelID(s string) {
	m.model_id = &s
}

// ModelID returns the value of the "model_id" field in the mutation.
func (m *ModelMutation) ModelID() (r string, exists bool) {
	v := m.model_id
	if v == nil {
		return
	}
	return *v, true
}

// OldModelID returns the old "model_id" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldModelID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldModelID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldModelID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldModelID: %w", err)
	}
	return oldValue.ModelID, nil
}

// ResetModelID resets all changes to the "model_id" field.
func (m *ModelMutation) ResetModelID() {
	m.model_id = nil
}

// SetModelNameVersion sets the "model_name_version" field.
func (m *ModelMutation) SetModelNameVersion(s string) {
	m.model_name_version = &s
}

// ModelNameVersion returns the value of the "model_name_version" field in the mutation.
func (m *ModelMutation) ModelNameVersion() (r string, exists bool) {
	v := m.model_name_version
	if v == nil {
		return
	}
	return *v, true
}

// OldModelNameVersion returns the old "model_name_version" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldModelNameVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldModelNameVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldModelNameVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldModelNameVersion: %w", err)
	}
	return oldValue.ModelNameVersion, nil
}

// ResetModelNameVersion resets all changes to the "model_name_version" field.
func (m *ModelMutation) ResetModelNameVersion() {
	m.model_name_version = nil
}

// SetFrameworkVersion sets the "framework_version" field.
func (m *ModelMutation) SetFrameworkVersion(s string) {
	m.framework_version = &s
}

// FrameworkVersion returns the value of the "framework_version" field in the mutation.
func (m *ModelMutation) FrameworkVersion() (r string, exists bool) {
	v := m.framework_version
	if v == nil {
		return
	}
	return *v, true
}

// OldFrameworkVersion returns the old "framework_version" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldFrameworkVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFrameworkVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFrameworkVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFrameworkVersion: %w", err)
	}
	return oldValue.FrameworkVersion, nil
}

// ResetFrameworkVersion resets all changes to the "framework_version" field.
func (m *ModelMutation) ResetFrameworkVersion() {
	m.framework_version = nil
}

// SetTypeDesc sets the "type_desc" field.
func (m *ModelMutation) SetTypeDesc(s string) {
	m.type_desc = &s
}

// TypeDesc returns the value of the "type_desc" field in the mutation.
func (m *ModelMutation) TypeDesc() (r string, exists bool) {
	v := m.type_desc
	if v == nil {
		return
	}
	return *v, true
}

// OldTypeDesc returns the old "type_desc" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldTypeDesc(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTypeDesc is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTypeDesc requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTypeDesc: %w", err)
	}
	return oldValue.TypeDesc, nil
}

// ResetTypeDesc resets all changes to the "type_desc" field.
func (m *ModelMutation) ResetTypeDesc() {
	m.type_desc = nil
}

// SetStoragePath sets the "storage_path" field.
func (m *ModelMutation) SetStoragePath(s string) {
	m.storage_path = &s
}

// StoragePath returns the value of the "storage_path" field in the mutation.
func (m *ModelMutation) StoragePath() (r string, exists bool) {
	v := m.storage_path
	if v == nil {
		return
	}
	return *v, true
}

// OldStoragePath returns the old "storage_path" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldStoragePath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStoragePath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStoragePath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStoragePath: %w", err)
	}
	return oldValue.StoragePath, nil
}

// ResetStoragePath resets all changes to the "storage_path" field.
func (m *ModelMutation) ResetStoragePath() {
	m.storage_path = nil
}

// SetAccuracy sets the "accuracy" field.
func (m *ModelMutation) SetAccuracy(f float64) {
	m.accuracy = &f
	m.addaccuracy = nil
}

// Accuracy returns the value of the "accuracy" field in the mutation.
func (m *ModelMutation) Accuracy() (r float64, exists bool) {
	v := m.accuracy
	if v == nil {
		return
	}
	return *v, true
}

// OldAccuracy returns the old "accuracy" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldAccuracy(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccuracy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccuracy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccuracy: %w", err)
	}
	return oldValue.Accuracy, nil
}

// AddAccuracy adds f to the "accuracy" field.
func (m *ModelMutation) AddAccuracy(f float64) {
	if m.addaccuracy != nil {
		*m.addaccuracy += f
	} else {
		m.addaccuracy = &f
	}
}

// AddedAccuracy returns the value that was added to the "accuracy" field in this mutation.
func (m *ModelMutation) AddedAccuracy() (r float64, exists bool) {
	v := m.addaccuracy
	if v == nil {
		return
	}
	return *v, true
}

// ResetAccuracy resets all changes to the "accuracy" field.
func (m *ModelMutation) ResetAccuracy() {
	m.accuracy = nil
	m.addaccuracy = nil
}

// SetPrecision sets the "precision" field.
func (m *ModelMutation) SetPrecision(f float64) {
	m.precision = &f
	m.addprecision = nil
}

// Precision returns the value of the "precision" field in the mutation.
func (m *ModelMutation) Precision() (r float64, exists bool) {
	v := m.precision
	if v == nil {
		return
	}
	return *v, true
}

// OldPrecision returns the old "precision" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldPrecision(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPrecision is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPrecision requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPrecision: %w", err)
	}
	return oldValue.Precision, nil
}

// AddPrecision adds f to the "precision" field.
func (m *ModelMutation) AddPrecision(f float64) {
	if m.addprecision != nil {
		*m.addprecision += f
	} else {
		m.addprecision = &f
	}
}

// AddedPrecision returns the value that was added to the "precision" field in this mutation.
func (m *ModelMutation) AddedPrecision() (r float64, exists bool) {
	v := m.addprecision
	if v == nil {
		return
	}
	return *v, true
}

// ResetPrecision resets all changes to the "precision" field.
func (m *ModelMutation) ResetPrecision() {
	m.precision = nil
	m.addprecision = nil
}

// SetRecall sets the "recall" field.
func (m *ModelMutation) SetRecall(f float64) {
	m.recall = &f
	m.addrecall = nil
}

// Recall returns the value of the "recall" field in the mutation.
func (m *ModelMutation) Recall() (r float64, exists bool) {
	v := m.recall
	if v == nil {
		return
	}
	return *v, true
}

// OldRecall returns the old "recall" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldRecall(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRecall is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRecall requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRecall: %w", err)
	}
	return oldValue.Recall, nil
}

// AddRecall adds f to the "recall" field.
func (m *ModelMutation) AddRecall(f float64) {
	if m.addrecall != nil {
		*m.addrecall += f
	} else {
		m.addrecall = &f
	}
}

// AddedRecall returns the value that was added to the "recall" field in this mutation.
func (m *ModelMutation) AddedRecall() (r float64, exists bool) {
	v := m.addrecall
	if v == nil {
		return
	}
	return *v, true
}

// ResetRecall resets all changes to the "recall" field.
func (m *ModelMutation) ResetRecall() {
	m.recall = nil
	m.addrecall = nil
}

// SetF1Score sets the "f1_score" field.
func (m *ModelMutation) SetF1Score(f float64) {
	m.f1_score = &f
	m.addf1_score = nil
}

// F1Score returns the value of the "f1_score" field in the mutation.
func (m *ModelMutation) F1Score() (r float64, exists bool) {
	v := m.f1_score
	if v == nil {
		return
	}
	return *v, true
}

// OldF1Score returns the old "f1_score" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldF1Score(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldF1Score is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldF1Score requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldF1Score: %w", err)
	}
	return oldValue.F1Score, nil
}

// AddF1Score adds f to the "f1_score" field.
func (m *ModelMutation) AddF1Score(f float64) {
	if m.addf1_score != nil {
		*m.addf1_score += f
	} else {
		m.addf1_score = &f
	}
}

// AddedF1Score returns the value that was added to the "f1_score" field in this mutation.
func (m *ModelMutation) AddedF1Score() (r float64, exists bool) {
	v := m.addf1_score
	if v == nil {
		return
	}
	return *v, true
}

// ResetF1Score resets all changes to the "f1_score" field.
func (m *ModelMutation) ResetF1Score() {
	m.f1_score = nil
	m.addf1_score = nil
}

// SetCreatedBy sets the "created_by" field.
func (m *ModelMutation) SetCreatedBy(s string) {
	m.created_by = &s
}

// CreatedBy returns the value of the "created_by" field in the mutation.
func (m *ModelMutation) CreatedBy() (r string, exists bool) {
	v := m.created_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedBy returns the old "created_by" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldCreatedBy(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedBy: %w", err)
	}
	return oldValue.CreatedBy, nil
}

// ResetCreatedBy resets all changes to the "created_by" field.
func (m *ModelMutation) ResetCreatedBy() {
	m.created_by = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *ModelMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *ModelMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *ModelMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *ModelMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *ModelMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the Model entity.
// If the Model object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ModelMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *ModelMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// Where appends a list predicates to the ModelMutation builder.
func (m *ModelMutation) Where(ps ...predicate.Model) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ModelMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ModelMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Model, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ModelMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ModelMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Model).
func (m *ModelMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ModelMutation) Fields() []string {
	fields := make([]string, 0, 12)
	if m.model_id != nil {
		fields = append(fields, model.FieldModelID)
	}
	if m.model_name_version != nil {
		fields = append(fields, model.FieldModelNameVersion)
	}
	if m.framework_version != nil {
		fields = append(fields, model.FieldFrameworkVersion)
	}
	if m.type_desc != nil {
		fields = append(fields, model.FieldTypeDesc)
	}
	if m.storage_path != nil {
		fields = append(fields, model.FieldStoragePath)
	}
	if m.accuracy != nil {
		fields = append(fields, model.FieldAccuracy)
	}
	if m.precision != nil {
		fields = append(fields, model.FieldPrecision)
	}
	if m.recall != nil {
		fields = append(fields, model.FieldRecall)
	}
	if m.f1_score != nil {
		fields = append(fields, model.FieldF1Score)
	}
	if m.created_by != nil {
		fields = append(fields, model.FieldCreatedBy)
	}
	if m.create_unix != nil {
		fields = append(fields, model.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, model.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ModelMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case model.FieldModelID:
		return m.ModelID()
	case model.FieldModelNameVersion:
		return m.ModelNameVersion()
	case model.FieldFrameworkVersion:
		return m.FrameworkVersion()
	case model.FieldTypeDesc:
		return m.TypeDesc()
	case model.FieldStoragePath:
		return m.StoragePath()
	case model.FieldAccuracy:
		return m.Accuracy()
	case model.FieldPrecision:
		return m.Precision()
	case model.FieldRecall:
		return m.Recall()
	case model.FieldF1Score:
		return m.F1Score()
	case model.FieldCreatedBy:
		return m.CreatedBy()
	case model.FieldCreateUnix:
		return m.CreateUnix()
	case model.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ModelMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case model.FieldModelID:
		return m.OldModelID(ctx)
	case model.FieldModelNameVersion:
		return m.OldModelNameVersion(ctx)
	case model.FieldFrameworkVersion:
		return m.OldFrameworkVersion(ctx)
	case model.FieldTypeDesc:
		return m.OldTypeDesc(ctx)
	case model.FieldStoragePath:
		return m.OldStoragePath(ctx)
	case model.FieldAccuracy:
		return m.OldAccuracy(ctx)
	case model.FieldPrecision:
		return m.OldPrecision(ctx)
	case model.FieldRecall:
		return m.OldRecall(ctx)
	case model.FieldF1Score:
		return m.OldF1Score(ctx)
	case model.FieldCreatedBy:
		return m.OldCreatedBy(ctx)
	case model.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case model.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown Model field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ModelMutation) SetField(name string, value ent.Value) error {
	switch name {
	case model.FieldModelID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetModelID(v)
		return nil
	case model.FieldModelNameVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetModelNameVersion(v)
		return nil
	case model.FieldFrameworkVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFrameworkVersion(v)
		return nil
	case model.FieldTypeDesc:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTypeDesc(v)
		return nil
	case model.FieldStoragePath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStoragePath(v)
		return nil
	case model.FieldAccuracy:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccuracy(v)
		return nil
	case model.FieldPrecision:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPrecision(v)
		return nil
	case model.FieldRecall:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRecall(v)
		return nil
	case model.FieldF1Score:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetF1Score(v)
		return nil
	case model.FieldCreatedBy:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedBy(v)
		return nil
	case model.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case model.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown Model field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ModelMutation) AddedFields() []string {
	var fields []string
	if m.addaccuracy != nil {
		fields = append(fields, model.FieldAccuracy)
	}
	if m.addprecision != nil {
		fields = append(fields, model.FieldPrecision)
	}
	if m.addrecall != nil {
		fields = append(fields, model.FieldRecall)
	}
	if m.addf1_score != nil {
		fields = append(fields, model.FieldF1Score)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ModelMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case model.FieldAccuracy:
		return m.AddedAccuracy()
	case model.FieldPrecision:
		return m.AddedPrecision()
	case model.FieldRecall:
		return m.AddedRecall()
	case model.FieldF1Score:
		return m.AddedF1Score()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ModelMutation) AddField(name string, value ent.Value) error {
	switch name {
	case model.FieldAccuracy:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAccuracy(v)
		return nil
	case model.FieldPrecision:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPrecision(v)
		return nil
	case model.FieldRecall:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddRecall(v)
		return nil
	case model.FieldF1Score:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddF1Score(v)
		return nil
	}
	return fmt.Errorf("unknown Model numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ModelMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ModelMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ModelMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Model nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ModelMutation) ResetField(name string) error {
	switch name {
	case model.FieldModelID:
		m.ResetModelID()
		return nil
	case model.FieldModelNameVersion:
		m.ResetModelNameVersion()
		return nil
	case model.FieldFrameworkVersion:
		m.ResetFrameworkVersion()
		return nil
	case model.FieldTypeDesc:
		m.ResetTypeDesc()
		return nil
	case model.FieldStoragePath:
		m.ResetStoragePath()
		return nil
	case model.FieldAccuracy:
		m.ResetAccuracy()
		return nil
	case model.FieldPrecision:
		m.ResetPrecision()
		return nil
	case model.FieldRecall:
		m.ResetRecall()
		return nil
	case model.FieldF1Score:
		m.ResetF1Score()
		return nil
	case model.FieldCreatedBy:
		m.ResetCreatedBy()
		return nil
	case model.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case model.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown Model field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ModelMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ModelMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ModelMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ModelMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ModelMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ModelMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ModelMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Model unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ModelMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Model edge %s", name)
}
