FROM golang:alpine AS builder

LABEL stage=gobuilder

ENV CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn,direct
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

RUN apk update --no-cache && apk add --no-cache tzdata

WORKDIR /build

# 复制整个项目目录
COPY . .
RUN go mod download

# 构建应用
RUN go build -ldflags="-s -w" -o /app/datamanager app/Datamanager/data.go

FROM busybox:latest

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /app/datamanager /app/datamanager
COPY --from=builder /build/app/Datamanager/etc /app/etc

CMD ["./datamanager", "-f", "etc/data.yaml"]
