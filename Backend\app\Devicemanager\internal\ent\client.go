// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"GCF/app/Devicemanager/internal/ent/migrate"

	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/test_create_db"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Data is the client for interacting with the Data builders.
	Data *DataClient
	// Device is the client for interacting with the Device builders.
	Device *DeviceClient
	// Modbus is the client for interacting with the Modbus builders.
	Modbus *ModbusClient
	// Test_create_db is the client for interacting with the Test_create_db builders.
	Test_create_db *TestCreateDbClient
	// Uart is the client for interacting with the Uart builders.
	Uart *UartClient
	// Udp is the client for interacting with the Udp builders.
	Udp *UDPClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Data = NewDataClient(c.config)
	c.Device = NewDeviceClient(c.config)
	c.Modbus = NewModbusClient(c.config)
	c.Test_create_db = NewTestCreateDbClient(c.config)
	c.Uart = NewUartClient(c.config)
	c.Udp = NewUDPClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:            ctx,
		config:         cfg,
		Data:           NewDataClient(cfg),
		Device:         NewDeviceClient(cfg),
		Modbus:         NewModbusClient(cfg),
		Test_create_db: NewTestCreateDbClient(cfg),
		Uart:           NewUartClient(cfg),
		Udp:            NewUDPClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:            ctx,
		config:         cfg,
		Data:           NewDataClient(cfg),
		Device:         NewDeviceClient(cfg),
		Modbus:         NewModbusClient(cfg),
		Test_create_db: NewTestCreateDbClient(cfg),
		Uart:           NewUartClient(cfg),
		Udp:            NewUDPClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Data.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.Data, c.Device, c.Modbus, c.Test_create_db, c.Uart, c.Udp,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.Data, c.Device, c.Modbus, c.Test_create_db, c.Uart, c.Udp,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *DataMutation:
		return c.Data.mutate(ctx, m)
	case *DeviceMutation:
		return c.Device.mutate(ctx, m)
	case *ModbusMutation:
		return c.Modbus.mutate(ctx, m)
	case *TestCreateDbMutation:
		return c.Test_create_db.mutate(ctx, m)
	case *UartMutation:
		return c.Uart.mutate(ctx, m)
	case *UDPMutation:
		return c.Udp.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// DataClient is a client for the Data schema.
type DataClient struct {
	config
}

// NewDataClient returns a client for the Data from the given config.
func NewDataClient(c config) *DataClient {
	return &DataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `data.Hooks(f(g(h())))`.
func (c *DataClient) Use(hooks ...Hook) {
	c.hooks.Data = append(c.hooks.Data, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `data.Intercept(f(g(h())))`.
func (c *DataClient) Intercept(interceptors ...Interceptor) {
	c.inters.Data = append(c.inters.Data, interceptors...)
}

// Create returns a builder for creating a Data entity.
func (c *DataClient) Create() *DataCreate {
	mutation := newDataMutation(c.config, OpCreate)
	return &DataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Data entities.
func (c *DataClient) CreateBulk(builders ...*DataCreate) *DataCreateBulk {
	return &DataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DataClient) MapCreateBulk(slice any, setFunc func(*DataCreate, int)) *DataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DataCreateBulk{err: fmt.Errorf("calling to DataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Data.
func (c *DataClient) Update() *DataUpdate {
	mutation := newDataMutation(c.config, OpUpdate)
	return &DataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DataClient) UpdateOne(d *Data) *DataUpdateOne {
	mutation := newDataMutation(c.config, OpUpdateOne, withData(d))
	return &DataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DataClient) UpdateOneID(id string) *DataUpdateOne {
	mutation := newDataMutation(c.config, OpUpdateOne, withDataID(id))
	return &DataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Data.
func (c *DataClient) Delete() *DataDelete {
	mutation := newDataMutation(c.config, OpDelete)
	return &DataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DataClient) DeleteOne(d *Data) *DataDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DataClient) DeleteOneID(id string) *DataDeleteOne {
	builder := c.Delete().Where(data.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DataDeleteOne{builder}
}

// Query returns a query builder for Data.
func (c *DataClient) Query() *DataQuery {
	return &DataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeData},
		inters: c.Interceptors(),
	}
}

// Get returns a Data entity by its id.
func (c *DataClient) Get(ctx context.Context, id string) (*Data, error) {
	return c.Query().Where(data.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DataClient) GetX(ctx context.Context, id string) *Data {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DataClient) Hooks() []Hook {
	return c.hooks.Data
}

// Interceptors returns the client interceptors.
func (c *DataClient) Interceptors() []Interceptor {
	return c.inters.Data
}

func (c *DataClient) mutate(ctx context.Context, m *DataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Data mutation op: %q", m.Op())
	}
}

// DeviceClient is a client for the Device schema.
type DeviceClient struct {
	config
}

// NewDeviceClient returns a client for the Device from the given config.
func NewDeviceClient(c config) *DeviceClient {
	return &DeviceClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `device.Hooks(f(g(h())))`.
func (c *DeviceClient) Use(hooks ...Hook) {
	c.hooks.Device = append(c.hooks.Device, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `device.Intercept(f(g(h())))`.
func (c *DeviceClient) Intercept(interceptors ...Interceptor) {
	c.inters.Device = append(c.inters.Device, interceptors...)
}

// Create returns a builder for creating a Device entity.
func (c *DeviceClient) Create() *DeviceCreate {
	mutation := newDeviceMutation(c.config, OpCreate)
	return &DeviceCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Device entities.
func (c *DeviceClient) CreateBulk(builders ...*DeviceCreate) *DeviceCreateBulk {
	return &DeviceCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DeviceClient) MapCreateBulk(slice any, setFunc func(*DeviceCreate, int)) *DeviceCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DeviceCreateBulk{err: fmt.Errorf("calling to DeviceClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DeviceCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DeviceCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Device.
func (c *DeviceClient) Update() *DeviceUpdate {
	mutation := newDeviceMutation(c.config, OpUpdate)
	return &DeviceUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DeviceClient) UpdateOne(d *Device) *DeviceUpdateOne {
	mutation := newDeviceMutation(c.config, OpUpdateOne, withDevice(d))
	return &DeviceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DeviceClient) UpdateOneID(id string) *DeviceUpdateOne {
	mutation := newDeviceMutation(c.config, OpUpdateOne, withDeviceID(id))
	return &DeviceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Device.
func (c *DeviceClient) Delete() *DeviceDelete {
	mutation := newDeviceMutation(c.config, OpDelete)
	return &DeviceDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DeviceClient) DeleteOne(d *Device) *DeviceDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DeviceClient) DeleteOneID(id string) *DeviceDeleteOne {
	builder := c.Delete().Where(device.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DeviceDeleteOne{builder}
}

// Query returns a query builder for Device.
func (c *DeviceClient) Query() *DeviceQuery {
	return &DeviceQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDevice},
		inters: c.Interceptors(),
	}
}

// Get returns a Device entity by its id.
func (c *DeviceClient) Get(ctx context.Context, id string) (*Device, error) {
	return c.Query().Where(device.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DeviceClient) GetX(ctx context.Context, id string) *Device {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryModbusConfig queries the modbusConfig edge of a Device.
func (c *DeviceClient) QueryModbusConfig(d *Device) *ModbusQuery {
	query := (&ModbusClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(device.Table, device.FieldID, id),
			sqlgraph.To(modbus.Table, modbus.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, device.ModbusConfigTable, device.ModbusConfigColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUartConfig queries the uartConfig edge of a Device.
func (c *DeviceClient) QueryUartConfig(d *Device) *UartQuery {
	query := (&UartClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(device.Table, device.FieldID, id),
			sqlgraph.To(uart.Table, uart.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, device.UartConfigTable, device.UartConfigColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUdpConfig queries the udpConfig edge of a Device.
func (c *DeviceClient) QueryUdpConfig(d *Device) *UDPQuery {
	query := (&UDPClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := d.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(device.Table, device.FieldID, id),
			sqlgraph.To(udp.Table, udp.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, device.UdpConfigTable, device.UdpConfigColumn),
		)
		fromV = sqlgraph.Neighbors(d.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DeviceClient) Hooks() []Hook {
	return c.hooks.Device
}

// Interceptors returns the client interceptors.
func (c *DeviceClient) Interceptors() []Interceptor {
	return c.inters.Device
}

func (c *DeviceClient) mutate(ctx context.Context, m *DeviceMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DeviceCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DeviceUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DeviceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DeviceDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Device mutation op: %q", m.Op())
	}
}

// ModbusClient is a client for the Modbus schema.
type ModbusClient struct {
	config
}

// NewModbusClient returns a client for the Modbus from the given config.
func NewModbusClient(c config) *ModbusClient {
	return &ModbusClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `modbus.Hooks(f(g(h())))`.
func (c *ModbusClient) Use(hooks ...Hook) {
	c.hooks.Modbus = append(c.hooks.Modbus, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `modbus.Intercept(f(g(h())))`.
func (c *ModbusClient) Intercept(interceptors ...Interceptor) {
	c.inters.Modbus = append(c.inters.Modbus, interceptors...)
}

// Create returns a builder for creating a Modbus entity.
func (c *ModbusClient) Create() *ModbusCreate {
	mutation := newModbusMutation(c.config, OpCreate)
	return &ModbusCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Modbus entities.
func (c *ModbusClient) CreateBulk(builders ...*ModbusCreate) *ModbusCreateBulk {
	return &ModbusCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ModbusClient) MapCreateBulk(slice any, setFunc func(*ModbusCreate, int)) *ModbusCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ModbusCreateBulk{err: fmt.Errorf("calling to ModbusClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ModbusCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ModbusCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Modbus.
func (c *ModbusClient) Update() *ModbusUpdate {
	mutation := newModbusMutation(c.config, OpUpdate)
	return &ModbusUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ModbusClient) UpdateOne(m *Modbus) *ModbusUpdateOne {
	mutation := newModbusMutation(c.config, OpUpdateOne, withModbus(m))
	return &ModbusUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ModbusClient) UpdateOneID(id string) *ModbusUpdateOne {
	mutation := newModbusMutation(c.config, OpUpdateOne, withModbusID(id))
	return &ModbusUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Modbus.
func (c *ModbusClient) Delete() *ModbusDelete {
	mutation := newModbusMutation(c.config, OpDelete)
	return &ModbusDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ModbusClient) DeleteOne(m *Modbus) *ModbusDeleteOne {
	return c.DeleteOneID(m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ModbusClient) DeleteOneID(id string) *ModbusDeleteOne {
	builder := c.Delete().Where(modbus.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ModbusDeleteOne{builder}
}

// Query returns a query builder for Modbus.
func (c *ModbusClient) Query() *ModbusQuery {
	return &ModbusQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeModbus},
		inters: c.Interceptors(),
	}
}

// Get returns a Modbus entity by its id.
func (c *ModbusClient) Get(ctx context.Context, id string) (*Modbus, error) {
	return c.Query().Where(modbus.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ModbusClient) GetX(ctx context.Context, id string) *Modbus {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDevice queries the device edge of a Modbus.
func (c *ModbusClient) QueryDevice(m *Modbus) *DeviceQuery {
	query := (&DeviceClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(modbus.Table, modbus.FieldID, id),
			sqlgraph.To(device.Table, device.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, modbus.DeviceTable, modbus.DeviceColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ModbusClient) Hooks() []Hook {
	return c.hooks.Modbus
}

// Interceptors returns the client interceptors.
func (c *ModbusClient) Interceptors() []Interceptor {
	return c.inters.Modbus
}

func (c *ModbusClient) mutate(ctx context.Context, m *ModbusMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ModbusCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ModbusUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ModbusUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ModbusDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Modbus mutation op: %q", m.Op())
	}
}

// TestCreateDbClient is a client for the Test_create_db schema.
type TestCreateDbClient struct {
	config
}

// NewTestCreateDbClient returns a client for the Test_create_db from the given config.
func NewTestCreateDbClient(c config) *TestCreateDbClient {
	return &TestCreateDbClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `test_create_db.Hooks(f(g(h())))`.
func (c *TestCreateDbClient) Use(hooks ...Hook) {
	c.hooks.Test_create_db = append(c.hooks.Test_create_db, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `test_create_db.Intercept(f(g(h())))`.
func (c *TestCreateDbClient) Intercept(interceptors ...Interceptor) {
	c.inters.Test_create_db = append(c.inters.Test_create_db, interceptors...)
}

// Create returns a builder for creating a Test_create_db entity.
func (c *TestCreateDbClient) Create() *TestCreateDbCreate {
	mutation := newTestCreateDbMutation(c.config, OpCreate)
	return &TestCreateDbCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Test_create_db entities.
func (c *TestCreateDbClient) CreateBulk(builders ...*TestCreateDbCreate) *TestCreateDbCreateBulk {
	return &TestCreateDbCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TestCreateDbClient) MapCreateBulk(slice any, setFunc func(*TestCreateDbCreate, int)) *TestCreateDbCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TestCreateDbCreateBulk{err: fmt.Errorf("calling to TestCreateDbClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TestCreateDbCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TestCreateDbCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Test_create_db.
func (c *TestCreateDbClient) Update() *TestCreateDbUpdate {
	mutation := newTestCreateDbMutation(c.config, OpUpdate)
	return &TestCreateDbUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TestCreateDbClient) UpdateOne(tcd *Test_create_db) *TestCreateDbUpdateOne {
	mutation := newTestCreateDbMutation(c.config, OpUpdateOne, withTest_create_db(tcd))
	return &TestCreateDbUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TestCreateDbClient) UpdateOneID(id int) *TestCreateDbUpdateOne {
	mutation := newTestCreateDbMutation(c.config, OpUpdateOne, withTest_create_dbID(id))
	return &TestCreateDbUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Test_create_db.
func (c *TestCreateDbClient) Delete() *TestCreateDbDelete {
	mutation := newTestCreateDbMutation(c.config, OpDelete)
	return &TestCreateDbDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TestCreateDbClient) DeleteOne(tcd *Test_create_db) *TestCreateDbDeleteOne {
	return c.DeleteOneID(tcd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TestCreateDbClient) DeleteOneID(id int) *TestCreateDbDeleteOne {
	builder := c.Delete().Where(test_create_db.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TestCreateDbDeleteOne{builder}
}

// Query returns a query builder for Test_create_db.
func (c *TestCreateDbClient) Query() *TestCreateDbQuery {
	return &TestCreateDbQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTestCreateDb},
		inters: c.Interceptors(),
	}
}

// Get returns a Test_create_db entity by its id.
func (c *TestCreateDbClient) Get(ctx context.Context, id int) (*Test_create_db, error) {
	return c.Query().Where(test_create_db.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TestCreateDbClient) GetX(ctx context.Context, id int) *Test_create_db {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TestCreateDbClient) Hooks() []Hook {
	return c.hooks.Test_create_db
}

// Interceptors returns the client interceptors.
func (c *TestCreateDbClient) Interceptors() []Interceptor {
	return c.inters.Test_create_db
}

func (c *TestCreateDbClient) mutate(ctx context.Context, m *TestCreateDbMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TestCreateDbCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TestCreateDbUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TestCreateDbUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TestCreateDbDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Test_create_db mutation op: %q", m.Op())
	}
}

// UartClient is a client for the Uart schema.
type UartClient struct {
	config
}

// NewUartClient returns a client for the Uart from the given config.
func NewUartClient(c config) *UartClient {
	return &UartClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `uart.Hooks(f(g(h())))`.
func (c *UartClient) Use(hooks ...Hook) {
	c.hooks.Uart = append(c.hooks.Uart, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `uart.Intercept(f(g(h())))`.
func (c *UartClient) Intercept(interceptors ...Interceptor) {
	c.inters.Uart = append(c.inters.Uart, interceptors...)
}

// Create returns a builder for creating a Uart entity.
func (c *UartClient) Create() *UartCreate {
	mutation := newUartMutation(c.config, OpCreate)
	return &UartCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Uart entities.
func (c *UartClient) CreateBulk(builders ...*UartCreate) *UartCreateBulk {
	return &UartCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UartClient) MapCreateBulk(slice any, setFunc func(*UartCreate, int)) *UartCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UartCreateBulk{err: fmt.Errorf("calling to UartClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UartCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UartCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Uart.
func (c *UartClient) Update() *UartUpdate {
	mutation := newUartMutation(c.config, OpUpdate)
	return &UartUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UartClient) UpdateOne(u *Uart) *UartUpdateOne {
	mutation := newUartMutation(c.config, OpUpdateOne, withUart(u))
	return &UartUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UartClient) UpdateOneID(id string) *UartUpdateOne {
	mutation := newUartMutation(c.config, OpUpdateOne, withUartID(id))
	return &UartUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Uart.
func (c *UartClient) Delete() *UartDelete {
	mutation := newUartMutation(c.config, OpDelete)
	return &UartDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UartClient) DeleteOne(u *Uart) *UartDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UartClient) DeleteOneID(id string) *UartDeleteOne {
	builder := c.Delete().Where(uart.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UartDeleteOne{builder}
}

// Query returns a query builder for Uart.
func (c *UartClient) Query() *UartQuery {
	return &UartQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUart},
		inters: c.Interceptors(),
	}
}

// Get returns a Uart entity by its id.
func (c *UartClient) Get(ctx context.Context, id string) (*Uart, error) {
	return c.Query().Where(uart.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UartClient) GetX(ctx context.Context, id string) *Uart {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDevice queries the device edge of a Uart.
func (c *UartClient) QueryDevice(u *Uart) *DeviceQuery {
	query := (&DeviceClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(uart.Table, uart.FieldID, id),
			sqlgraph.To(device.Table, device.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, uart.DeviceTable, uart.DeviceColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UartClient) Hooks() []Hook {
	return c.hooks.Uart
}

// Interceptors returns the client interceptors.
func (c *UartClient) Interceptors() []Interceptor {
	return c.inters.Uart
}

func (c *UartClient) mutate(ctx context.Context, m *UartMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UartCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UartUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UartUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UartDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Uart mutation op: %q", m.Op())
	}
}

// UDPClient is a client for the Udp schema.
type UDPClient struct {
	config
}

// NewUDPClient returns a client for the Udp from the given config.
func NewUDPClient(c config) *UDPClient {
	return &UDPClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `udp.Hooks(f(g(h())))`.
func (c *UDPClient) Use(hooks ...Hook) {
	c.hooks.Udp = append(c.hooks.Udp, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `udp.Intercept(f(g(h())))`.
func (c *UDPClient) Intercept(interceptors ...Interceptor) {
	c.inters.Udp = append(c.inters.Udp, interceptors...)
}

// Create returns a builder for creating a Udp entity.
func (c *UDPClient) Create() *UDPCreate {
	mutation := newUDPMutation(c.config, OpCreate)
	return &UDPCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Udp entities.
func (c *UDPClient) CreateBulk(builders ...*UDPCreate) *UDPCreateBulk {
	return &UDPCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UDPClient) MapCreateBulk(slice any, setFunc func(*UDPCreate, int)) *UDPCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UDPCreateBulk{err: fmt.Errorf("calling to UDPClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UDPCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UDPCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Udp.
func (c *UDPClient) Update() *UDPUpdate {
	mutation := newUDPMutation(c.config, OpUpdate)
	return &UDPUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UDPClient) UpdateOne(u *Udp) *UDPUpdateOne {
	mutation := newUDPMutation(c.config, OpUpdateOne, withUdp(u))
	return &UDPUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UDPClient) UpdateOneID(id string) *UDPUpdateOne {
	mutation := newUDPMutation(c.config, OpUpdateOne, withUdpID(id))
	return &UDPUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Udp.
func (c *UDPClient) Delete() *UDPDelete {
	mutation := newUDPMutation(c.config, OpDelete)
	return &UDPDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UDPClient) DeleteOne(u *Udp) *UDPDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UDPClient) DeleteOneID(id string) *UDPDeleteOne {
	builder := c.Delete().Where(udp.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UDPDeleteOne{builder}
}

// Query returns a query builder for Udp.
func (c *UDPClient) Query() *UDPQuery {
	return &UDPQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUDP},
		inters: c.Interceptors(),
	}
}

// Get returns a Udp entity by its id.
func (c *UDPClient) Get(ctx context.Context, id string) (*Udp, error) {
	return c.Query().Where(udp.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UDPClient) GetX(ctx context.Context, id string) *Udp {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDevice queries the device edge of a Udp.
func (c *UDPClient) QueryDevice(u *Udp) *DeviceQuery {
	query := (&DeviceClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(udp.Table, udp.FieldID, id),
			sqlgraph.To(device.Table, device.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, udp.DeviceTable, udp.DeviceColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UDPClient) Hooks() []Hook {
	return c.hooks.Udp
}

// Interceptors returns the client interceptors.
func (c *UDPClient) Interceptors() []Interceptor {
	return c.inters.Udp
}

func (c *UDPClient) mutate(ctx context.Context, m *UDPMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UDPCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UDPUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UDPUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UDPDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Udp mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Data, Device, Modbus, Test_create_db, Uart, Udp []ent.Hook
	}
	inters struct {
		Data, Device, Modbus, Test_create_db, Uart, Udp []ent.Interceptor
	}
)
