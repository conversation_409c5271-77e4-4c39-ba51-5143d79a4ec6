apiVersion: apps/v1
kind: Deployment
metadata:
  name: modelmanager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: modelmanager
  template:
    metadata:
      labels:
        app: modelmanager
    spec:
      containers:
      - name: modelmanager
        image: 192.168.3.64:5888/generative-control-foundation-dev/modelmanager:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 6666
        volumeMounts:
        - name: config-volume
          mountPath: /app/etc
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: modelmanager-config
---
apiVersion: v1
kind: Service
metadata:
  name: modelmanager
spec:
  selector:
    app: modelmanager
  ports:
  - port: 6666
    targetPort: 6666
    nodePort: 31666
  type: NodePort