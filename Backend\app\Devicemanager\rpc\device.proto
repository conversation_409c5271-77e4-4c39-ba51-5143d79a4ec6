syntax = "proto3";

package devicemanager;
option go_package="./devicemanager";

message GetFramesByDeviceIDRequest {
  string device_id = 1;  
}

message GetFramesByDeviceIDResponse {
  repeated FrameInfo frames = 1;
}

message FrameInfo {
  FrameMeta frame_meta = 1;
  FrameLibs frame_lib = 2;
}

message FrameMeta{
  string frame_uid = 1;
  string frame_type = 2;
  string frame_name = 3;
}

message FrameLibs{
  ModbusInfo modbus_info = 1;
  UartInfo uart_info=2;
  UdpInfo udp_info=3;
}

message ModbusInfo {
  string tid = 1; 
  string pid = 2;
  string len = 3;
  string uid = 4;
  string fc = 5;
  repeated DataDefs datas = 6; 
}

message UartInfo {
  string header = 1; 
  string addr = 2;
  string cmd = 3;
  string tail = 4;
  repeated DataDefs datas = 5; 
}

message UdpInfo {
  string type = 1;
  string header = 2;
  string type_id = 3; 
  repeated DataDefs datas = 4;
}

message DataDefs {
  string index = 1;
  string name = 2;
  string type = 3;
  string unit = 4;
  string value = 5;
  string desc = 6;
}

message GetDeviceByIDRequest {
  string device_id = 1;  
}

message GetDeviceByIDResponse {
  DeviceResourceInfo device_resource_info = 1;
  DeviceWorkStatus device_work_status = 2;
}

message DeviceResourceInfo {
  string ip = 1;
  string name = 2;
  string type = 3;
  string os = 4;
  ResourceDef cpu = 5;
  ResourceDef gpu = 6;
  ResourceDef disk = 7;
  ResourceDef mem = 8;
  repeated string protocol = 9;
}
message ResourceDef {
  int64 amount = 1;
  string type = 2;
  string unit = 3;
}

message DeviceWorkStatus {
  string health_status = 1;
  int64 timestamp = 2;
  string work_mode = 3;
}

message CheckDeviceHealthRequest {
  string device_id = 1;  
}

message CheckDeviceHealthResponse {
  DeviceWorkStatus device_work_status = 1;
}

message ControlDeviceByDeviceIDRequest {
  string device_id = 1;
  FrameInfo frame_info = 2;
}

message ControlDeviceByDeviceIDResponse {
  DeviceWorkStatus device_work_status = 1;
  FrameInfo frame_infos = 2;
}

message PushDataConfigToEtcdRequest {
  string device_id = 1;
  repeated FrameInfo frame_infos = 2;
}

message PushDataConfigToEtcdResponse {
  string message = 1;
}

service Device {
  rpc GetFramesByDeviceID(GetFramesByDeviceIDRequest) returns (GetFramesByDeviceIDResponse);
  rpc GetDeviceByID(GetDeviceByIDRequest) returns (GetDeviceByIDResponse); 
  rpc CheckDeviceHealth(CheckDeviceHealthRequest) returns (CheckDeviceHealthResponse);
  rpc ControlDeviceByDeviceID(ControlDeviceByDeviceIDRequest) returns (ControlDeviceByDeviceIDResponse);
  rpc PushDataConfigToEtcd(PushDataConfigToEtcdRequest) returns (PushDataConfigToEtcdResponse);
}
