// Code generated by ent, DO NOT EDIT.

package device

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the device type in the database.
	Label = "device"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldIP holds the string denoting the ip field in the database.
	FieldIP = "ip"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldOs holds the string denoting the os field in the database.
	FieldOs = "os"
	// FieldCPU holds the string denoting the cpu field in the database.
	FieldCPU = "cpu"
	// FieldGpu holds the string denoting the gpu field in the database.
	FieldGpu = "gpu"
	// FieldMemory holds the string denoting the memory field in the database.
	FieldMemory = "memory"
	// FieldDisk holds the string denoting the disk field in the database.
	FieldDisk = "disk"
	// FieldProtocol holds the string denoting the protocol field in the database.
	FieldProtocol = "protocol"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldHealthtimestamp holds the string denoting the healthtimestamp field in the database.
	FieldHealthtimestamp = "healthtimestamp"
	// FieldWorkmode holds the string denoting the workmode field in the database.
	FieldWorkmode = "workmode"
	// FieldCreateUnix holds the string denoting the create_unix field in the database.
	FieldCreateUnix = "create_unix"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// EdgeModbusConfig holds the string denoting the modbusconfig edge name in mutations.
	EdgeModbusConfig = "modbusConfig"
	// EdgeUartConfig holds the string denoting the uartconfig edge name in mutations.
	EdgeUartConfig = "uartConfig"
	// EdgeUdpConfig holds the string denoting the udpconfig edge name in mutations.
	EdgeUdpConfig = "udpConfig"
	// Table holds the table name of the device in the database.
	Table = "devices"
	// ModbusConfigTable is the table that holds the modbusConfig relation/edge.
	ModbusConfigTable = "modbuses"
	// ModbusConfigInverseTable is the table name for the Modbus entity.
	// It exists in this package in order to avoid circular dependency with the "modbus" package.
	ModbusConfigInverseTable = "modbuses"
	// ModbusConfigColumn is the table column denoting the modbusConfig relation/edge.
	ModbusConfigColumn = "device_id"
	// UartConfigTable is the table that holds the uartConfig relation/edge.
	UartConfigTable = "uarts"
	// UartConfigInverseTable is the table name for the Uart entity.
	// It exists in this package in order to avoid circular dependency with the "uart" package.
	UartConfigInverseTable = "uarts"
	// UartConfigColumn is the table column denoting the uartConfig relation/edge.
	UartConfigColumn = "device_id"
	// UdpConfigTable is the table that holds the udpConfig relation/edge.
	UdpConfigTable = "udps"
	// UdpConfigInverseTable is the table name for the Udp entity.
	// It exists in this package in order to avoid circular dependency with the "udp" package.
	UdpConfigInverseTable = "udps"
	// UdpConfigColumn is the table column denoting the udpConfig relation/edge.
	UdpConfigColumn = "device_id"
)

// Columns holds all SQL columns for device fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldIP,
	FieldType,
	FieldOs,
	FieldCPU,
	FieldGpu,
	FieldMemory,
	FieldDisk,
	FieldProtocol,
	FieldDescription,
	FieldStatus,
	FieldHealthtimestamp,
	FieldWorkmode,
	FieldCreateUnix,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
	// IPValidator is a validator for the "ip" field. It is called by the builders before save.
	IPValidator func(string) error
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// DefaultOs holds the default value on creation for the "os" field.
	DefaultOs string
	// DefaultCPU holds the default value on creation for the "cpu" field.
	DefaultCPU string
	// DefaultGpu holds the default value on creation for the "gpu" field.
	DefaultGpu string
	// DefaultMemory holds the default value on creation for the "memory" field.
	DefaultMemory string
	// DefaultDisk holds the default value on creation for the "disk" field.
	DefaultDisk string
	// DefaultProtocol holds the default value on creation for the "protocol" field.
	DefaultProtocol string
	// DefaultDescription holds the default value on creation for the "description" field.
	DefaultDescription string
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// DefaultHealthtimestamp holds the default value on creation for the "healthtimestamp" field.
	DefaultHealthtimestamp time.Time
	// DefaultWorkmode holds the default value on creation for the "workmode" field.
	DefaultWorkmode string
	// DefaultCreateUnix holds the default value on creation for the "create_unix" field.
	DefaultCreateUnix func() time.Time
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Device queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByIP orders the results by the ip field.
func ByIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIP, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByOs orders the results by the os field.
func ByOs(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOs, opts...).ToFunc()
}

// ByCPU orders the results by the cpu field.
func ByCPU(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCPU, opts...).ToFunc()
}

// ByGpu orders the results by the gpu field.
func ByGpu(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGpu, opts...).ToFunc()
}

// ByMemory orders the results by the memory field.
func ByMemory(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMemory, opts...).ToFunc()
}

// ByDisk orders the results by the disk field.
func ByDisk(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDisk, opts...).ToFunc()
}

// ByProtocol orders the results by the protocol field.
func ByProtocol(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProtocol, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByHealthtimestamp orders the results by the healthtimestamp field.
func ByHealthtimestamp(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHealthtimestamp, opts...).ToFunc()
}

// ByWorkmode orders the results by the workmode field.
func ByWorkmode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWorkmode, opts...).ToFunc()
}

// ByCreateUnix orders the results by the create_unix field.
func ByCreateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUnix, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}

// ByModbusConfigCount orders the results by modbusConfig count.
func ByModbusConfigCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newModbusConfigStep(), opts...)
	}
}

// ByModbusConfig orders the results by modbusConfig terms.
func ByModbusConfig(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newModbusConfigStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByUartConfigCount orders the results by uartConfig count.
func ByUartConfigCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newUartConfigStep(), opts...)
	}
}

// ByUartConfig orders the results by uartConfig terms.
func ByUartConfig(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUartConfigStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByUdpConfigCount orders the results by udpConfig count.
func ByUdpConfigCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newUdpConfigStep(), opts...)
	}
}

// ByUdpConfig orders the results by udpConfig terms.
func ByUdpConfig(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUdpConfigStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newModbusConfigStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ModbusConfigInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ModbusConfigTable, ModbusConfigColumn),
	)
}
func newUartConfigStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UartConfigInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, UartConfigTable, UartConfigColumn),
	)
}
func newUdpConfigStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UdpConfigInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, UdpConfigTable, UdpConfigColumn),
	)
}
