// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/uart"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UartCreate is the builder for creating a Uart entity.
type UartCreate struct {
	config
	mutation *UartMutation
	hooks    []Hook
}

// SetDeviceID sets the "device_id" field.
func (uc *UartCreate) SetDeviceID(s string) *UartCreate {
	uc.mutation.SetDeviceID(s)
	return uc
}

// SetHeader sets the "header" field.
func (uc *UartCreate) SetHeader(s string) *UartCreate {
	uc.mutation.SetHeader(s)
	return uc
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (uc *UartCreate) SetNillableHeader(s *string) *UartCreate {
	if s != nil {
		uc.SetHeader(*s)
	}
	return uc
}

// SetAddr sets the "addr" field.
func (uc *UartCreate) SetAddr(s string) *UartCreate {
	uc.mutation.SetAddr(s)
	return uc
}

// SetNillableAddr sets the "addr" field if the given value is not nil.
func (uc *UartCreate) SetNillableAddr(s *string) *UartCreate {
	if s != nil {
		uc.SetAddr(*s)
	}
	return uc
}

// SetCmd sets the "cmd" field.
func (uc *UartCreate) SetCmd(s string) *UartCreate {
	uc.mutation.SetCmd(s)
	return uc
}

// SetNillableCmd sets the "cmd" field if the given value is not nil.
func (uc *UartCreate) SetNillableCmd(s *string) *UartCreate {
	if s != nil {
		uc.SetCmd(*s)
	}
	return uc
}

// SetTail sets the "tail" field.
func (uc *UartCreate) SetTail(s string) *UartCreate {
	uc.mutation.SetTail(s)
	return uc
}

// SetNillableTail sets the "tail" field if the given value is not nil.
func (uc *UartCreate) SetNillableTail(s *string) *UartCreate {
	if s != nil {
		uc.SetTail(*s)
	}
	return uc
}

// SetDescription sets the "description" field.
func (uc *UartCreate) SetDescription(s string) *UartCreate {
	uc.mutation.SetDescription(s)
	return uc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (uc *UartCreate) SetNillableDescription(s *string) *UartCreate {
	if s != nil {
		uc.SetDescription(*s)
	}
	return uc
}

// SetName sets the "name" field.
func (uc *UartCreate) SetName(s string) *UartCreate {
	uc.mutation.SetName(s)
	return uc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uc *UartCreate) SetNillableName(s *string) *UartCreate {
	if s != nil {
		uc.SetName(*s)
	}
	return uc
}

// SetCreateUnix sets the "create_unix" field.
func (uc *UartCreate) SetCreateUnix(t time.Time) *UartCreate {
	uc.mutation.SetCreateUnix(t)
	return uc
}

// SetNillableCreateUnix sets the "create_unix" field if the given value is not nil.
func (uc *UartCreate) SetNillableCreateUnix(t *time.Time) *UartCreate {
	if t != nil {
		uc.SetCreateUnix(*t)
	}
	return uc
}

// SetUpdateUnix sets the "update_unix" field.
func (uc *UartCreate) SetUpdateUnix(t time.Time) *UartCreate {
	uc.mutation.SetUpdateUnix(t)
	return uc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (uc *UartCreate) SetNillableUpdateUnix(t *time.Time) *UartCreate {
	if t != nil {
		uc.SetUpdateUnix(*t)
	}
	return uc
}

// SetID sets the "id" field.
func (uc *UartCreate) SetID(s string) *UartCreate {
	uc.mutation.SetID(s)
	return uc
}

// SetDevice sets the "device" edge to the Device entity.
func (uc *UartCreate) SetDevice(d *Device) *UartCreate {
	return uc.SetDeviceID(d.ID)
}

// Mutation returns the UartMutation object of the builder.
func (uc *UartCreate) Mutation() *UartMutation {
	return uc.mutation
}

// Save creates the Uart in the database.
func (uc *UartCreate) Save(ctx context.Context) (*Uart, error) {
	uc.defaults()
	return withHooks(ctx, uc.sqlSave, uc.mutation, uc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uc *UartCreate) SaveX(ctx context.Context) *Uart {
	v, err := uc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uc *UartCreate) Exec(ctx context.Context) error {
	_, err := uc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uc *UartCreate) ExecX(ctx context.Context) {
	if err := uc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uc *UartCreate) defaults() {
	if _, ok := uc.mutation.Header(); !ok {
		v := uart.DefaultHeader
		uc.mutation.SetHeader(v)
	}
	if _, ok := uc.mutation.Addr(); !ok {
		v := uart.DefaultAddr
		uc.mutation.SetAddr(v)
	}
	if _, ok := uc.mutation.Cmd(); !ok {
		v := uart.DefaultCmd
		uc.mutation.SetCmd(v)
	}
	if _, ok := uc.mutation.Tail(); !ok {
		v := uart.DefaultTail
		uc.mutation.SetTail(v)
	}
	if _, ok := uc.mutation.Description(); !ok {
		v := uart.DefaultDescription
		uc.mutation.SetDescription(v)
	}
	if _, ok := uc.mutation.Name(); !ok {
		v := uart.DefaultName
		uc.mutation.SetName(v)
	}
	if _, ok := uc.mutation.CreateUnix(); !ok {
		v := uart.DefaultCreateUnix()
		uc.mutation.SetCreateUnix(v)
	}
	if _, ok := uc.mutation.UpdateUnix(); !ok {
		v := uart.DefaultUpdateUnix()
		uc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uc *UartCreate) check() error {
	if _, ok := uc.mutation.DeviceID(); !ok {
		return &ValidationError{Name: "device_id", err: errors.New(`ent: missing required field "Uart.device_id"`)}
	}
	if v, ok := uc.mutation.DeviceID(); ok {
		if err := uart.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Uart.device_id": %w`, err)}
		}
	}
	if _, ok := uc.mutation.Header(); !ok {
		return &ValidationError{Name: "header", err: errors.New(`ent: missing required field "Uart.header"`)}
	}
	if _, ok := uc.mutation.Addr(); !ok {
		return &ValidationError{Name: "addr", err: errors.New(`ent: missing required field "Uart.addr"`)}
	}
	if _, ok := uc.mutation.Cmd(); !ok {
		return &ValidationError{Name: "cmd", err: errors.New(`ent: missing required field "Uart.cmd"`)}
	}
	if _, ok := uc.mutation.Tail(); !ok {
		return &ValidationError{Name: "tail", err: errors.New(`ent: missing required field "Uart.tail"`)}
	}
	if _, ok := uc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "Uart.description"`)}
	}
	if _, ok := uc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Uart.name"`)}
	}
	if _, ok := uc.mutation.CreateUnix(); !ok {
		return &ValidationError{Name: "create_unix", err: errors.New(`ent: missing required field "Uart.create_unix"`)}
	}
	if _, ok := uc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "Uart.update_unix"`)}
	}
	if v, ok := uc.mutation.ID(); ok {
		if err := uart.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Uart.id": %w`, err)}
		}
	}
	if len(uc.mutation.DeviceIDs()) == 0 {
		return &ValidationError{Name: "device", err: errors.New(`ent: missing required edge "Uart.device"`)}
	}
	return nil
}

func (uc *UartCreate) sqlSave(ctx context.Context) (*Uart, error) {
	if err := uc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Uart.ID type: %T", _spec.ID.Value)
		}
	}
	uc.mutation.id = &_node.ID
	uc.mutation.done = true
	return _node, nil
}

func (uc *UartCreate) createSpec() (*Uart, *sqlgraph.CreateSpec) {
	var (
		_node = &Uart{config: uc.config}
		_spec = sqlgraph.NewCreateSpec(uart.Table, sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString))
	)
	if id, ok := uc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := uc.mutation.Header(); ok {
		_spec.SetField(uart.FieldHeader, field.TypeString, value)
		_node.Header = value
	}
	if value, ok := uc.mutation.Addr(); ok {
		_spec.SetField(uart.FieldAddr, field.TypeString, value)
		_node.Addr = value
	}
	if value, ok := uc.mutation.Cmd(); ok {
		_spec.SetField(uart.FieldCmd, field.TypeString, value)
		_node.Cmd = value
	}
	if value, ok := uc.mutation.Tail(); ok {
		_spec.SetField(uart.FieldTail, field.TypeString, value)
		_node.Tail = value
	}
	if value, ok := uc.mutation.Description(); ok {
		_spec.SetField(uart.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := uc.mutation.Name(); ok {
		_spec.SetField(uart.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := uc.mutation.CreateUnix(); ok {
		_spec.SetField(uart.FieldCreateUnix, field.TypeTime, value)
		_node.CreateUnix = value
	}
	if value, ok := uc.mutation.UpdateUnix(); ok {
		_spec.SetField(uart.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	if nodes := uc.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   uart.DeviceTable,
			Columns: []string{uart.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.DeviceID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// UartCreateBulk is the builder for creating many Uart entities in bulk.
type UartCreateBulk struct {
	config
	err      error
	builders []*UartCreate
}

// Save creates the Uart entities in the database.
func (ucb *UartCreateBulk) Save(ctx context.Context) ([]*Uart, error) {
	if ucb.err != nil {
		return nil, ucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ucb.builders))
	nodes := make([]*Uart, len(ucb.builders))
	mutators := make([]Mutator, len(ucb.builders))
	for i := range ucb.builders {
		func(i int, root context.Context) {
			builder := ucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UartMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ucb *UartCreateBulk) SaveX(ctx context.Context) []*Uart {
	v, err := ucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucb *UartCreateBulk) Exec(ctx context.Context) error {
	_, err := ucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucb *UartCreateBulk) ExecX(ctx context.Context) {
	if err := ucb.Exec(ctx); err != nil {
		panic(err)
	}
}
