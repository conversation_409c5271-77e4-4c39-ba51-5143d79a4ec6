package utils

import (
	"GCF/app/Devicemanager/internal/ent"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"
	"context"
	"encoding/json"
	"fmt"
)

// 声明变量
type DataInfo struct {
	Name  string `json:"name"`
	Unit  string `json:"unit"`
	Type  string `json:"type"`
	Desc  string `json:"desc"`
	Owner string `json:"owner"`
}

type DataGroup struct {
	DataInfos []DataInfo `json:"data_node"`
	Frequency string     `json:"frequency"`
	Desc      string     `json:"desc"`
	Name      string     `json:"name"`
}

type Producer struct {
	UID        string      `json:"Uid"`
	DataGroups []DataGroup `json:"data_groups"`
}

type Instance struct {
	Name string `json:"name"`
	UID  string `json:"UID"`
	IP   string `json:"ip"`
}

type Drivers struct {
	Instances []*Instance `json:"instances"`
	Producer  Producer    `json:"producer"`
}

func DataDefs2DataInfos(dataDefs []types.DataDef, Device string) []DataInfo {
	dataInfos := make([]DataInfo, 0, len(dataDefs))
	for _, def := range dataDefs {
		dataInfo := DataInfo{
			Name:  def.Name,
			Unit:  def.Unit,
			Type:  def.Type,
			Desc:  def.Desc,
			Owner: Device,
		}
		dataInfos = append(dataInfos, dataInfo)
	}
	return dataInfos
}

// 生成DataGroups
func GetDataGroups(ctx context.Context, svcCtx *svc.ServiceContext, Protocols []string, Device *ent.Device) ([]DataGroup, error) {
	//获取帧info数据
	FrameInfosMap, err := GetFrameInfos(ctx, svcCtx, Protocols, Device.ID)
	if err != nil {
		return nil, err
	}
	DataGroups := make([]DataGroup, 0, len(FrameInfosMap))
	for _, frameInfo := range FrameInfosMap {
		//TODO:Frequency还没有支持
		DataGroup := &DataGroup{
			Name:      fmt.Sprintf("%s_%s", frameInfo.FrameMeta.FrameType, frameInfo.FrameMeta.FrameUID),
			Frequency: "-1",
			Desc:      frameInfo.FrameMeta.FrameDescription,
		}
		switch frameInfo.FrameMeta.FrameType {
		case FrameTypeModbus:
			DataGroup.DataInfos = DataDefs2DataInfos(frameInfo.FrameLibs.ModbusInfo.Datas, Device.Type)
		case FrameTypeUart:
			DataGroup.DataInfos = DataDefs2DataInfos(frameInfo.FrameLibs.UartInfo.Datas, Device.Type)
		case FrameTypeUdp:
			DataGroup.DataInfos = DataDefs2DataInfos(frameInfo.FrameLibs.UdpInfo.Datas, Device.Type)
		}
		DataGroups = append(DataGroups, *DataGroup)
	}
	return DataGroups, nil
}

func GetDriverMap(ctx context.Context, svcCtx *svc.ServiceContext, queryDevices []*ent.Device) (map[*ent.Device]Drivers, error) {
	driverMap := make(map[*ent.Device]Drivers, len(queryDevices))
	//TODO:如果有多个设备
	//instances := make([]*Instance, 0, len(queryDevices))
	for _, device := range queryDevices {
		DeviceResourceInfo, err := GetDeviceResourceInfo(ctx, svcCtx, device)
		if err != nil {
			return nil, err
		}

		Protocols := DeviceResourceInfo.Protocol
		DataGroups, err := GetDataGroups(ctx, svcCtx, Protocols, device)
		if err != nil {
			return nil, err
		}
		instance := Instance{
			Name: device.Name, // 设备名称
			UID:  device.ID,   // 设备ID
			IP:   DeviceResourceInfo.IP,
		}
		producer := Producer{
			UID:        fmt.Sprintf("%s_%s", device.Type, device.ID), // 生成唯一的UID
			DataGroups: DataGroups,
		}
		driverMap[device] = Drivers{
			Producer:  producer,
			Instances: []*Instance{&instance},
		}
	}
	return driverMap, nil
}

func GetWatchListAndDrivers(ctx context.Context, svcCtx *svc.ServiceContext) (map[string]interface{}, error) {
	// 查询Device中全部记录
	queryDevices, err := svcCtx.Db.Device.Query().All(ctx)
	if err != nil {
		return nil, fmt.Errorf("查询设备信息失败: %w", err)
	}
	driverMap, err := GetDriverMap(ctx, svcCtx, queryDevices)
	if err != nil {
		return nil, fmt.Errorf("获取驱动映射失败: %w", err)
	}

	watchList := make([]string, 0, len(queryDevices))
	result := make(map[string]interface{})

	for device, driver := range driverMap {
		driverKey := fmt.Sprintf("/driver/%s_%s", device.Type, device.ID)
		watchList = append(watchList, driverKey)

		// 生成 /driver/motor_UID/producer 数据
		producerKey := fmt.Sprintf("%s/producer", driverKey)
		producerValue := driver.Producer

		// 生成 /driver/motor_UID/instances 数据
		instancesKey := fmt.Sprintf("%s/instances", driverKey)
		instancesValue := driver.Instances

		result[producerKey] = producerValue
		result[instancesKey] = map[string]interface{}{
			"instances": instancesValue,
		}

	}
	result["/watch_list"] = map[string]interface{}{
		"watch_list": watchList,
	}

	return result, nil
}

func DelEtcdDriver(ctx context.Context, svcCtx *svc.ServiceContext, device *ent.Device) error {
	driverKey := fmt.Sprintf("/driver/%s_%s", device.Type, device.ID)
	producerKey := fmt.Sprintf("%s/producer", driverKey)
	instancesKey := fmt.Sprintf("%s/instances", driverKey)
	_, err := svcCtx.ZEtcdClient.Delete(producerKey)
	if err != nil {
		return fmt.Errorf("删除producer失败: %w", err)
	}
	_, err = svcCtx.ZEtcdClient.Delete(instancesKey)
	if err != nil {
		return fmt.Errorf("删除instances失败: %w", err)
	}
	return nil
}

// api调用这个
func Pub2Etcd(ctx context.Context, svcCtx *svc.ServiceContext) error {
	results, err := GetWatchListAndDrivers(ctx, svcCtx)
	if err != nil {
		return fmt.Errorf("获取设备信息失败: %w", err)
	}
	for key, value := range results {
		cfgTopic := key
		cfgMessage, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("序列化数据失败: %w", err)
		}
		err = svcCtx.ZEtcdClient.Publish(cfgTopic, string(cfgMessage))
		if err != nil {
			return fmt.Errorf("发布到etcd失败: %w", err)
		}
		fmt.Printf("Publishing to etcd: %s -> %s\n", cfgTopic, cfgMessage)
	}
	return nil
}
