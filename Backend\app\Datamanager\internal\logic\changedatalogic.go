package logic

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewChangeDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeDataLogic {
	return &ChangeDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ChangeDataLogic) ChangeData(in *datamanager.ChangeDataRequest) (*datamanager.ChangeDataResponse, error) {
	// todo: add your logic here and delete this line

	return &datamanager.ChangeDataResponse{}, nil
}
