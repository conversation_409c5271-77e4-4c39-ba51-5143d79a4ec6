package logic

import (
	"bytes"
	"encoding/csv"
	"time"
	"fmt"

	"GCF/app/Datamanager/internal/types"
	"GCF/app/Datamanager/internal/datamanager"
)


func convertWhereInfos(whereInfos []types.WhereInfo) []*datamanager.WhereInfo {
	var result []*datamanager.WhereInfo
	for _, info := range whereInfos {
		result = append(result, &datamanager.WhereInfo{
			ColumnName:    info.ColumnName,
			CompareSymbol: info.CompareSymbol,
			CompareValue:  info.CompareValue,
		})
	}
	return result
}

func convertTableInfo(tableInfo types.TableInfo) *datamanager.TableInfo {
	return &datamanager.TableInfo{
		TableUid: tableInfo.TableUID,
	}
}

func convertDataGroupInfo(dataGroupInfo types.DataGroupTagInfo) *datamanager.DataGroupTagInfo {
	return &datamanager.DataGroupTagInfo{
		DatagroupName: dataGroupInfo.DataGroupName,
	}
}

func convertInstanceInfo(instanceInfo types.InstanceTagInfo) *datamanager.InstanceTagInfo {
	return &datamanager.InstanceTagInfo{
		InstanceName: instanceInfo.InstanceName,
		InstanceUid:  instanceInfo.InstanceUID,
	}
}

func convertFieldsInfo(fieldsInfo types.FieldsInfo) *datamanager.FieldsInfo {
	return &datamanager.FieldsInfo{
		FieldName: fieldsInfo.FieldName,
	}
}

func convertDataRows(dataRows []*datamanager.DataRow) []types.DataRow {
	var result []types.DataRow
	for _, row := range dataRows {
		convertedRow := types.DataRow{
			TimeStamp: row.Ts.AsTime().UnixMilli(),
			InstanceInfo: types.InstanceTagInfo{
				InstanceName: row.InstanceInfo.InstanceName,
				InstanceUID:  row.InstanceInfo.InstanceUid,
			},
			DataGroupInfo: types.DataGroupTagInfo{
				DataGroupName: row.DatagroupInfo.DatagroupName,
			},
			Datas: convertSingleDatas(row.Datas),
		}
		result = append(result, convertedRow)
	}
	return result
}

func convertSingleDatas(datas []*datamanager.SingleData) []types.SingleData {
	var result []types.SingleData
	for _, data := range datas {
		result = append(result, types.SingleData{
			Field: data.Field,
			Value: data.Value,
		})
	}
	return result
}


func convertDataRowsToCSV(dataRows []types.DataRow) (string, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)
	if len(dataRows) == 0 {
		return "", nil
	}
	headers := []string{"timestamp", "instance_name", "instance_uid", "data_group_name"}
	
	fieldSet := make(map[string]bool)
	for _, row := range dataRows {
		for _, data := range row.Datas {
			if !fieldSet[data.Field] {
				headers = append(headers, data.Field)
				fieldSet[data.Field] = true
			}
		}
	}
	if err := writer.Write(headers); err != nil {
		return "", fmt.Errorf("failed to write CSV header: %v", err)
	}
	
	for _, row := range dataRows {
		record := make([]string, len(headers))
		record[0] = formatTimestamp(row.TimeStamp)
		record[1] = row.InstanceInfo.InstanceName
		record[2] = row.InstanceInfo.InstanceUID
		record[3] = row.DataGroupInfo.DataGroupName
		
		dataMap := make(map[string]string)
		for _, data := range row.Datas {
			dataMap[data.Field] = data.Value
		}
		
		for i := 4; i < len(headers); i++ {
			fieldName := headers[i]
			if value, exists := dataMap[fieldName]; exists {
				record[i] = value
			} else {
				record[i] = ""
			}
		}
		if err := writer.Write(record); err != nil {
			return "", fmt.Errorf("failed to write CSV record: %v", err)
		}
	}
	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", fmt.Errorf("CSV writer error: %v", err)
	}
	return buf.String(), nil
}

func formatTimestamp(ts int64) string {
	t := time.Unix(ts/1000, (ts%1000)*1000000)
	return t.Format("2006-01-02 15:04:05.000")
}
