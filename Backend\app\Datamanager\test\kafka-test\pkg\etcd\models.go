package etcd

// DataNode 数据节点
type DataNode struct {
	Name  string `json:"name"`
	Unit  string `json:"unit"`
	Desc  string `json:"desc"`
	Owner string `json:"owner"`
}

// DataGroup 数据组
type DataGroup struct {
	Name      string     `json:"name"`
	DataNode  []DataNode `json:"data_node"`
	Frequency string     `json:"frequency"`
	Desc      string     `json:"desc"`
}

// ProducerInfo 生产者信息
type ProducerInfo struct {
	Uname      string      `json:"Uname"`
	UID        string      `json:"UID"`
	Name       string      `json:"name"`
	DataGroups []DataGroup `json:"data_groups"`
	Revision   int64       `json:"-"` // etcd版本号，不在JSON中序列化
}

// Instance 实例信息
type Instance struct {
	Uname    string `json:"Uname"`
	Name     string `json:"name"`
	UID      string `json:"UID"`
	Hostname string `json:"hostname,omitempty"`
	IP       string `json:"IP,omitempty"`
	Desc     string `json:"desc,omitempty"`
}

// InstancesWrapper 实例包装器
type InstancesWrapper struct {
	Instances []Instance `json:"instances"`
}
