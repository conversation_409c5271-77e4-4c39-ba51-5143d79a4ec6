package datamodel

// DataNode represents a single data point's metadata.
// 对应于JSON结构中的 "data_node" 内的每个条目，如 "current", "voltage" 等。
type DataNode struct {
	Name  string `json:"name"`
	Unit  string `json:"unit"`
	Desc  string `json:"desc"`
	Owner string `json:"owner"`
}

// DataGroup represents a collection of data nodes, corresponding to a table.
// 对应于 "data_groups" 中的每个条目，如 "motor_curr_loop"。
// 这部分的变化会影响表结构。
type DataGroup struct {
	Name      string     `json:"name"`
	DataNode  []DataNode `json:"data_node"`
	Frequency string     `json:"frequency"`
	Desc      string     `json:"desc"`
}

// DataProducer represents a data producer structure.
// 对应于 "/driver/motor_UID/producer"。
type DataProducer struct {
	Uname      string      `json:"Uname"`
	UID        string      `json:"UID"`
	Name       string      `json:"name"`
	DataGroups []DataGroup `json:"data_groups"`
}

// Instance represents a specific device instance.
// 对应于 "instances" 中的每个条目，如 "motor1"。
// 这部分的变化不会影响表结构。
type Instance struct {
	Uname    string `json:"Uname"`
	Name     string `json:"name"`
	UID      string `json:"UID"`
	Hostname string `json:"hostname,omitempty"` // omitempty for model instances
	IP       string `json:"IP,omitempty"`         // omitempty for model instances
	Desc     string `json:"desc,omitempty"`       // omitempty for driver instances
}

// InstancesWrapper represents the instances wrapper structure.
// 对应于 "/driver/motor_UID/instances"。
type InstancesWrapper struct {
	Instances []Instance `json:"instances"`
}

// DriverInfo represents the driver basic information.
// 对应于 "/driver/motor_UID"。
type DriverInfo struct {
	UID   string `json:"UID"`
	Name  string `json:"name"`
	Uname string `json:"Uname"`
}

// WatchList represents a list of watched items.
// 对应于 "/watch_list"。
// 这部分的变化不会影响表结构。

type WatchList struct {
	WatchList []string `json:"watch_list"`
}