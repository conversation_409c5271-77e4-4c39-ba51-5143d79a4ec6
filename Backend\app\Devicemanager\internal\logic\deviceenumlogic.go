package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeviceEnumLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeviceEnumLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeviceEnumLogic {
	return &DeviceEnumLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeviceEnumLogic) DeviceEnum(req *types.DeviceTypeEnumRequest) (resp *types.DeviceTypeEnumResponse, err error) {
	//遍历utils包中的设备类型常量,命名都是 DeviceTypeXXX

	deviceTypeEnum := []string{utils.DeviceTypeFan,
		utils.DeviceTypeMotor,
		utils.DeviceTypeVirtual,
		utils.DeviceTypePC,
		utils.DeviceTypeEdge,
	}

	response := &types.DeviceTypeEnumResponse{
		DeviceTypeEnum: deviceTypeEnum,
	}
	return response, nil
}
