// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Modelmanager/internal/ent/model"
	"GCF/app/Modelmanager/internal/ent/schema"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	modelFields := schema.Model{}.Fields()
	_ = modelFields
	// modelDescModelID is the schema descriptor for model_id field.
	modelDescModelID := modelFields[0].Descriptor()
	// model.ModelIDValidator is a validator for the "model_id" field. It is called by the builders before save.
	model.ModelIDValidator = modelDescModelID.Validators[0].(func(string) error)
	// modelDescFrameworkVersion is the schema descriptor for framework_version field.
	modelDescFrameworkVersion := modelFields[2].Descriptor()
	// model.DefaultFrameworkVersion holds the default value on creation for the framework_version field.
	model.DefaultFrameworkVersion = modelDescFrameworkVersion.Default.(string)
	// modelDescTypeDesc is the schema descriptor for type_desc field.
	modelDescTypeDesc := modelFields[3].Descriptor()
	// model.DefaultTypeDesc holds the default value on creation for the type_desc field.
	model.DefaultTypeDesc = modelDescTypeDesc.Default.(string)
	// modelDescStoragePath is the schema descriptor for storage_path field.
	modelDescStoragePath := modelFields[4].Descriptor()
	// model.StoragePathValidator is a validator for the "storage_path" field. It is called by the builders before save.
	model.StoragePathValidator = modelDescStoragePath.Validators[0].(func(string) error)
	// modelDescAccuracy is the schema descriptor for accuracy field.
	modelDescAccuracy := modelFields[5].Descriptor()
	// model.DefaultAccuracy holds the default value on creation for the accuracy field.
	model.DefaultAccuracy = modelDescAccuracy.Default.(float64)
	// model.AccuracyValidator is a validator for the "accuracy" field. It is called by the builders before save.
	model.AccuracyValidator = modelDescAccuracy.Validators[0].(func(float64) error)
	// modelDescPrecision is the schema descriptor for precision field.
	modelDescPrecision := modelFields[6].Descriptor()
	// model.DefaultPrecision holds the default value on creation for the precision field.
	model.DefaultPrecision = modelDescPrecision.Default.(float64)
	// model.PrecisionValidator is a validator for the "precision" field. It is called by the builders before save.
	model.PrecisionValidator = modelDescPrecision.Validators[0].(func(float64) error)
	// modelDescRecall is the schema descriptor for recall field.
	modelDescRecall := modelFields[7].Descriptor()
	// model.DefaultRecall holds the default value on creation for the recall field.
	model.DefaultRecall = modelDescRecall.Default.(float64)
	// model.RecallValidator is a validator for the "recall" field. It is called by the builders before save.
	model.RecallValidator = modelDescRecall.Validators[0].(func(float64) error)
	// modelDescF1Score is the schema descriptor for f1_score field.
	modelDescF1Score := modelFields[8].Descriptor()
	// model.DefaultF1Score holds the default value on creation for the f1_score field.
	model.DefaultF1Score = modelDescF1Score.Default.(float64)
	// model.F1ScoreValidator is a validator for the "f1_score" field. It is called by the builders before save.
	model.F1ScoreValidator = modelDescF1Score.Validators[0].(func(float64) error)
	// modelDescCreatedBy is the schema descriptor for created_by field.
	modelDescCreatedBy := modelFields[9].Descriptor()
	// model.DefaultCreatedBy holds the default value on creation for the created_by field.
	model.DefaultCreatedBy = modelDescCreatedBy.Default.(string)
	// modelDescCreateUnix is the schema descriptor for create_unix field.
	modelDescCreateUnix := modelFields[10].Descriptor()
	// model.DefaultCreateUnix holds the default value on creation for the create_unix field.
	model.DefaultCreateUnix = modelDescCreateUnix.Default.(func() time.Time)
	// modelDescUpdateUnix is the schema descriptor for update_unix field.
	modelDescUpdateUnix := modelFields[11].Descriptor()
	// model.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	model.DefaultUpdateUnix = modelDescUpdateUnix.Default.(func() time.Time)
	// model.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	model.UpdateDefaultUpdateUnix = modelDescUpdateUnix.UpdateDefault.(func() time.Time)
}
