package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type PushDataConfigToEtcdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewPushDataConfigToEtcdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PushDataConfigToEtcdLogic {
	return &PushDataConfigToEtcdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *PushDataConfigToEtcdLogic) PushDataConfigToEtcd(in *devicemanager.PushDataConfigToEtcdRequest) (*devicemanager.PushDataConfigToEtcdResponse, error) {
	// todo: add your logic here and delete this line

	return &devicemanager.PushDataConfigToEtcdResponse{}, nil
}
