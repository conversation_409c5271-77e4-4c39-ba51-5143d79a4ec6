// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: rpc/model.proto

package modelmanager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ModelNameVersion string                 `protobuf:"bytes,1,opt,name=model_name_version,json=modelNameVersion,proto3" json:"model_name_version,omitempty"`
	FrameworkVersion string                 `protobuf:"bytes,2,opt,name=framework_version,json=frameworkVersion,proto3" json:"framework_version,omitempty"`
	TypeDesc         string                 `protobuf:"bytes,3,opt,name=type_desc,json=typeDesc,proto3" json:"type_desc,omitempty"`
	StoragePath      string                 `protobuf:"bytes,4,opt,name=storage_path,json=storagePath,proto3" json:"storage_path,omitempty"`
	CreatedBy        string                 `protobuf:"bytes,5,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ModelInfo) Reset() {
	*x = ModelInfo{}
	mi := &file_rpc_model_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelInfo) ProtoMessage() {}

func (x *ModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelInfo.ProtoReflect.Descriptor instead.
func (*ModelInfo) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{0}
}

func (x *ModelInfo) GetModelNameVersion() string {
	if x != nil {
		return x.ModelNameVersion
	}
	return ""
}

func (x *ModelInfo) GetFrameworkVersion() string {
	if x != nil {
		return x.FrameworkVersion
	}
	return ""
}

func (x *ModelInfo) GetTypeDesc() string {
	if x != nil {
		return x.TypeDesc
	}
	return ""
}

func (x *ModelInfo) GetStoragePath() string {
	if x != nil {
		return x.StoragePath
	}
	return ""
}

func (x *ModelInfo) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type ModelMetricsInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accuracy      float64                `protobuf:"fixed64,1,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	Precision     float64                `protobuf:"fixed64,2,opt,name=precision,proto3" json:"precision,omitempty"`
	Recall        float64                `protobuf:"fixed64,3,opt,name=recall,proto3" json:"recall,omitempty"`
	F1Score       float64                `protobuf:"fixed64,4,opt,name=f1_score,json=f1Score,proto3" json:"f1_score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelMetricsInfo) Reset() {
	*x = ModelMetricsInfo{}
	mi := &file_rpc_model_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelMetricsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMetricsInfo) ProtoMessage() {}

func (x *ModelMetricsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMetricsInfo.ProtoReflect.Descriptor instead.
func (*ModelMetricsInfo) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{1}
}

func (x *ModelMetricsInfo) GetAccuracy() float64 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *ModelMetricsInfo) GetPrecision() float64 {
	if x != nil {
		return x.Precision
	}
	return 0
}

func (x *ModelMetricsInfo) GetRecall() float64 {
	if x != nil {
		return x.Recall
	}
	return 0
}

func (x *ModelMetricsInfo) GetF1Score() float64 {
	if x != nil {
		return x.F1Score
	}
	return 0
}

type ModelTimeStampInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ts            *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelTimeStampInfo) Reset() {
	*x = ModelTimeStampInfo{}
	mi := &file_rpc_model_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelTimeStampInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelTimeStampInfo) ProtoMessage() {}

func (x *ModelTimeStampInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelTimeStampInfo.ProtoReflect.Descriptor instead.
func (*ModelTimeStampInfo) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{2}
}

func (x *ModelTimeStampInfo) GetTs() *timestamppb.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

type ModelMetaData struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ModelId          string                 `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	ModelNameVersion string                 `protobuf:"bytes,2,opt,name=model_name_version,json=modelNameVersion,proto3" json:"model_name_version,omitempty"`
	FrameworkVersion string                 `protobuf:"bytes,3,opt,name=framework_version,json=frameworkVersion,proto3" json:"framework_version,omitempty"`
	TypeDesc         string                 `protobuf:"bytes,4,opt,name=type_desc,json=typeDesc,proto3" json:"type_desc,omitempty"`
	StoragePath      string                 `protobuf:"bytes,5,opt,name=storage_path,json=storagePath,proto3" json:"storage_path,omitempty"`
	Accuracy         float64                `protobuf:"fixed64,6,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	Precision        float64                `protobuf:"fixed64,7,opt,name=precision,proto3" json:"precision,omitempty"`
	Recall           float64                `protobuf:"fixed64,8,opt,name=recall,proto3" json:"recall,omitempty"`
	F1Score          float64                `protobuf:"fixed64,9,opt,name=f1_score,json=f1Score,proto3" json:"f1_score,omitempty"`
	CreatedBy        string                 `protobuf:"bytes,10,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedTs        *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_ts,json=createdTs,proto3" json:"created_ts,omitempty"`
	UpdatedTs        *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_ts,json=updatedTs,proto3" json:"updated_ts,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ModelMetaData) Reset() {
	*x = ModelMetaData{}
	mi := &file_rpc_model_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMetaData) ProtoMessage() {}

func (x *ModelMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMetaData.ProtoReflect.Descriptor instead.
func (*ModelMetaData) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{3}
}

func (x *ModelMetaData) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ModelMetaData) GetModelNameVersion() string {
	if x != nil {
		return x.ModelNameVersion
	}
	return ""
}

func (x *ModelMetaData) GetFrameworkVersion() string {
	if x != nil {
		return x.FrameworkVersion
	}
	return ""
}

func (x *ModelMetaData) GetTypeDesc() string {
	if x != nil {
		return x.TypeDesc
	}
	return ""
}

func (x *ModelMetaData) GetStoragePath() string {
	if x != nil {
		return x.StoragePath
	}
	return ""
}

func (x *ModelMetaData) GetAccuracy() float64 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *ModelMetaData) GetPrecision() float64 {
	if x != nil {
		return x.Precision
	}
	return 0
}

func (x *ModelMetaData) GetRecall() float64 {
	if x != nil {
		return x.Recall
	}
	return 0
}

func (x *ModelMetaData) GetF1Score() float64 {
	if x != nil {
		return x.F1Score
	}
	return 0
}

func (x *ModelMetaData) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ModelMetaData) GetCreatedTs() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTs
	}
	return nil
}

func (x *ModelMetaData) GetUpdatedTs() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedTs
	}
	return nil
}

type CreateModelConfigRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ModelInfo        *ModelInfo             `protobuf:"bytes,1,opt,name=model_info,json=modelInfo,proto3" json:"model_info,omitempty"`
	ModelMetricsInfo *ModelMetricsInfo      `protobuf:"bytes,2,opt,name=model_metrics_info,json=modelMetricsInfo,proto3" json:"model_metrics_info,omitempty"`
	TrainedModel     []byte                 `protobuf:"bytes,3,opt,name=trained_model,json=trainedModel,proto3" json:"trained_model,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateModelConfigRequest) Reset() {
	*x = CreateModelConfigRequest{}
	mi := &file_rpc_model_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateModelConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelConfigRequest) ProtoMessage() {}

func (x *CreateModelConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelConfigRequest.ProtoReflect.Descriptor instead.
func (*CreateModelConfigRequest) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{4}
}

func (x *CreateModelConfigRequest) GetModelInfo() *ModelInfo {
	if x != nil {
		return x.ModelInfo
	}
	return nil
}

func (x *CreateModelConfigRequest) GetModelMetricsInfo() *ModelMetricsInfo {
	if x != nil {
		return x.ModelMetricsInfo
	}
	return nil
}

func (x *CreateModelConfigRequest) GetTrainedModel() []byte {
	if x != nil {
		return x.TrainedModel
	}
	return nil
}

type CreateModelConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModelMetaData *ModelMetaData         `protobuf:"bytes,1,opt,name=model_meta_data,json=modelMetaData,proto3" json:"model_meta_data,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateModelConfigResponse) Reset() {
	*x = CreateModelConfigResponse{}
	mi := &file_rpc_model_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateModelConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateModelConfigResponse) ProtoMessage() {}

func (x *CreateModelConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateModelConfigResponse.ProtoReflect.Descriptor instead.
func (*CreateModelConfigResponse) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{5}
}

func (x *CreateModelConfigResponse) GetModelMetaData() *ModelMetaData {
	if x != nil {
		return x.ModelMetaData
	}
	return nil
}

func (x *CreateModelConfigResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetTrainedModelRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModelId       string                 `protobuf:"bytes,1,opt,name=model_id,json=modelId,proto3" json:"model_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTrainedModelRequest) Reset() {
	*x = GetTrainedModelRequest{}
	mi := &file_rpc_model_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTrainedModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrainedModelRequest) ProtoMessage() {}

func (x *GetTrainedModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrainedModelRequest.ProtoReflect.Descriptor instead.
func (*GetTrainedModelRequest) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{6}
}

func (x *GetTrainedModelRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

type GetTrainedModelResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrainedModel  []byte                 `protobuf:"bytes,1,opt,name=trained_model,json=trainedModel,proto3" json:"trained_model,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTrainedModelResponse) Reset() {
	*x = GetTrainedModelResponse{}
	mi := &file_rpc_model_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTrainedModelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTrainedModelResponse) ProtoMessage() {}

func (x *GetTrainedModelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_model_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTrainedModelResponse.ProtoReflect.Descriptor instead.
func (*GetTrainedModelResponse) Descriptor() ([]byte, []int) {
	return file_rpc_model_proto_rawDescGZIP(), []int{7}
}

func (x *GetTrainedModelResponse) GetTrainedModel() []byte {
	if x != nil {
		return x.TrainedModel
	}
	return nil
}

func (x *GetTrainedModelResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_rpc_model_proto protoreflect.FileDescriptor

const file_rpc_model_proto_rawDesc = "" +
	"\n" +
	"\x0frpc/model.proto\x12\fmodelmanager\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc5\x01\n" +
	"\tModelInfo\x12,\n" +
	"\x12model_name_version\x18\x01 \x01(\tR\x10modelNameVersion\x12+\n" +
	"\x11framework_version\x18\x02 \x01(\tR\x10frameworkVersion\x12\x1b\n" +
	"\ttype_desc\x18\x03 \x01(\tR\btypeDesc\x12!\n" +
	"\fstorage_path\x18\x04 \x01(\tR\vstoragePath\x12\x1d\n" +
	"\n" +
	"created_by\x18\x05 \x01(\tR\tcreatedBy\"\x7f\n" +
	"\x10ModelMetricsInfo\x12\x1a\n" +
	"\baccuracy\x18\x01 \x01(\x01R\baccuracy\x12\x1c\n" +
	"\tprecision\x18\x02 \x01(\x01R\tprecision\x12\x16\n" +
	"\x06recall\x18\x03 \x01(\x01R\x06recall\x12\x19\n" +
	"\bf1_score\x18\x04 \x01(\x01R\af1Score\"@\n" +
	"\x12ModelTimeStampInfo\x12*\n" +
	"\x02ts\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\x02ts\"\xc7\x03\n" +
	"\rModelMetaData\x12\x19\n" +
	"\bmodel_id\x18\x01 \x01(\tR\amodelId\x12,\n" +
	"\x12model_name_version\x18\x02 \x01(\tR\x10modelNameVersion\x12+\n" +
	"\x11framework_version\x18\x03 \x01(\tR\x10frameworkVersion\x12\x1b\n" +
	"\ttype_desc\x18\x04 \x01(\tR\btypeDesc\x12!\n" +
	"\fstorage_path\x18\x05 \x01(\tR\vstoragePath\x12\x1a\n" +
	"\baccuracy\x18\x06 \x01(\x01R\baccuracy\x12\x1c\n" +
	"\tprecision\x18\a \x01(\x01R\tprecision\x12\x16\n" +
	"\x06recall\x18\b \x01(\x01R\x06recall\x12\x19\n" +
	"\bf1_score\x18\t \x01(\x01R\af1Score\x12\x1d\n" +
	"\n" +
	"created_by\x18\n" +
	" \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_ts\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedTs\x129\n" +
	"\n" +
	"updated_ts\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedTs\"\xc5\x01\n" +
	"\x18CreateModelConfigRequest\x126\n" +
	"\n" +
	"model_info\x18\x01 \x01(\v2\x17.modelmanager.ModelInfoR\tmodelInfo\x12L\n" +
	"\x12model_metrics_info\x18\x02 \x01(\v2\x1e.modelmanager.ModelMetricsInfoR\x10modelMetricsInfo\x12#\n" +
	"\rtrained_model\x18\x03 \x01(\fR\ftrainedModel\"x\n" +
	"\x19CreateModelConfigResponse\x12C\n" +
	"\x0fmodel_meta_data\x18\x01 \x01(\v2\x1b.modelmanager.ModelMetaDataR\rmodelMetaData\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\"3\n" +
	"\x16GetTrainedModelRequest\x12\x19\n" +
	"\bmodel_id\x18\x01 \x01(\tR\amodelId\"V\n" +
	"\x17GetTrainedModelResponse\x12#\n" +
	"\rtrained_model\x18\x01 \x01(\fR\ftrainedModel\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status2\xcd\x01\n" +
	"\x05Model\x12d\n" +
	"\x11CreateModelConfig\x12&.modelmanager.CreateModelConfigRequest\x1a'.modelmanager.CreateModelConfigResponse\x12^\n" +
	"\x0fGetTrainedModel\x12$.modelmanager.GetTrainedModelRequest\x1a%.modelmanager.GetTrainedModelResponseB\x10Z\x0e./modelmanagerb\x06proto3"

var (
	file_rpc_model_proto_rawDescOnce sync.Once
	file_rpc_model_proto_rawDescData []byte
)

func file_rpc_model_proto_rawDescGZIP() []byte {
	file_rpc_model_proto_rawDescOnce.Do(func() {
		file_rpc_model_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_model_proto_rawDesc), len(file_rpc_model_proto_rawDesc)))
	})
	return file_rpc_model_proto_rawDescData
}

var file_rpc_model_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_rpc_model_proto_goTypes = []any{
	(*ModelInfo)(nil),                 // 0: modelmanager.ModelInfo
	(*ModelMetricsInfo)(nil),          // 1: modelmanager.ModelMetricsInfo
	(*ModelTimeStampInfo)(nil),        // 2: modelmanager.ModelTimeStampInfo
	(*ModelMetaData)(nil),             // 3: modelmanager.ModelMetaData
	(*CreateModelConfigRequest)(nil),  // 4: modelmanager.CreateModelConfigRequest
	(*CreateModelConfigResponse)(nil), // 5: modelmanager.CreateModelConfigResponse
	(*GetTrainedModelRequest)(nil),    // 6: modelmanager.GetTrainedModelRequest
	(*GetTrainedModelResponse)(nil),   // 7: modelmanager.GetTrainedModelResponse
	(*timestamppb.Timestamp)(nil),     // 8: google.protobuf.Timestamp
}
var file_rpc_model_proto_depIdxs = []int32{
	8, // 0: modelmanager.ModelTimeStampInfo.ts:type_name -> google.protobuf.Timestamp
	8, // 1: modelmanager.ModelMetaData.created_ts:type_name -> google.protobuf.Timestamp
	8, // 2: modelmanager.ModelMetaData.updated_ts:type_name -> google.protobuf.Timestamp
	0, // 3: modelmanager.CreateModelConfigRequest.model_info:type_name -> modelmanager.ModelInfo
	1, // 4: modelmanager.CreateModelConfigRequest.model_metrics_info:type_name -> modelmanager.ModelMetricsInfo
	3, // 5: modelmanager.CreateModelConfigResponse.model_meta_data:type_name -> modelmanager.ModelMetaData
	4, // 6: modelmanager.Model.CreateModelConfig:input_type -> modelmanager.CreateModelConfigRequest
	6, // 7: modelmanager.Model.GetTrainedModel:input_type -> modelmanager.GetTrainedModelRequest
	5, // 8: modelmanager.Model.CreateModelConfig:output_type -> modelmanager.CreateModelConfigResponse
	7, // 9: modelmanager.Model.GetTrainedModel:output_type -> modelmanager.GetTrainedModelResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_rpc_model_proto_init() }
func file_rpc_model_proto_init() {
	if File_rpc_model_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_model_proto_rawDesc), len(file_rpc_model_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_model_proto_goTypes,
		DependencyIndexes: file_rpc_model_proto_depIdxs,
		MessageInfos:      file_rpc_model_proto_msgTypes,
	}.Build()
	File_rpc_model_proto = out.File
	file_rpc_model_proto_goTypes = nil
	file_rpc_model_proto_depIdxs = nil
}
