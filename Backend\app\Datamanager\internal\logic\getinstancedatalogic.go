package logic

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInstanceDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetInstanceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInstanceDataLogic {
	return &GetInstanceDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetInstanceDataLogic) GetInstanceData(in *datamanager.GetInstanceDataRequest) (*datamanager.GetInstanceDataResponse, error) {
	// todo: add your logic here and delete this line
	return l.svcCtx.UtilDataService.GetInstanceData(l.ctx, in)
}
