// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.4
// Source: model.proto

package model

import (
	"context"

	"GCF/app/Modelmanager/internal/modelmanager"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CreateModelConfigRequest  = modelmanager.CreateModelConfigRequest
	CreateModelConfigResponse = modelmanager.CreateModelConfigResponse
	GetTrainedModelRequest    = modelmanager.GetTrainedModelRequest
	GetTrainedModelResponse   = modelmanager.GetTrainedModelResponse
	ModelInfo                 = modelmanager.ModelInfo
	ModelMetaData             = modelmanager.ModelMetaData
	ModelMetricsInfo          = modelmanager.ModelMetricsInfo
	ModelTimeStampInfo        = modelmanager.ModelTimeStampInfo

	Model interface {
		CreateModelConfig(ctx context.Context, in *CreateModelConfigRequest, opts ...grpc.CallOption) (*CreateModelConfigResponse, error)
		GetTrainedModel(ctx context.Context, in *GetTrainedModelRequest, opts ...grpc.CallOption) (*GetTrainedModelResponse, error)
	}

	defaultModel struct {
		cli zrpc.Client
	}
)

func NewModel(cli zrpc.Client) Model {
	return &defaultModel{
		cli: cli,
	}
}

func (m *defaultModel) CreateModelConfig(ctx context.Context, in *CreateModelConfigRequest, opts ...grpc.CallOption) (*CreateModelConfigResponse, error) {
	client := modelmanager.NewModelClient(m.cli.Conn())
	return client.CreateModelConfig(ctx, in, opts...)
}

func (m *defaultModel) GetTrainedModel(ctx context.Context, in *GetTrainedModelRequest, opts ...grpc.CallOption) (*GetTrainedModelResponse, error) {
	client := modelmanager.NewModelClient(m.cli.Conn())
	return client.GetTrainedModel(ctx, in, opts...)
}
