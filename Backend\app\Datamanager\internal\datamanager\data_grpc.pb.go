// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: rpc/data.proto

package datamanager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Data_GetProducerData_FullMethodName    = "/datamanager.Data/GetProducerData"
	Data_DeleteProducerData_FullMethodName = "/datamanager.Data/DeleteProducerData"
	Data_ChangeProducerData_FullMethodName = "/datamanager.Data/ChangeProducerData"
	Data_GetInstanceData_FullMethodName    = "/datamanager.Data/GetInstanceData"
	Data_DeleteInstanceData_FullMethodName = "/datamanager.Data/DeleteInstanceData"
	Data_ChangeInstanceData_FullMethodName = "/datamanager.Data/ChangeInstanceData"
	Data_ChangeData_FullMethodName         = "/datamanager.Data/ChangeData"
)

// DataClient is the client API for Data service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataClient interface {
	GetProducerData(ctx context.Context, in *GetProducerDataRequest, opts ...grpc.CallOption) (*GetProducerDataResponse, error)
	DeleteProducerData(ctx context.Context, in *DeleteProducerDataRequest, opts ...grpc.CallOption) (*DeleteProducerDataResponse, error)
	ChangeProducerData(ctx context.Context, in *ChangeProducerDataRequest, opts ...grpc.CallOption) (*ChangeProducerDataResponse, error)
	GetInstanceData(ctx context.Context, in *GetInstanceDataRequest, opts ...grpc.CallOption) (*GetInstanceDataResponse, error)
	DeleteInstanceData(ctx context.Context, in *DeleteInstanceDataRequest, opts ...grpc.CallOption) (*DeleteInstanceDataResponse, error)
	ChangeInstanceData(ctx context.Context, in *ChangeInstanceDataRequest, opts ...grpc.CallOption) (*ChangeInstanceDataResponse, error)
	ChangeData(ctx context.Context, in *ChangeDataRequest, opts ...grpc.CallOption) (*ChangeDataResponse, error)
}

type dataClient struct {
	cc grpc.ClientConnInterface
}

func NewDataClient(cc grpc.ClientConnInterface) DataClient {
	return &dataClient{cc}
}

func (c *dataClient) GetProducerData(ctx context.Context, in *GetProducerDataRequest, opts ...grpc.CallOption) (*GetProducerDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProducerDataResponse)
	err := c.cc.Invoke(ctx, Data_GetProducerData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataClient) DeleteProducerData(ctx context.Context, in *DeleteProducerDataRequest, opts ...grpc.CallOption) (*DeleteProducerDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteProducerDataResponse)
	err := c.cc.Invoke(ctx, Data_DeleteProducerData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataClient) ChangeProducerData(ctx context.Context, in *ChangeProducerDataRequest, opts ...grpc.CallOption) (*ChangeProducerDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeProducerDataResponse)
	err := c.cc.Invoke(ctx, Data_ChangeProducerData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataClient) GetInstanceData(ctx context.Context, in *GetInstanceDataRequest, opts ...grpc.CallOption) (*GetInstanceDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInstanceDataResponse)
	err := c.cc.Invoke(ctx, Data_GetInstanceData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataClient) DeleteInstanceData(ctx context.Context, in *DeleteInstanceDataRequest, opts ...grpc.CallOption) (*DeleteInstanceDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteInstanceDataResponse)
	err := c.cc.Invoke(ctx, Data_DeleteInstanceData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataClient) ChangeInstanceData(ctx context.Context, in *ChangeInstanceDataRequest, opts ...grpc.CallOption) (*ChangeInstanceDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeInstanceDataResponse)
	err := c.cc.Invoke(ctx, Data_ChangeInstanceData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataClient) ChangeData(ctx context.Context, in *ChangeDataRequest, opts ...grpc.CallOption) (*ChangeDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeDataResponse)
	err := c.cc.Invoke(ctx, Data_ChangeData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataServer is the server API for Data service.
// All implementations must embed UnimplementedDataServer
// for forward compatibility.
type DataServer interface {
	GetProducerData(context.Context, *GetProducerDataRequest) (*GetProducerDataResponse, error)
	DeleteProducerData(context.Context, *DeleteProducerDataRequest) (*DeleteProducerDataResponse, error)
	ChangeProducerData(context.Context, *ChangeProducerDataRequest) (*ChangeProducerDataResponse, error)
	GetInstanceData(context.Context, *GetInstanceDataRequest) (*GetInstanceDataResponse, error)
	DeleteInstanceData(context.Context, *DeleteInstanceDataRequest) (*DeleteInstanceDataResponse, error)
	ChangeInstanceData(context.Context, *ChangeInstanceDataRequest) (*ChangeInstanceDataResponse, error)
	ChangeData(context.Context, *ChangeDataRequest) (*ChangeDataResponse, error)
	mustEmbedUnimplementedDataServer()
}

// UnimplementedDataServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDataServer struct{}

func (UnimplementedDataServer) GetProducerData(context.Context, *GetProducerDataRequest) (*GetProducerDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProducerData not implemented")
}
func (UnimplementedDataServer) DeleteProducerData(context.Context, *DeleteProducerDataRequest) (*DeleteProducerDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProducerData not implemented")
}
func (UnimplementedDataServer) ChangeProducerData(context.Context, *ChangeProducerDataRequest) (*ChangeProducerDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeProducerData not implemented")
}
func (UnimplementedDataServer) GetInstanceData(context.Context, *GetInstanceDataRequest) (*GetInstanceDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstanceData not implemented")
}
func (UnimplementedDataServer) DeleteInstanceData(context.Context, *DeleteInstanceDataRequest) (*DeleteInstanceDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstanceData not implemented")
}
func (UnimplementedDataServer) ChangeInstanceData(context.Context, *ChangeInstanceDataRequest) (*ChangeInstanceDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeInstanceData not implemented")
}
func (UnimplementedDataServer) ChangeData(context.Context, *ChangeDataRequest) (*ChangeDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeData not implemented")
}
func (UnimplementedDataServer) mustEmbedUnimplementedDataServer() {}
func (UnimplementedDataServer) testEmbeddedByValue()              {}

// UnsafeDataServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataServer will
// result in compilation errors.
type UnsafeDataServer interface {
	mustEmbedUnimplementedDataServer()
}

func RegisterDataServer(s grpc.ServiceRegistrar, srv DataServer) {
	// If the following call pancis, it indicates UnimplementedDataServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Data_ServiceDesc, srv)
}

func _Data_GetProducerData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProducerDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).GetProducerData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_GetProducerData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).GetProducerData(ctx, req.(*GetProducerDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Data_DeleteProducerData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProducerDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).DeleteProducerData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_DeleteProducerData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).DeleteProducerData(ctx, req.(*DeleteProducerDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Data_ChangeProducerData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeProducerDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).ChangeProducerData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_ChangeProducerData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).ChangeProducerData(ctx, req.(*ChangeProducerDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Data_GetInstanceData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).GetInstanceData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_GetInstanceData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).GetInstanceData(ctx, req.(*GetInstanceDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Data_DeleteInstanceData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).DeleteInstanceData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_DeleteInstanceData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).DeleteInstanceData(ctx, req.(*DeleteInstanceDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Data_ChangeInstanceData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeInstanceDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).ChangeInstanceData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_ChangeInstanceData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).ChangeInstanceData(ctx, req.(*ChangeInstanceDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Data_ChangeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataServer).ChangeData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Data_ChangeData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataServer).ChangeData(ctx, req.(*ChangeDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Data_ServiceDesc is the grpc.ServiceDesc for Data service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Data_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "datamanager.Data",
	HandlerType: (*DataServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProducerData",
			Handler:    _Data_GetProducerData_Handler,
		},
		{
			MethodName: "DeleteProducerData",
			Handler:    _Data_DeleteProducerData_Handler,
		},
		{
			MethodName: "ChangeProducerData",
			Handler:    _Data_ChangeProducerData_Handler,
		},
		{
			MethodName: "GetInstanceData",
			Handler:    _Data_GetInstanceData_Handler,
		},
		{
			MethodName: "DeleteInstanceData",
			Handler:    _Data_DeleteInstanceData_Handler,
		},
		{
			MethodName: "ChangeInstanceData",
			Handler:    _Data_ChangeInstanceData_Handler,
		},
		{
			MethodName: "ChangeData",
			Handler:    _Data_ChangeData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/data.proto",
}
