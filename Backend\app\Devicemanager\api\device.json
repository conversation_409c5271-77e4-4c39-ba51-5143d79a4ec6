{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "swagger": "2.0", "info": {"title": "device Service", "version": "1.0"}, "basePath": "/", "paths": {"/api/v1/device/data/fetch": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "FetchDeviceDataHandler", "operationId": "fetchDeviceDataHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID"], "properties": {"deviceUID": {"type": "string"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}, "frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}}}}, "/api/v1/device/device/control": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "ControlDeviceHandler", "operationId": "controlDeviceHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID", "frameInfo"], "properties": {"deviceUID": {"type": "string"}, "frameInfo": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}, "frameInfo": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}}}}, "/api/v1/device/device/list": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "ListDeviceHandler", "operationId": "listDeviceHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"deviceUID": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}, "healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "pageNum": {"type": "integer"}, "pageSize": {"type": "integer"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"devices": {"type": "array", "items": {"type": "object", "required": ["deviceMeta", "deviceWorkStatus", "deviceResourceInfo", "frameInfos"], "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceResourceInfo": {"type": "object", "required": ["ip", "type", "protocol"], "properties": {"cpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "description": {"type": "string"}, "devicename": {"type": "string"}, "disk": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "gpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "ip": {"type": "string"}, "mem": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "os": {"type": "string"}, "protocol": {"type": "array", "enum": ["udp", "modbus", "uart"], "items": {"type": "string"}}, "type": {"type": "string", "enum": ["fan", "motor", "virtual", "pc", "edge"]}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}, "frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}, "pageNum": {"type": "integer"}, "pageSize": {"type": "integer"}, "total": {"type": "integer"}}}}}}}, "/api/v1/device/frame/create": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "CreateDeviceFrameHandler", "operationId": "createDeviceFrameHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID", "frameInfos"], "properties": {"deviceUID": {"type": "string"}, "frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}}}}}}}, "/api/v1/device/frame/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "DeleteDeviceFrameHandler", "operationId": "deleteDeviceFrameHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"description": "帧描述", "type": "string"}, "deviceUID": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}, "frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}}}}, "/api/v1/device/frame/info/get": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "GetFrameInfoHandler", "operationId": "getFrameInfoHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"deviceUID": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}}}}, "/api/v1/device/frame/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "operationId": "updateFrameHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID", "frameInfos"], "properties": {"deviceUID": {"type": "string"}, "frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}}}}}}}, "/api/v1/device/info/get": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "GetDeviceInfoHandler", "operationId": "getDeviceInfoHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID"], "properties": {"deviceUID": {"type": "string"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}}}}}}}, "/api/v1/device/resource/create": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "CreateDeviceResourceHandler", "operationId": "createDeviceResourceHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceResourceInfo"], "properties": {"deviceResourceInfo": {"type": "object", "required": ["ip", "type", "protocol"], "properties": {"cpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "description": {"type": "string"}, "devicename": {"type": "string"}, "disk": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "gpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "ip": {"type": "string"}, "mem": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "os": {"type": "string"}, "protocol": {"type": "array", "enum": ["udp", "modbus", "uart"], "items": {"type": "string"}}, "type": {"type": "string", "enum": ["fan", "motor", "virtual", "pc", "edge"]}}}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}}}}}}}, "/api/v1/device/resource/delete": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "DeleteDeviceResourceHandler", "operationId": "deleteDeviceResourceHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID"], "properties": {"deviceUID": {"type": "string"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceResourceInfo": {"type": "object", "required": ["ip", "type", "protocol"], "properties": {"cpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "description": {"type": "string"}, "devicename": {"type": "string"}, "disk": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "gpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "ip": {"type": "string"}, "mem": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "os": {"type": "string"}, "protocol": {"type": "array", "enum": ["udp", "modbus", "uart"], "items": {"type": "string"}}, "type": {"type": "string", "enum": ["fan", "motor", "virtual", "pc", "edge"]}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}}}}}}}, "/api/v1/device/resource/enum": {"post": {"consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "schemes": ["https"], "summary": "DeviceEnumHandler", "operationId": "deviceEnumHandler", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceTypeEnum": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/v1/device/resource/protocol/enum": {"post": {"consumes": ["application/x-www-form-urlencoded"], "produces": ["application/json"], "schemes": ["https"], "summary": "ProtocolEnumHandler", "operationId": "protocolEnumHandler", "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"modbus": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc", "datas"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "protocolTypeEnum": {"type": "array", "items": {"type": "string"}}, "uart": {"type": "object", "required": ["header", "addr", "cmd", "tail", "datas"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udp": {"type": "object", "required": ["type", "header", "typeID", "datas"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}}}}}, "/api/v1/device/resource/update": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["https"], "summary": "UpdateDeviceResourceHandler", "operationId": "updateDeviceResourceHandler", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "required": ["deviceUID", "deviceResourceInfo"], "properties": {"deviceResourceInfo": {"type": "object", "required": ["ip", "type", "protocol"], "properties": {"cpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "description": {"type": "string"}, "devicename": {"type": "string"}, "disk": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "gpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "ip": {"type": "string"}, "mem": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "os": {"type": "string"}, "protocol": {"type": "array", "enum": ["udp", "modbus", "uart"], "items": {"type": "string"}}, "type": {"type": "string", "enum": ["fan", "motor", "virtual", "pc", "edge"]}}}, "deviceUID": {"type": "string"}}}}], "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"deviceMeta": {"type": "object", "required": ["deviceUID", "frameMetas"], "properties": {"description": {"type": "string"}, "deviceUID": {"type": "string"}, "devicename": {"type": "string"}, "frameMetas": {"type": "array", "items": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}, "deviceResourceInfo": {"type": "object", "required": ["ip", "type", "protocol"], "properties": {"cpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "description": {"type": "string"}, "devicename": {"type": "string"}, "disk": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "gpu": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "ip": {"type": "string"}, "mem": {"type": "object", "required": ["amount", "type", "unit"], "properties": {"amount": {"type": "integer"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "os": {"type": "string"}, "protocol": {"type": "array", "enum": ["udp", "modbus", "uart"], "items": {"type": "string"}}, "type": {"type": "string", "enum": ["fan", "motor", "virtual", "pc", "edge"]}}}, "deviceWorkStatus": {"type": "object", "required": ["healthStatus", "timestamp", "workMode"], "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}, "frameInfos": {"type": "array", "items": {"type": "object", "required": ["frameMeta", "frameLibs"], "properties": {"frameLibs": {"type": "object", "properties": {"modbusInfo": {"type": "object", "required": ["tid", "pid", "len", "uid", "fc"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "fc": {"type": "string"}, "len": {"type": "string"}, "pid": {"type": "string"}, "tid": {"type": "string"}, "uid": {"type": "string"}}}, "uartInfo": {"type": "object", "required": ["header", "addr", "cmd", "tail"], "properties": {"addr": {"type": "string"}, "cmd": {"type": "string"}, "datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "tail": {"type": "string"}}}, "udpInfo": {"type": "object", "required": ["type", "header", "typeID"], "properties": {"datas": {"type": "array", "items": {"type": "object", "required": ["index", "name", "type", "unit"], "properties": {"desc": {"type": "string"}, "index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}}}}, "header": {"type": "string"}, "type": {"type": "string"}, "typeID": {"type": "string"}}}}}, "frameMeta": {"type": "object", "required": ["frameType", "frameName"], "properties": {"frameName": {"type": "string"}, "frameType": {"type": "string", "enum": ["modbus", "uart", "udp"]}, "frameUID": {"type": "string"}, "framedescription": {"type": "string"}}}}}}}}}}}}}, "x-date": "2025-08-01 10:47:18", "x-description": "This is a goctl generated swagger file.", "x-github": "https://github.com/zeromicro/go-zero", "x-go-zero-doc": "https://go-zero.dev/", "x-goctl-version": "1.8.5"}