package logic

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"GCF/app/Devicemanager/internal/ent"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type GetFrameInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetFrameInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFrameInfoLogic {
	return &GetFrameInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// allowedProtocols 白名单，按需扩展
var allowedProtocols = map[string]struct{}{
	"modbus": {},
	"uart":   {},
	"udp":    {},
}

func (l *GetFrameInfoLogic) GetFrameInfo(req *types.GetFrameInfoRequest) (*types.GetFrameInfoResponse, error) {
	// 1. 确认设备存在
	if _, err := l.svcCtx.Db.Device.Query().
		Where(device.IDEQ(req.DeviceUID)).
		First(l.ctx); err != nil {
		if ent.IsNotFound(err) {
			return nil, xerrors.New(http.StatusNotFound, fmt.Sprintf("设备 %s 未找到", req.DeviceUID))
		}
		return nil, xerrors.New(http.StatusServiceUnavailable, fmt.Sprintf("查询设备 %s 失败: %v", req.DeviceUID, err))
	}

	// 2. 组装协议列表
	protocols := make([]string, 0, len(req.FrameMetas)) // 最终要查询的协议切片

	// ── 需求1：前端没传任何协议 → 用数据库里该设备记录的 protocol 字段
	if len(req.FrameMetas) == 0 {
		// 取出 device 记录（忽略错误，前面已验证存在）
		dev, _ := l.svcCtx.Db.Device.Get(l.ctx, req.DeviceUID)
		// 按逗号拆分成切片，如 "modbus,uart" → ["modbus","uart"]
		rawProtocol := strings.Split(dev.Protocol, ",")
		// 过白名单
		for _, p := range rawProtocol {
			p = strings.TrimSpace(p)
			if _, ok := allowedProtocols[p]; ok && p != "" {
				protocols = append(protocols, p)
			}
		}
	} else {
		// ── 需求2：前端传了协议 → 只保留白名单内的合法值
		for _, fm := range req.FrameMetas {
			if _, ok := allowedProtocols[fm.FrameType]; ok { // 白名单过滤
				protocols = append(protocols, fm.FrameType)
			}
		}
	}

	// ── 补丁1：去掉空串、空格等无效元素
	for i := 0; i < len(protocols); {
		if strings.TrimSpace(protocols[i]) == "" {
			protocols = append(protocols[:i], protocols[i+1:]...) // 删除空元素
			continue
		}
		i++
	}

	// ── 补丁2：如果最终没有合法协议，直接返回业务错误
	if len(protocols) == 0 {
		return nil, xerrors.New(http.StatusBadRequest, fmt.Sprintf("设备 %s 无可用协议", req.DeviceUID))
	}

	// 2. 组装协议列表后
	l.Logger.Infof("GetFrameInfoLogic protocols=%v device=%s", protocols, req.DeviceUID)

	// 3. 按协议查帧
	_, allFrames, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, protocols, req.DeviceUID)
	if err != nil {
		return nil, xerrors.New(http.StatusServiceUnavailable, fmt.Sprintf("未能将帧信息载入设备 %s: %v", req.DeviceUID, err))
	}

	// 4. 若同时提供了 frameID，精确过滤；找不到就返回该协议全部
	var result []types.FrameInfo
	for _, frame := range allFrames {
		// 收集所有与当前 frame.FrameType 匹配的请求项
		for _, fm := range req.FrameMetas {
			if fm.FrameType == frame.FrameMeta.FrameType && fm.FrameUID != "" {
				// 有 ID 且命中
				if fm.FrameUID == frame.FrameMeta.FrameUID {
					result = append(result, frame)
				}
				// 若 ID 不匹配，继续循环
			}
		}
	}

	// // 5. 没有精确匹配到 ID → 返回该协议下全部
	// if len(result) == 0 && len(req.FrameMetas) > 0 {
	// 	// 只保留请求里出现的协议
	// 	reqProto := make(map[string]struct{})
	// 	for _, fm := range req.FrameMetas {
	// 		reqProto[fm.FrameType] = struct{}{}
	// 	}
	// 	for _, f := range allFrames {
	// 		if _, ok := reqProto[f.FrameMeta.FrameType]; ok {
	// 			result = append(result, f)
	// 		}
	// 	}
	// }
	// 5. 无 ID 或空结果 → 返回该协议全部
	if len(req.FrameMetas) == 0 || len(result) == 0 {
		reqProto := make(map[string]struct{})
		for _, fm := range req.FrameMetas {
			reqProto[fm.FrameType] = struct{}{}
		}
		for _, f := range allFrames {
			if len(req.FrameMetas) == 0 || reqProto[f.FrameMeta.FrameType] == struct{}{} {
				result = append(result, f)
			}
		}
	}

	// // 6. 如果仍是空 → 说明该协议下无帧
	// if len(result) == 0 {
	// 	return nil, xerrors.New(http.StatusNotFound,
	// 		fmt.Sprintf("No frames found for device %s with given criteria", req.DeviceUID))
	// }

	return &types.GetFrameInfoResponse{FrameInfos: result}, nil
}
