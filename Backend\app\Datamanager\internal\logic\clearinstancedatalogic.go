package logic

import (
	"context"
	"errors"
	"net/http"

	xerrors "github.com/zeromicro/x/errors"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ClearInstanceDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewClearInstanceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ClearInstanceDataLogic {
	return &ClearInstanceDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ClearInstanceDataLogic) ClearInstanceData(req *types.ClearInstanceDataRequest) (resp *types.ClearInstanceDataResponse, err error) {
	// todo: add your logic here and delete this line
	rpcLogic := NewDeleteInstanceDataLogic(l.ctx, l.svcCtx)

	rpcReq := &datamanager.DeleteInstanceDataRequest{
		Revision:     req.Revision,
		InstanceInfo: convertInstanceInfo(req.InstanceInfo),
	}
	rpcResp, err := rpcLogic.DeleteInstanceData(rpcReq)
	if err != nil {
		Msg := xerrors.New(http.StatusBadRequest, err.Error())
		return nil, errors.New(Msg.Error())
	}

	return &types.ClearInstanceDataResponse{
		Status:       "success",
		RowsAffected: rpcResp.RowsAffected,
	}, nil
}
