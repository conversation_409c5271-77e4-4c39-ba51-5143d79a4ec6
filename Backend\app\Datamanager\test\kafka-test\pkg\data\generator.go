package data

import (
	"kafka-producer/pkg/etcd"
	"math/rand"
	"time"
)

// DataGenerator 数据生成器
type DataGenerator struct {
	rand *rand.Rand
}

// NewDataGenerator 创建新的数据生成器
func NewDataGenerator() *DataGenerator {
	return &DataGenerator{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateDataForGroup 为指定的DataGroup生成模拟数据
func (g *DataGenerator) GenerateDataForGroup(dataGroup etcd.DataGroup) map[string]interface{} {
	data := make(map[string]interface{})

	for _, node := range dataGroup.DataNode {
		switch node.Name {
		case "current":
			// 电流数据 (A)
			data[node.Name] = g.rand.Float64()*10 + 5 // 5-15A
		case "voltage":
			// 电压数据 (V)
			data[node.Name] = g.rand.Float64()*50 + 200 // 200-250V
		case "speed":
			// 转速数据 (RPM)
			data[node.Name] = g.rand.Float64()*2000 + 1000 // 1000-3000RPM
		case "temp":
			// 温度数据 (℃)
			data[node.Name] = g.rand.Float64()*40 + 20 // 20-60℃
		default:
			// 默认生成随机数值
			data[node.Name] = g.rand.Float64() * 100
		}
	}

	return data
}
