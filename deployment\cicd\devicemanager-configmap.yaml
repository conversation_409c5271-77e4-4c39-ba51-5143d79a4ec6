apiVersion: v1
kind: ConfigMap
metadata:
  name: devicemanager-config
data:
  device.yaml: |
    API:
      Name: device-api
      Host: 0.0.0.0
      Port: 8888
    Db:
      Driver: postgres
      Source: ********************************************/devicemanager?sslmode=disable
    # Db:
    #   Driver: postgres
    #   Source: ********************************************/migration?sslmode=disable

    RPC:
      Name: device.rpc
      ListenOn: 0.0.0.0:8080
    # Etcd:
    #   Hosts:
    #   - 127.0.0.1:2379
    #   Key: device.rpc

    Log:
      ServiceName: devicemanager
      Mode: console  # 可以是file或console
      Level: info    # 日志级别：debug, info, error等
    ZEtcdConf:
      Endpoints:
      - etcd-svc.etcd.svc.cluster.local:12379
