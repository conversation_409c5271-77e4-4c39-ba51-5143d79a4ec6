package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFramesByDeviceIDLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetFramesByDeviceIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFramesByDeviceIDLogic {
	return &GetFramesByDeviceIDLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetFramesByDeviceIDLogic) GetFramesByDeviceID(in *devicemanager.GetFramesByDeviceIDRequest) (*devicemanager.GetFramesByDeviceIDResponse, error) {
	//TODO: 可以修改为检查缓存
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(in.DeviceId)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	DeviceResourceInfo, err := utils.GetDeviceResourceInfo(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	Protocols := DeviceResourceInfo.Protocol
	frameMap, err := utils.GetFrameInfos(l.ctx, l.svcCtx, Protocols, in.DeviceId)
	if err != nil {
		return nil, err
	}

	frameInfos := make([]*devicemanager.FrameInfo, 0, len(frameMap))
	for protocol, frameInfo := range frameMap {
		if protocol == utils.FrameTypeModbus {
			//TODO: 优化两次循环
			thedatas := make([]*devicemanager.DataDefs, 0, len(frameInfo.FrameLibs.ModbusInfo.Datas))
			for _, data := range frameInfo.FrameLibs.ModbusInfo.Datas {
				thedatas = append(thedatas, &devicemanager.DataDefs{
					Index: data.Index,
					Name:  data.Name,
					Type:  data.Type,
					Unit:  data.Unit,
					Value: data.Value,
					Desc:  data.Desc,
				})
			}
			frameInfos = append(frameInfos, &devicemanager.FrameInfo{
				FrameMeta: &devicemanager.FrameMeta{
					FrameUid:  frameInfo.FrameMeta.FrameUID,
					FrameType: frameInfo.FrameMeta.FrameType,
					FrameName: frameInfo.FrameMeta.FrameName,
				},
				FrameLib: &devicemanager.FrameLibs{
					ModbusInfo: &devicemanager.ModbusInfo{
						Tid:   frameInfo.FrameLibs.ModbusInfo.TID,
						Pid:   frameInfo.FrameLibs.ModbusInfo.PID,
						Len:   frameInfo.FrameLibs.ModbusInfo.Len,
						Uid:   frameInfo.FrameLibs.ModbusInfo.UID,
						Fc:    frameInfo.FrameLibs.ModbusInfo.FC,
						Datas: thedatas,
					},
				},
			})
		} else if protocol == utils.FrameTypeUart {
			//TODO: 优化两次循环
			thedatas := make([]*devicemanager.DataDefs, 0, len(frameInfo.FrameLibs.UartInfo.Datas))
			for _, data := range frameInfo.FrameLibs.UartInfo.Datas {
				thedatas = append(thedatas, &devicemanager.DataDefs{
					Index: data.Index,
					Name:  data.Name,
					Type:  data.Type,
					Unit:  data.Unit,
					Value: data.Value,
					Desc:  data.Desc,
				})
			}
			frameInfos = append(frameInfos, &devicemanager.FrameInfo{
				FrameMeta: &devicemanager.FrameMeta{
					FrameUid:  frameInfo.FrameMeta.FrameUID,
					FrameType: frameInfo.FrameMeta.FrameType,
					FrameName: frameInfo.FrameMeta.FrameName,
				},
				FrameLib: &devicemanager.FrameLibs{
					UartInfo: &devicemanager.UartInfo{
						Header: frameInfo.FrameLibs.UartInfo.Header,
						Addr:   frameInfo.FrameLibs.UartInfo.Addr,
						Cmd:    frameInfo.FrameLibs.UartInfo.Cmd,
						Tail:   frameInfo.FrameLibs.UartInfo.Tail,
						Datas:  thedatas,
					},
				},
			})
		} else if protocol == utils.FrameTypeUdp {
			//TODO: 优化两次循环
			thedatas := make([]*devicemanager.DataDefs, 0, len(frameInfo.FrameLibs.UdpInfo.Datas))
			for _, data := range frameInfo.FrameLibs.UdpInfo.Datas {
				thedatas = append(thedatas, &devicemanager.DataDefs{
					Index: data.Index,
					Name:  data.Name,
					Type:  data.Type,
					Unit:  data.Unit,
					Value: data.Value,
					Desc:  data.Desc,
				})
			}
			frameInfos = append(frameInfos, &devicemanager.FrameInfo{
				FrameMeta: &devicemanager.FrameMeta{
					FrameUid:  frameInfo.FrameMeta.FrameUID,
					FrameType: frameInfo.FrameMeta.FrameType,
					FrameName: frameInfo.FrameMeta.FrameName,
				},
				FrameLib: &devicemanager.FrameLibs{
					UdpInfo: &devicemanager.UdpInfo{
						Type:   frameInfo.FrameLibs.UdpInfo.Type,
						Header: frameInfo.FrameLibs.UdpInfo.Header,
						TypeId: frameInfo.FrameLibs.UdpInfo.TypeID,
						Datas:  thedatas,
					},
				},
			})
		}
	}
	return &devicemanager.GetFramesByDeviceIDResponse{
		Frames: frameInfos,
	}, nil
}
