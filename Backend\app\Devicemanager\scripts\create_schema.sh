#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

SERVICE_ROOT=$SCRIPT_DIR/..
BACKEND_ROOT=$SERVICE_ROOT/../..

cd "$SERVICE_ROOT"

if [ -z "${1:-}" ]; then
  echo "Usage: $0 <schema_name>"
  echo "Please provide the name of the schema as the first argument."
  echo "Example: $0 User"
  exit 1
fi

SCHEMA_NAME=$1 SCHEMA_DIRECTORY=internal/ent/schema \
"$BACKEND_ROOT/scripts/ent/create_schema.sh"
