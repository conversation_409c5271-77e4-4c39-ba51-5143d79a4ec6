// Code generated by ent, DO NOT EDIT.

package data

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldID, id))
}

// FrameID applies equality check predicate on the "frame_id" field. It's identical to FrameIDEQ.
func FrameID(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldFrameID, v))
}

// Index applies equality check predicate on the "index" field. It's identical to IndexEQ.
func Index(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldIndex, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldName, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldType, v))
}

// Unit applies equality check predicate on the "unit" field. It's identical to UnitEQ.
func Unit(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldUnit, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldDescription, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldUpdateUnix, v))
}

// FrameIDEQ applies the EQ predicate on the "frame_id" field.
func FrameIDEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldFrameID, v))
}

// FrameIDNEQ applies the NEQ predicate on the "frame_id" field.
func FrameIDNEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldFrameID, v))
}

// FrameIDIn applies the In predicate on the "frame_id" field.
func FrameIDIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldFrameID, vs...))
}

// FrameIDNotIn applies the NotIn predicate on the "frame_id" field.
func FrameIDNotIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldFrameID, vs...))
}

// FrameIDGT applies the GT predicate on the "frame_id" field.
func FrameIDGT(v string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldFrameID, v))
}

// FrameIDGTE applies the GTE predicate on the "frame_id" field.
func FrameIDGTE(v string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldFrameID, v))
}

// FrameIDLT applies the LT predicate on the "frame_id" field.
func FrameIDLT(v string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldFrameID, v))
}

// FrameIDLTE applies the LTE predicate on the "frame_id" field.
func FrameIDLTE(v string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldFrameID, v))
}

// FrameIDContains applies the Contains predicate on the "frame_id" field.
func FrameIDContains(v string) predicate.Data {
	return predicate.Data(sql.FieldContains(FieldFrameID, v))
}

// FrameIDHasPrefix applies the HasPrefix predicate on the "frame_id" field.
func FrameIDHasPrefix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasPrefix(FieldFrameID, v))
}

// FrameIDHasSuffix applies the HasSuffix predicate on the "frame_id" field.
func FrameIDHasSuffix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasSuffix(FieldFrameID, v))
}

// FrameIDEqualFold applies the EqualFold predicate on the "frame_id" field.
func FrameIDEqualFold(v string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldFrameID, v))
}

// FrameIDContainsFold applies the ContainsFold predicate on the "frame_id" field.
func FrameIDContainsFold(v string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldFrameID, v))
}

// IndexEQ applies the EQ predicate on the "index" field.
func IndexEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldIndex, v))
}

// IndexNEQ applies the NEQ predicate on the "index" field.
func IndexNEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldIndex, v))
}

// IndexIn applies the In predicate on the "index" field.
func IndexIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldIndex, vs...))
}

// IndexNotIn applies the NotIn predicate on the "index" field.
func IndexNotIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldIndex, vs...))
}

// IndexGT applies the GT predicate on the "index" field.
func IndexGT(v string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldIndex, v))
}

// IndexGTE applies the GTE predicate on the "index" field.
func IndexGTE(v string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldIndex, v))
}

// IndexLT applies the LT predicate on the "index" field.
func IndexLT(v string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldIndex, v))
}

// IndexLTE applies the LTE predicate on the "index" field.
func IndexLTE(v string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldIndex, v))
}

// IndexContains applies the Contains predicate on the "index" field.
func IndexContains(v string) predicate.Data {
	return predicate.Data(sql.FieldContains(FieldIndex, v))
}

// IndexHasPrefix applies the HasPrefix predicate on the "index" field.
func IndexHasPrefix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasPrefix(FieldIndex, v))
}

// IndexHasSuffix applies the HasSuffix predicate on the "index" field.
func IndexHasSuffix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasSuffix(FieldIndex, v))
}

// IndexEqualFold applies the EqualFold predicate on the "index" field.
func IndexEqualFold(v string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldIndex, v))
}

// IndexContainsFold applies the ContainsFold predicate on the "index" field.
func IndexContainsFold(v string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldIndex, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Data {
	return predicate.Data(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldName, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Data {
	return predicate.Data(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldType, v))
}

// UnitEQ applies the EQ predicate on the "unit" field.
func UnitEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldUnit, v))
}

// UnitNEQ applies the NEQ predicate on the "unit" field.
func UnitNEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldUnit, v))
}

// UnitIn applies the In predicate on the "unit" field.
func UnitIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldUnit, vs...))
}

// UnitNotIn applies the NotIn predicate on the "unit" field.
func UnitNotIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldUnit, vs...))
}

// UnitGT applies the GT predicate on the "unit" field.
func UnitGT(v string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldUnit, v))
}

// UnitGTE applies the GTE predicate on the "unit" field.
func UnitGTE(v string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldUnit, v))
}

// UnitLT applies the LT predicate on the "unit" field.
func UnitLT(v string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldUnit, v))
}

// UnitLTE applies the LTE predicate on the "unit" field.
func UnitLTE(v string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldUnit, v))
}

// UnitContains applies the Contains predicate on the "unit" field.
func UnitContains(v string) predicate.Data {
	return predicate.Data(sql.FieldContains(FieldUnit, v))
}

// UnitHasPrefix applies the HasPrefix predicate on the "unit" field.
func UnitHasPrefix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasPrefix(FieldUnit, v))
}

// UnitHasSuffix applies the HasSuffix predicate on the "unit" field.
func UnitHasSuffix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasSuffix(FieldUnit, v))
}

// UnitEqualFold applies the EqualFold predicate on the "unit" field.
func UnitEqualFold(v string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldUnit, v))
}

// UnitContainsFold applies the ContainsFold predicate on the "unit" field.
func UnitContainsFold(v string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldUnit, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.Data {
	return predicate.Data(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.Data {
	return predicate.Data(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.Data {
	return predicate.Data(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.Data {
	return predicate.Data(sql.FieldContainsFold(FieldDescription, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.Data {
	return predicate.Data(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.Data {
	return predicate.Data(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.Data {
	return predicate.Data(sql.FieldLTE(FieldUpdateUnix, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Data) predicate.Data {
	return predicate.Data(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Data) predicate.Data {
	return predicate.Data(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Data) predicate.Data {
	return predicate.Data(sql.NotPredicates(p))
}
