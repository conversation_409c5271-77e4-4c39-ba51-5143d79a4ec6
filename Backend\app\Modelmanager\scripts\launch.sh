#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

########################################################################
# This script runs `go run service` with required environment variables.
#
# No building BUILD.bazel, or docker image.
# It's for quick and dirty manual tests.
########################################################################

cd "$SCRIPT_DIR"/..

export TZ=Asia/Singapore
export MODE=dev

export JWT_SECRET_YAML_FILE=../../deploy/terraform/aws/foundations/dev_secrets/jwt.yaml
export REDIS=supporting-service-redis-master.default:6379
export USER_STORAGE_ENDPOINT=userstorage-rpc.default:80
export USER_AUTHENTICATOR_ENDPOINT=userauthenticator-rpc.default:80
export MODEL_MANAGER_ENDPOINT=modelmanager-rpc.default:80
export TEMPLATE_ENDPOINT=localhost:8080
export MAIL_HOST=localhost
export MAIL_PORT=9105
export TemplateDirectory=../cmd/api/etc/templates

export USE_IN_CLUSTER_KUBECONFIG=false
export MYSQL_ACCOUNT=root:luchen2022
export MYSQL=localhost:3306
export MYSQL_DB=joborchestrator

export KUBECONFIG_PATH=$HOME/.kube/config

export JAEGER_ENDPOINT=http://localhost:9109/api/traces

echo "## Start API service."
echo "## Press Ctrl-C to kill them."
echo "## Log goes to ./api.log , remember: rm *.log"

go run storage.go -f etc/storage.yaml
