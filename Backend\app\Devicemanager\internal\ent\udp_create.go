// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UDPCreate is the builder for creating a Udp entity.
type UDPCreate struct {
	config
	mutation *UDPMutation
	hooks    []Hook
}

// SetDeviceID sets the "device_id" field.
func (uc *UDPCreate) SetDeviceID(s string) *UDPCreate {
	uc.mutation.SetDeviceID(s)
	return uc
}

// SetType sets the "type" field.
func (uc *UDPCreate) SetType(s string) *UDPCreate {
	uc.mutation.SetType(s)
	return uc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (uc *UDPCreate) SetNillableType(s *string) *UDPCreate {
	if s != nil {
		uc.SetType(*s)
	}
	return uc
}

// SetHeader sets the "header" field.
func (uc *UDPCreate) SetHeader(s string) *UDPCreate {
	uc.mutation.SetHeader(s)
	return uc
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (uc *UDPCreate) SetNillableHeader(s *string) *UDPCreate {
	if s != nil {
		uc.SetHeader(*s)
	}
	return uc
}

// SetTypeID sets the "type_id" field.
func (uc *UDPCreate) SetTypeID(s string) *UDPCreate {
	uc.mutation.SetTypeID(s)
	return uc
}

// SetNillableTypeID sets the "type_id" field if the given value is not nil.
func (uc *UDPCreate) SetNillableTypeID(s *string) *UDPCreate {
	if s != nil {
		uc.SetTypeID(*s)
	}
	return uc
}

// SetDescription sets the "description" field.
func (uc *UDPCreate) SetDescription(s string) *UDPCreate {
	uc.mutation.SetDescription(s)
	return uc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (uc *UDPCreate) SetNillableDescription(s *string) *UDPCreate {
	if s != nil {
		uc.SetDescription(*s)
	}
	return uc
}

// SetName sets the "name" field.
func (uc *UDPCreate) SetName(s string) *UDPCreate {
	uc.mutation.SetName(s)
	return uc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (uc *UDPCreate) SetNillableName(s *string) *UDPCreate {
	if s != nil {
		uc.SetName(*s)
	}
	return uc
}

// SetCreateUnix sets the "create_unix" field.
func (uc *UDPCreate) SetCreateUnix(t time.Time) *UDPCreate {
	uc.mutation.SetCreateUnix(t)
	return uc
}

// SetNillableCreateUnix sets the "create_unix" field if the given value is not nil.
func (uc *UDPCreate) SetNillableCreateUnix(t *time.Time) *UDPCreate {
	if t != nil {
		uc.SetCreateUnix(*t)
	}
	return uc
}

// SetUpdateUnix sets the "update_unix" field.
func (uc *UDPCreate) SetUpdateUnix(t time.Time) *UDPCreate {
	uc.mutation.SetUpdateUnix(t)
	return uc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (uc *UDPCreate) SetNillableUpdateUnix(t *time.Time) *UDPCreate {
	if t != nil {
		uc.SetUpdateUnix(*t)
	}
	return uc
}

// SetID sets the "id" field.
func (uc *UDPCreate) SetID(s string) *UDPCreate {
	uc.mutation.SetID(s)
	return uc
}

// SetDevice sets the "device" edge to the Device entity.
func (uc *UDPCreate) SetDevice(d *Device) *UDPCreate {
	return uc.SetDeviceID(d.ID)
}

// Mutation returns the UDPMutation object of the builder.
func (uc *UDPCreate) Mutation() *UDPMutation {
	return uc.mutation
}

// Save creates the Udp in the database.
func (uc *UDPCreate) Save(ctx context.Context) (*Udp, error) {
	uc.defaults()
	return withHooks(ctx, uc.sqlSave, uc.mutation, uc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uc *UDPCreate) SaveX(ctx context.Context) *Udp {
	v, err := uc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uc *UDPCreate) Exec(ctx context.Context) error {
	_, err := uc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uc *UDPCreate) ExecX(ctx context.Context) {
	if err := uc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uc *UDPCreate) defaults() {
	if _, ok := uc.mutation.GetType(); !ok {
		v := udp.DefaultType
		uc.mutation.SetType(v)
	}
	if _, ok := uc.mutation.Header(); !ok {
		v := udp.DefaultHeader
		uc.mutation.SetHeader(v)
	}
	if _, ok := uc.mutation.TypeID(); !ok {
		v := udp.DefaultTypeID
		uc.mutation.SetTypeID(v)
	}
	if _, ok := uc.mutation.Description(); !ok {
		v := udp.DefaultDescription
		uc.mutation.SetDescription(v)
	}
	if _, ok := uc.mutation.Name(); !ok {
		v := udp.DefaultName
		uc.mutation.SetName(v)
	}
	if _, ok := uc.mutation.CreateUnix(); !ok {
		v := udp.DefaultCreateUnix()
		uc.mutation.SetCreateUnix(v)
	}
	if _, ok := uc.mutation.UpdateUnix(); !ok {
		v := udp.DefaultUpdateUnix()
		uc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uc *UDPCreate) check() error {
	if _, ok := uc.mutation.DeviceID(); !ok {
		return &ValidationError{Name: "device_id", err: errors.New(`ent: missing required field "Udp.device_id"`)}
	}
	if v, ok := uc.mutation.DeviceID(); ok {
		if err := udp.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Udp.device_id": %w`, err)}
		}
	}
	if _, ok := uc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Udp.type"`)}
	}
	if _, ok := uc.mutation.Header(); !ok {
		return &ValidationError{Name: "header", err: errors.New(`ent: missing required field "Udp.header"`)}
	}
	if _, ok := uc.mutation.TypeID(); !ok {
		return &ValidationError{Name: "type_id", err: errors.New(`ent: missing required field "Udp.type_id"`)}
	}
	if _, ok := uc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "Udp.description"`)}
	}
	if _, ok := uc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Udp.name"`)}
	}
	if _, ok := uc.mutation.CreateUnix(); !ok {
		return &ValidationError{Name: "create_unix", err: errors.New(`ent: missing required field "Udp.create_unix"`)}
	}
	if _, ok := uc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "Udp.update_unix"`)}
	}
	if v, ok := uc.mutation.ID(); ok {
		if err := udp.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Udp.id": %w`, err)}
		}
	}
	if len(uc.mutation.DeviceIDs()) == 0 {
		return &ValidationError{Name: "device", err: errors.New(`ent: missing required edge "Udp.device"`)}
	}
	return nil
}

func (uc *UDPCreate) sqlSave(ctx context.Context) (*Udp, error) {
	if err := uc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Udp.ID type: %T", _spec.ID.Value)
		}
	}
	uc.mutation.id = &_node.ID
	uc.mutation.done = true
	return _node, nil
}

func (uc *UDPCreate) createSpec() (*Udp, *sqlgraph.CreateSpec) {
	var (
		_node = &Udp{config: uc.config}
		_spec = sqlgraph.NewCreateSpec(udp.Table, sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString))
	)
	if id, ok := uc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := uc.mutation.GetType(); ok {
		_spec.SetField(udp.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := uc.mutation.Header(); ok {
		_spec.SetField(udp.FieldHeader, field.TypeString, value)
		_node.Header = value
	}
	if value, ok := uc.mutation.TypeID(); ok {
		_spec.SetField(udp.FieldTypeID, field.TypeString, value)
		_node.TypeID = value
	}
	if value, ok := uc.mutation.Description(); ok {
		_spec.SetField(udp.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := uc.mutation.Name(); ok {
		_spec.SetField(udp.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := uc.mutation.CreateUnix(); ok {
		_spec.SetField(udp.FieldCreateUnix, field.TypeTime, value)
		_node.CreateUnix = value
	}
	if value, ok := uc.mutation.UpdateUnix(); ok {
		_spec.SetField(udp.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	if nodes := uc.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   udp.DeviceTable,
			Columns: []string{udp.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.DeviceID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// UDPCreateBulk is the builder for creating many Udp entities in bulk.
type UDPCreateBulk struct {
	config
	err      error
	builders []*UDPCreate
}

// Save creates the Udp entities in the database.
func (ucb *UDPCreateBulk) Save(ctx context.Context) ([]*Udp, error) {
	if ucb.err != nil {
		return nil, ucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ucb.builders))
	nodes := make([]*Udp, len(ucb.builders))
	mutators := make([]Mutator, len(ucb.builders))
	for i := range ucb.builders {
		func(i int, root context.Context) {
			builder := ucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UDPMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ucb *UDPCreateBulk) SaveX(ctx context.Context) []*Udp {
	v, err := ucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucb *UDPCreateBulk) Exec(ctx context.Context) error {
	_, err := ucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucb *UDPCreateBulk) ExecX(ctx context.Context) {
	if err := ucb.Exec(ctx); err != nil {
		panic(err)
	}
}
