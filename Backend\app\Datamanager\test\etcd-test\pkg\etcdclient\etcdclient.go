package etcdclient

import (
	"context"
	"encoding/json"
	"log"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
)

// EtcdClient wraps the etcd client.
// EtcdClient 封装了 etcd 客户端。
type EtcdClient struct {
	*clientv3.Client
}

// NewEtcdClient creates a new EtcdClient.
// NewEtcdClient 创建一个新的 EtcdClient。
func NewEtcdClient(endpoints []string, dialTimeout time.Duration) (*EtcdClient, error) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   endpoints,
		DialTimeout: dialTimeout,
	})
	if err != nil {
		return nil, err
	}
	return &EtcdClient{Client: cli}, nil
}

// StoreData marshals and puts data into etcd.
// StoreData 序列化数据并将其放入 etcd。
func (c *EtcdClient) StoreData(ctx context.Context, key string, data interface{}) error {
	value, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	_, err = c.Put(ctx, key, string(value))
	if err != nil {
		return err
	}
	log.Printf("Successfully put data to etcd with key: %s\nValue: %s", key, string(value))
	return nil
}

// GetData retrieves data from etcd.
// GetData 从 etcd 检索数据。
func (c *EtcdClient) GetData(ctx context.Context, key string, opts ...clientv3.OpOption) (*clientv3.GetResponse, error) {
	return c.Get(ctx, key, opts...)
}