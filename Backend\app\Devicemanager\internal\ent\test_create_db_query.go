// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/test_create_db"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCreateDbQuery is the builder for querying Test_create_db entities.
type TestCreateDbQuery struct {
	config
	ctx        *QueryContext
	order      []test_create_db.OrderOption
	inters     []Interceptor
	predicates []predicate.Test_create_db
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the TestCreateDbQuery builder.
func (tcdq *TestCreateDbQuery) Where(ps ...predicate.Test_create_db) *TestCreateDbQuery {
	tcdq.predicates = append(tcdq.predicates, ps...)
	return tcdq
}

// Limit the number of records to be returned by this query.
func (tcdq *TestCreateDbQuery) Limit(limit int) *TestCreateDbQuery {
	tcdq.ctx.Limit = &limit
	return tcdq
}

// Offset to start from.
func (tcdq *TestCreateDbQuery) Offset(offset int) *TestCreateDbQuery {
	tcdq.ctx.Offset = &offset
	return tcdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (tcdq *TestCreateDbQuery) Unique(unique bool) *TestCreateDbQuery {
	tcdq.ctx.Unique = &unique
	return tcdq
}

// Order specifies how the records should be ordered.
func (tcdq *TestCreateDbQuery) Order(o ...test_create_db.OrderOption) *TestCreateDbQuery {
	tcdq.order = append(tcdq.order, o...)
	return tcdq
}

// First returns the first Test_create_db entity from the query.
// Returns a *NotFoundError when no Test_create_db was found.
func (tcdq *TestCreateDbQuery) First(ctx context.Context) (*Test_create_db, error) {
	nodes, err := tcdq.Limit(1).All(setContextOp(ctx, tcdq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{test_create_db.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) FirstX(ctx context.Context) *Test_create_db {
	node, err := tcdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Test_create_db ID from the query.
// Returns a *NotFoundError when no Test_create_db ID was found.
func (tcdq *TestCreateDbQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = tcdq.Limit(1).IDs(setContextOp(ctx, tcdq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{test_create_db.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) FirstIDX(ctx context.Context) int {
	id, err := tcdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Test_create_db entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Test_create_db entity is found.
// Returns a *NotFoundError when no Test_create_db entities are found.
func (tcdq *TestCreateDbQuery) Only(ctx context.Context) (*Test_create_db, error) {
	nodes, err := tcdq.Limit(2).All(setContextOp(ctx, tcdq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{test_create_db.Label}
	default:
		return nil, &NotSingularError{test_create_db.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) OnlyX(ctx context.Context) *Test_create_db {
	node, err := tcdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Test_create_db ID in the query.
// Returns a *NotSingularError when more than one Test_create_db ID is found.
// Returns a *NotFoundError when no entities are found.
func (tcdq *TestCreateDbQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = tcdq.Limit(2).IDs(setContextOp(ctx, tcdq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{test_create_db.Label}
	default:
		err = &NotSingularError{test_create_db.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) OnlyIDX(ctx context.Context) int {
	id, err := tcdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Test_create_dbs.
func (tcdq *TestCreateDbQuery) All(ctx context.Context) ([]*Test_create_db, error) {
	ctx = setContextOp(ctx, tcdq.ctx, ent.OpQueryAll)
	if err := tcdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Test_create_db, *TestCreateDbQuery]()
	return withInterceptors[[]*Test_create_db](ctx, tcdq, qr, tcdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) AllX(ctx context.Context) []*Test_create_db {
	nodes, err := tcdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Test_create_db IDs.
func (tcdq *TestCreateDbQuery) IDs(ctx context.Context) (ids []int, err error) {
	if tcdq.ctx.Unique == nil && tcdq.path != nil {
		tcdq.Unique(true)
	}
	ctx = setContextOp(ctx, tcdq.ctx, ent.OpQueryIDs)
	if err = tcdq.Select(test_create_db.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) IDsX(ctx context.Context) []int {
	ids, err := tcdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (tcdq *TestCreateDbQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, tcdq.ctx, ent.OpQueryCount)
	if err := tcdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, tcdq, querierCount[*TestCreateDbQuery](), tcdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) CountX(ctx context.Context) int {
	count, err := tcdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (tcdq *TestCreateDbQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, tcdq.ctx, ent.OpQueryExist)
	switch _, err := tcdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (tcdq *TestCreateDbQuery) ExistX(ctx context.Context) bool {
	exist, err := tcdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the TestCreateDbQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (tcdq *TestCreateDbQuery) Clone() *TestCreateDbQuery {
	if tcdq == nil {
		return nil
	}
	return &TestCreateDbQuery{
		config:     tcdq.config,
		ctx:        tcdq.ctx.Clone(),
		order:      append([]test_create_db.OrderOption{}, tcdq.order...),
		inters:     append([]Interceptor{}, tcdq.inters...),
		predicates: append([]predicate.Test_create_db{}, tcdq.predicates...),
		// clone intermediate query.
		sql:  tcdq.sql.Clone(),
		path: tcdq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
func (tcdq *TestCreateDbQuery) GroupBy(field string, fields ...string) *TestCreateDbGroupBy {
	tcdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &TestCreateDbGroupBy{build: tcdq}
	grbuild.flds = &tcdq.ctx.Fields
	grbuild.label = test_create_db.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
func (tcdq *TestCreateDbQuery) Select(fields ...string) *TestCreateDbSelect {
	tcdq.ctx.Fields = append(tcdq.ctx.Fields, fields...)
	sbuild := &TestCreateDbSelect{TestCreateDbQuery: tcdq}
	sbuild.label = test_create_db.Label
	sbuild.flds, sbuild.scan = &tcdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a TestCreateDbSelect configured with the given aggregations.
func (tcdq *TestCreateDbQuery) Aggregate(fns ...AggregateFunc) *TestCreateDbSelect {
	return tcdq.Select().Aggregate(fns...)
}

func (tcdq *TestCreateDbQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range tcdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, tcdq); err != nil {
				return err
			}
		}
	}
	for _, f := range tcdq.ctx.Fields {
		if !test_create_db.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if tcdq.path != nil {
		prev, err := tcdq.path(ctx)
		if err != nil {
			return err
		}
		tcdq.sql = prev
	}
	return nil
}

func (tcdq *TestCreateDbQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Test_create_db, error) {
	var (
		nodes = []*Test_create_db{}
		_spec = tcdq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Test_create_db).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Test_create_db{config: tcdq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, tcdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (tcdq *TestCreateDbQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := tcdq.querySpec()
	_spec.Node.Columns = tcdq.ctx.Fields
	if len(tcdq.ctx.Fields) > 0 {
		_spec.Unique = tcdq.ctx.Unique != nil && *tcdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, tcdq.driver, _spec)
}

func (tcdq *TestCreateDbQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(test_create_db.Table, test_create_db.Columns, sqlgraph.NewFieldSpec(test_create_db.FieldID, field.TypeInt))
	_spec.From = tcdq.sql
	if unique := tcdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if tcdq.path != nil {
		_spec.Unique = true
	}
	if fields := tcdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, test_create_db.FieldID)
		for i := range fields {
			if fields[i] != test_create_db.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := tcdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := tcdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := tcdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := tcdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (tcdq *TestCreateDbQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(tcdq.driver.Dialect())
	t1 := builder.Table(test_create_db.Table)
	columns := tcdq.ctx.Fields
	if len(columns) == 0 {
		columns = test_create_db.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if tcdq.sql != nil {
		selector = tcdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if tcdq.ctx.Unique != nil && *tcdq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range tcdq.predicates {
		p(selector)
	}
	for _, p := range tcdq.order {
		p(selector)
	}
	if offset := tcdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := tcdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// TestCreateDbGroupBy is the group-by builder for Test_create_db entities.
type TestCreateDbGroupBy struct {
	selector
	build *TestCreateDbQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (tcdgb *TestCreateDbGroupBy) Aggregate(fns ...AggregateFunc) *TestCreateDbGroupBy {
	tcdgb.fns = append(tcdgb.fns, fns...)
	return tcdgb
}

// Scan applies the selector query and scans the result into the given value.
func (tcdgb *TestCreateDbGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, tcdgb.build.ctx, ent.OpQueryGroupBy)
	if err := tcdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TestCreateDbQuery, *TestCreateDbGroupBy](ctx, tcdgb.build, tcdgb, tcdgb.build.inters, v)
}

func (tcdgb *TestCreateDbGroupBy) sqlScan(ctx context.Context, root *TestCreateDbQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(tcdgb.fns))
	for _, fn := range tcdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*tcdgb.flds)+len(tcdgb.fns))
		for _, f := range *tcdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*tcdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := tcdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// TestCreateDbSelect is the builder for selecting fields of TestCreateDb entities.
type TestCreateDbSelect struct {
	*TestCreateDbQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (tcds *TestCreateDbSelect) Aggregate(fns ...AggregateFunc) *TestCreateDbSelect {
	tcds.fns = append(tcds.fns, fns...)
	return tcds
}

// Scan applies the selector query and scans the result into the given value.
func (tcds *TestCreateDbSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, tcds.ctx, ent.OpQuerySelect)
	if err := tcds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TestCreateDbQuery, *TestCreateDbSelect](ctx, tcds.TestCreateDbQuery, tcds, tcds.inters, v)
}

func (tcds *TestCreateDbSelect) sqlScan(ctx context.Context, root *TestCreateDbQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(tcds.fns))
	for _, fn := range tcds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*tcds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := tcds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
