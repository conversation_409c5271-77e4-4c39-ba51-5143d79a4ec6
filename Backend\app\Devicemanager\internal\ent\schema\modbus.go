package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Modbus holds the schema definition for the Modbus entity.
type Modbus struct {
	ent.Schema
}

// Fields of the Modbus.
func (Modbus) Fields() []ent.Field {
	return []ent.Field{
		field.String("device_id").
			Comment("Foreign key to Device").
			NotEmpty(),
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("Frame ID"),
		field.String("tid").
			Comment("Transaction Identifier").
			Default(""),
		field.String("pid").
			Comment("Protocol Identifier").
			Default(""),
		field.String("len").
			Comment("Length of the remaining frame").
			Default(""),
		field.String("uid").
			Comment("Unit Identifier").
			Default(""),
		field.String("fc").
			Comment("Function Code").
			Default(""),
		field.String("description").
			Comment("Description of the Modbus entity").
			Default(""),
		field.String("name").
			Comment("Name of the Modbus entity").
			Default(""),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("Update timestamp"),
	}
}

// Edges of the Modbus.
func (Modbus) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("device", Device.Type).Ref("modbusConfig").Field("device_id").Unique().Required(),
		//edge.To("data_points", Data.Type),
	}
}
