// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Modbus is the model entity for the Modbus schema.
type Modbus struct {
	config `json:"-"`
	// ID of the ent.
	// Frame ID
	ID string `json:"id,omitempty"`
	// Foreign key to Device
	DeviceID string `json:"device_id,omitempty"`
	// Transaction Identifier
	Tid string `json:"tid,omitempty"`
	// Protocol Identifier
	Pid string `json:"pid,omitempty"`
	// Length of the remaining frame
	Len string `json:"len,omitempty"`
	// Unit Identifier
	UID string `json:"uid,omitempty"`
	// Function Code
	Fc string `json:"fc,omitempty"`
	// Description of the Modbus entity
	Description string `json:"description,omitempty"`
	// Name of the Modbus entity
	Name string `json:"name,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// Update timestamp
	UpdateUnix time.Time `json:"update_unix,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ModbusQuery when eager-loading is set.
	Edges        ModbusEdges `json:"edges"`
	selectValues sql.SelectValues
}

// ModbusEdges holds the relations/edges for other nodes in the graph.
type ModbusEdges struct {
	// Device holds the value of the device edge.
	Device *Device `json:"device,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// DeviceOrErr returns the Device value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e ModbusEdges) DeviceOrErr() (*Device, error) {
	if e.Device != nil {
		return e.Device, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: device.Label}
	}
	return nil, &NotLoadedError{edge: "device"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Modbus) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case modbus.FieldID, modbus.FieldDeviceID, modbus.FieldTid, modbus.FieldPid, modbus.FieldLen, modbus.FieldUID, modbus.FieldFc, modbus.FieldDescription, modbus.FieldName:
			values[i] = new(sql.NullString)
		case modbus.FieldCreateUnix, modbus.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Modbus fields.
func (m *Modbus) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case modbus.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				m.ID = value.String
			}
		case modbus.FieldDeviceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_id", values[i])
			} else if value.Valid {
				m.DeviceID = value.String
			}
		case modbus.FieldTid:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tid", values[i])
			} else if value.Valid {
				m.Tid = value.String
			}
		case modbus.FieldPid:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pid", values[i])
			} else if value.Valid {
				m.Pid = value.String
			}
		case modbus.FieldLen:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field len", values[i])
			} else if value.Valid {
				m.Len = value.String
			}
		case modbus.FieldUID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field uid", values[i])
			} else if value.Valid {
				m.UID = value.String
			}
		case modbus.FieldFc:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fc", values[i])
			} else if value.Valid {
				m.Fc = value.String
			}
		case modbus.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				m.Description = value.String
			}
		case modbus.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				m.Name = value.String
			}
		case modbus.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				m.CreateUnix = value.Time
			}
		case modbus.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				m.UpdateUnix = value.Time
			}
		default:
			m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Modbus.
// This includes values selected through modifiers, order, etc.
func (m *Modbus) Value(name string) (ent.Value, error) {
	return m.selectValues.Get(name)
}

// QueryDevice queries the "device" edge of the Modbus entity.
func (m *Modbus) QueryDevice() *DeviceQuery {
	return NewModbusClient(m.config).QueryDevice(m)
}

// Update returns a builder for updating this Modbus.
// Note that you need to call Modbus.Unwrap() before calling this method if this Modbus
// was returned from a transaction, and the transaction was committed or rolled back.
func (m *Modbus) Update() *ModbusUpdateOne {
	return NewModbusClient(m.config).UpdateOne(m)
}

// Unwrap unwraps the Modbus entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (m *Modbus) Unwrap() *Modbus {
	_tx, ok := m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Modbus is not a transactional entity")
	}
	m.config.driver = _tx.drv
	return m
}

// String implements the fmt.Stringer.
func (m *Modbus) String() string {
	var builder strings.Builder
	builder.WriteString("Modbus(")
	builder.WriteString(fmt.Sprintf("id=%v, ", m.ID))
	builder.WriteString("device_id=")
	builder.WriteString(m.DeviceID)
	builder.WriteString(", ")
	builder.WriteString("tid=")
	builder.WriteString(m.Tid)
	builder.WriteString(", ")
	builder.WriteString("pid=")
	builder.WriteString(m.Pid)
	builder.WriteString(", ")
	builder.WriteString("len=")
	builder.WriteString(m.Len)
	builder.WriteString(", ")
	builder.WriteString("uid=")
	builder.WriteString(m.UID)
	builder.WriteString(", ")
	builder.WriteString("fc=")
	builder.WriteString(m.Fc)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(m.Description)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(m.Name)
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(m.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(m.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Modbuses is a parsable slice of Modbus.
type Modbuses []*Modbus
