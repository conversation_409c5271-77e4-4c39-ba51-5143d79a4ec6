# Makefile

# Variables
GO=go

PROJECT_VERSION := $(shell git describe --abbrev=8 --always --dirty)
ifneq ($(CI_COMMIT_TAG),)
PROJECT_VERSION := $(CI_COMMIT_TAG)
endif

# 定义所有子应用
APPS := Devicemanager
# 可以后续添加更多应用
# APPS += OtherApp

IMAGE_REPO=192.168.3.64:5888/generative-control-foundation-dev
IMAGE_TAG=$(PROJECT_VERSION)

.PHONY: all build docker-login docker-build docker-push version $(APPS)

# 默认目标
all: build

# Docker登录
docker-login:
	@docker login -u dev_usr -p Zanvis123 192.168.3.64:5888/generative-control-foundation-dev

# 构建所有应用
build: $(APPS)

# 为每个应用创建构建规则
$(APPS):
	$(GO) build -ldflags="-s -w" -o app/$@/$@ app/$@/$@.go

# Docker构建规则
docker-build-%: 
	docker build --no-cache --platform linux/amd64 \
		-t $(IMAGE_REPO)/$(shell echo $* | tr '[:upper:]' '[:lower:]'):$(IMAGE_TAG) \
		-f app/$*/Dockerfile .

docker-build: $(addprefix docker-build-,$(APPS))

# Docker推送规则
docker-push-%:
	docker push $(IMAGE_REPO)/$(shell echo $* | tr '[:upper:]' '[:lower:]'):$(IMAGE_TAG)
	docker tag $(IMAGE_REPO)/$(shell echo $* | tr '[:upper:]' '[:lower:]'):$(IMAGE_TAG) $(IMAGE_REPO)/$(shell echo $* | tr '[:upper:]' '[:lower:]'):latest
	docker push $(IMAGE_REPO)/$(shell echo $* | tr '[:upper:]' '[:lower:]'):latest

docker-push: $(addprefix docker-push-,$(APPS))

version:
	@echo $(PROJECT_VERSION)