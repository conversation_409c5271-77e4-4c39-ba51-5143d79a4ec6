// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/uart"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Uart is the model entity for the Uart schema.
type Uart struct {
	config `json:"-"`
	// ID of the ent.
	// Frame ID
	ID string `json:"id,omitempty"`
	// Foreign key to Device
	DeviceID string `json:"device_id,omitempty"`
	// Header of the frame
	Header string `json:"header,omitempty"`
	// Address of the frame
	Addr string `json:"addr,omitempty"`
	// Command of the frame
	Cmd string `json:"cmd,omitempty"`
	// Tail of the frame
	Tail string `json:"tail,omitempty"`
	// Description of the Uart entity
	Description string `json:"description,omitempty"`
	// Name of the Uart entity
	Name string `json:"name,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// Update timestamp
	UpdateUnix time.Time `json:"update_unix,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UartQuery when eager-loading is set.
	Edges        UartEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UartEdges holds the relations/edges for other nodes in the graph.
type UartEdges struct {
	// Device holds the value of the device edge.
	Device *Device `json:"device,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// DeviceOrErr returns the Device value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UartEdges) DeviceOrErr() (*Device, error) {
	if e.Device != nil {
		return e.Device, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: device.Label}
	}
	return nil, &NotLoadedError{edge: "device"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Uart) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case uart.FieldID, uart.FieldDeviceID, uart.FieldHeader, uart.FieldAddr, uart.FieldCmd, uart.FieldTail, uart.FieldDescription, uart.FieldName:
			values[i] = new(sql.NullString)
		case uart.FieldCreateUnix, uart.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Uart fields.
func (u *Uart) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case uart.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				u.ID = value.String
			}
		case uart.FieldDeviceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_id", values[i])
			} else if value.Valid {
				u.DeviceID = value.String
			}
		case uart.FieldHeader:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field header", values[i])
			} else if value.Valid {
				u.Header = value.String
			}
		case uart.FieldAddr:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field addr", values[i])
			} else if value.Valid {
				u.Addr = value.String
			}
		case uart.FieldCmd:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field cmd", values[i])
			} else if value.Valid {
				u.Cmd = value.String
			}
		case uart.FieldTail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tail", values[i])
			} else if value.Valid {
				u.Tail = value.String
			}
		case uart.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				u.Description = value.String
			}
		case uart.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				u.Name = value.String
			}
		case uart.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				u.CreateUnix = value.Time
			}
		case uart.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				u.UpdateUnix = value.Time
			}
		default:
			u.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Uart.
// This includes values selected through modifiers, order, etc.
func (u *Uart) Value(name string) (ent.Value, error) {
	return u.selectValues.Get(name)
}

// QueryDevice queries the "device" edge of the Uart entity.
func (u *Uart) QueryDevice() *DeviceQuery {
	return NewUartClient(u.config).QueryDevice(u)
}

// Update returns a builder for updating this Uart.
// Note that you need to call Uart.Unwrap() before calling this method if this Uart
// was returned from a transaction, and the transaction was committed or rolled back.
func (u *Uart) Update() *UartUpdateOne {
	return NewUartClient(u.config).UpdateOne(u)
}

// Unwrap unwraps the Uart entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (u *Uart) Unwrap() *Uart {
	_tx, ok := u.config.driver.(*txDriver)
	if !ok {
		panic("ent: Uart is not a transactional entity")
	}
	u.config.driver = _tx.drv
	return u
}

// String implements the fmt.Stringer.
func (u *Uart) String() string {
	var builder strings.Builder
	builder.WriteString("Uart(")
	builder.WriteString(fmt.Sprintf("id=%v, ", u.ID))
	builder.WriteString("device_id=")
	builder.WriteString(u.DeviceID)
	builder.WriteString(", ")
	builder.WriteString("header=")
	builder.WriteString(u.Header)
	builder.WriteString(", ")
	builder.WriteString("addr=")
	builder.WriteString(u.Addr)
	builder.WriteString(", ")
	builder.WriteString("cmd=")
	builder.WriteString(u.Cmd)
	builder.WriteString(", ")
	builder.WriteString("tail=")
	builder.WriteString(u.Tail)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(u.Description)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(u.Name)
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(u.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(u.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Uarts is a parsable slice of Uart.
type Uarts []*Uart
