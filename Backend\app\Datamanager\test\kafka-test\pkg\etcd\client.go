package etcd

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
)

// Client etcd客户端
type Client struct {
	cli *clientv3.Client
}

// NewClient 创建新的etcd客户端
func NewClient(endpoints []string) (*Client, error) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   endpoints,
		DialTimeout: 5 * time.Second,
	})
	if err != nil {
		return nil, err
	}

	return &Client{cli: cli}, nil
}

// GetProducerInfo 获取生产者信息
func (c *Client) GetProducerInfo(ctx context.Context, driverUID string) (*ProducerInfo, error) {
	key := fmt.Sprintf("/driver/%s/producer", driverUID)
	resp, err := c.cli.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	if len(resp.Kvs) == 0 {
		return nil, fmt.Errorf("producer info not found for driver %s", driverUID)
	}

	var producer ProducerInfo
	err = json.Unmarshal(resp.Kvs[0].Value, &producer)
	if err != nil {
		return nil, err
	}

	producer.Revision = resp.Kvs[0].ModRevision
	return &producer, nil
}

// GetInstances 获取实例信息
func (c *Client) GetInstances(ctx context.Context, driverUID string) ([]Instance, error) {
	key := fmt.Sprintf("/driver/%s/instances", driverUID)
	resp, err := c.cli.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	if len(resp.Kvs) == 0 {
		return nil, fmt.Errorf("instances not found for driver %s", driverUID)
	}

	var wrapper InstancesWrapper
	err = json.Unmarshal(resp.Kvs[0].Value, &wrapper)
	if err != nil {
		return nil, err
	}

	return wrapper.Instances, nil
}

// WatchProducer 监听生产者配置变化
func (c *Client) WatchProducer(ctx context.Context, driverUID string, callback func(*ProducerInfo)) {
	key := fmt.Sprintf("/driver/%s/producer", driverUID)
	watchChan := c.cli.Watch(ctx, key)

	for watchResp := range watchChan {
		for _, event := range watchResp.Events {
			if event.Type == clientv3.EventTypePut {
				var producer ProducerInfo
				err := json.Unmarshal(event.Kv.Value, &producer)
				if err != nil {
					log.Printf("Failed to unmarshal producer info: %v", err)
					continue
				}
				producer.Revision = event.Kv.ModRevision
				callback(&producer)
			}
		}
	}
}

// Close 关闭etcd客户端
func (c *Client) Close() error {
	return c.cli.Close()
}
