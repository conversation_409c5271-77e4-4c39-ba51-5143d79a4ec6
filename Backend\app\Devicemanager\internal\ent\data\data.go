// Code generated by ent, DO NOT EDIT.

package data

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the data type in the database.
	Label = "data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldFrameID holds the string denoting the frame_id field in the database.
	FieldFrameID = "frame_id"
	// FieldIndex holds the string denoting the index field in the database.
	FieldIndex = "index"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldUnit holds the string denoting the unit field in the database.
	FieldUnit = "unit"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldCreateUnix holds the string denoting the create_unix field in the database.
	FieldCreateUnix = "create_unix"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// Table holds the table name of the data in the database.
	Table = "data"
)

// Columns holds all SQL columns for data fields.
var Columns = []string{
	FieldID,
	FieldFrameID,
	FieldIndex,
	FieldName,
	FieldType,
	FieldUnit,
	FieldDescription,
	FieldCreateUnix,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// FrameIDValidator is a validator for the "frame_id" field. It is called by the builders before save.
	FrameIDValidator func(string) error
	// IndexValidator is a validator for the "index" field. It is called by the builders before save.
	IndexValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// DefaultUnit holds the default value on creation for the "unit" field.
	DefaultUnit string
	// DefaultDescription holds the default value on creation for the "description" field.
	DefaultDescription string
	// DefaultCreateUnix holds the default value on creation for the "create_unix" field.
	DefaultCreateUnix func() time.Time
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Data queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByFrameID orders the results by the frame_id field.
func ByFrameID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFrameID, opts...).ToFunc()
}

// ByIndex orders the results by the index field.
func ByIndex(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIndex, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByUnit orders the results by the unit field.
func ByUnit(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUnit, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByCreateUnix orders the results by the create_unix field.
func ByCreateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUnix, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}
