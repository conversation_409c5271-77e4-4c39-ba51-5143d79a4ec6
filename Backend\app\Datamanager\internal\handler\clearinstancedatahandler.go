package handler

import (
	"net/http"

	"GCF/app/Datamanager/internal/logic"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"

	xhttp "github.com/zeromicro/x/http"
)

func ClearInstanceDataHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ClearInstanceDataRequest
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
			return
		}

		l := logic.NewClearInstanceDataLogic(r.Context(), svcCtx)
		resp, err := l.ClearInstanceData(&req)
		if err != nil {
			xhttp.JsonBaseResponseCtx(r.Context(), w, err)
		} else {
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
