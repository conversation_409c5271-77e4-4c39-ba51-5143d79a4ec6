package main

import (
	"flag"

	"GCF/app/Modelmanager/internal/config"
	"GCF/app/Modelmanager/internal/handler"
	"GCF/app/Modelmanager/internal/modelmanager"
	"GCF/app/Modelmanager/internal/server"
	"GCF/app/Modelmanager/internal/svc"
	"GCF/pkg/servicex"

	"github.com/zeromicro/go-zero/rest"
	"google.golang.org/grpc"
)

func main() {
	flag.Parse()

	var c config.Config
	servicex.MustLoadConfigFile(&c)
	svcCtx := svc.NewServiceContext(c)

	servicex.Main(
		&c,
		func(server *rest.Server) {
			handler.RegisterHandlers(server, svcCtx)
		},
		func(grpcServer *grpc.Server) {
			modelmanager.RegisterDeviceServer(grpcServer, server.NewDeviceServer(svcCtx))
		},
	)
}
