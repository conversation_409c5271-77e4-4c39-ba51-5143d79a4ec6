-- create "data" table
CREATE TABLE "data" ("id" character varying NOT NULL, "frame_id" character varying NOT NULL, "index" character varying NOT NULL, "name" character varying NOT NULL, "type" character varying NOT NULL DEFAULT '', "unit" character varying NOT NULL DEFAULT '', "description" character varying NOT NULL DEFAULT '', "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, PRIMARY KEY ("id"));
-- create "test_create_dbs" table
CREATE TABLE "test_create_dbs" ("id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY, PRIMARY KEY ("id"));
-- create "devices" table
CREATE TABLE "devices" ("id" character varying NOT NULL, "name" character varying NOT NULL DEFAULT '', "ip" character varying NOT NULL, "type" character varying NOT NULL DEFAULT 'lower', "os" character varying NOT NULL DEFAULT '', "cpu" character varying NOT NULL DEFAULT '', "gpu" character varying NOT NULL DEFAULT '', "memory" character varying NOT NULL DEFAULT '', "disk" character varying NOT NULL DEFAULT '', "protocol" character varying NOT NULL DEFAULT '', "description" character varying NOT NULL DEFAULT '', "status" character varying NOT NULL DEFAULT 'unready', "healthtimestamp" timestamptz NOT NULL, "workmode" character varying NOT NULL DEFAULT 'centralized', "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, PRIMARY KEY ("id"));
-- create "modbuses" table
CREATE TABLE "modbuses" ("id" character varying NOT NULL, "tid" character varying NOT NULL DEFAULT '', "pid" character varying NOT NULL DEFAULT '', "len" character varying NOT NULL DEFAULT '', "uid" character varying NOT NULL DEFAULT '', "fc" character varying NOT NULL DEFAULT '', "description" character varying NOT NULL DEFAULT '', "name" character varying NOT NULL DEFAULT '', "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, "device_id" character varying NOT NULL, PRIMARY KEY ("id"), CONSTRAINT "modbuses_devices_modbusConfig" FOREIGN KEY ("device_id") REFERENCES "devices" ("id") ON DELETE NO ACTION);
-- create "uarts" table
CREATE TABLE "uarts" ("id" character varying NOT NULL, "header" character varying NOT NULL DEFAULT '', "addr" character varying NOT NULL DEFAULT '', "cmd" character varying NOT NULL DEFAULT '', "tail" character varying NOT NULL DEFAULT '', "description" character varying NOT NULL DEFAULT '', "name" character varying NOT NULL DEFAULT '', "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, "device_id" character varying NOT NULL, PRIMARY KEY ("id"), CONSTRAINT "uarts_devices_uartConfig" FOREIGN KEY ("device_id") REFERENCES "devices" ("id") ON DELETE NO ACTION);
-- create "udps" table
CREATE TABLE "udps" ("id" character varying NOT NULL, "type" character varying NOT NULL DEFAULT '', "header" character varying NOT NULL DEFAULT '', "type_id" character varying NOT NULL DEFAULT '', "description" character varying NOT NULL DEFAULT '', "name" character varying NOT NULL DEFAULT '', "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, "device_id" character varying NOT NULL, PRIMARY KEY ("id"), CONSTRAINT "udps_devices_udpConfig" FOREIGN KEY ("device_id") REFERENCES "devices" ("id") ON DELETE NO ACTION);
