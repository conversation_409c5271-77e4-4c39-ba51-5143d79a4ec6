package logic

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"GCF/app/Devicemanager/internal/ent"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type CreateDeviceFrameLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDeviceFrameLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDeviceFrameLogic {
	return &CreateDeviceFrameLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDeviceFrameLogic) CreateDeviceFrame(req *types.CreateDeviceFrameRequest) (resp *types.CreateDeviceFrameResponse, err error) {
	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备失败 : %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, xerrors.New(http.StatusForbidden, "创建设备帧事务启动失败")
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()
	// 获取设备元数据
	DeviceMeta := types.DeviceMeta{
		DeviceUID:   queryDevice.ID,
		Description: queryDevice.Description,
		DeviceName:  queryDevice.Name,
	}
	Protocols := strings.Split(queryDevice.Protocol, ",")
	//添加Frame记录
	for _, frameInfo := range req.FrameInfos {
		Protocols = utils.AddProtocol(Protocols, frameInfo.FrameMeta.FrameType)
		_, err = tx.Device.Update().
			SetProtocol(strings.Join(Protocols, ",")).
			Save(l.ctx)
		if err != nil {
			Msg := fmt.Errorf("更新设备数据失败: %s", err.Error())
			return nil, xerrors.New(http.StatusNotFound, Msg.Error())
		}
		FrameUID := uuid.New().String()
		FrameMeta := &types.FrameMeta{
			FrameType:        frameInfo.FrameMeta.FrameType,
			FrameUID:         FrameUID,
			FrameDescription: frameInfo.FrameMeta.FrameDescription,
			FrameName:        frameInfo.FrameMeta.FrameName,
		}
		//添加FrameMeta到DeviceMeta
		DeviceMeta.FrameMetas = append(DeviceMeta.FrameMetas, *FrameMeta)
		//根据协议修改或添加记录
		switch frameInfo.FrameMeta.FrameType {
		case utils.FrameTypeModbus:
			_, err = tx.Modbus.Create().
				SetID(FrameMeta.FrameUID).
				SetName(FrameMeta.FrameName).
				SetTid(frameInfo.FrameLibs.ModbusInfo.TID).
				SetPid(frameInfo.FrameLibs.ModbusInfo.PID).
				SetLen(frameInfo.FrameLibs.ModbusInfo.Len).
				SetUID(frameInfo.FrameLibs.ModbusInfo.UID).
				SetFc(frameInfo.FrameLibs.ModbusInfo.FC).
				SetDeviceID(queryDevice.ID).
				SetDescription(FrameMeta.FrameDescription).
				Save(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					Msg := fmt.Errorf("Modbus数据更改撤销失败: %s", err_rollback.Error())
					return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
				}
				Msg := fmt.Errorf("创建Modbus数据失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
			// 添加数据定义
			err = InsertData(l.ctx, tx, *FrameMeta, frameInfo.FrameLibs.ModbusInfo.Datas)
			if err != nil {
				Msg := fmt.Errorf("Modbus.Datas结构体创建失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}

		case utils.FrameTypeUart:
			_, err = tx.Uart.Create().
				SetID(FrameMeta.FrameUID).
				SetName(FrameMeta.FrameName).
				SetHeader(frameInfo.FrameLibs.UartInfo.Header).
				SetAddr(frameInfo.FrameLibs.UartInfo.Addr).
				SetCmd(frameInfo.FrameLibs.UartInfo.Cmd).
				SetTail(frameInfo.FrameLibs.UartInfo.Tail).
				SetDeviceID(queryDevice.ID).
				SetDescription(FrameMeta.FrameDescription).
				Save(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					Msg := fmt.Errorf("Uart数据更改撤销失败: %s", err_rollback.Error())
					return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
				}
				Msg := fmt.Errorf("创建Uart数据失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
			// 添加数据定义
			err = InsertData(l.ctx, tx, *FrameMeta, frameInfo.FrameLibs.UartInfo.Datas)
			if err != nil {
				Msg := fmt.Errorf("Uart.Datas结构体创建失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
		case utils.FrameTypeUdp:
			_, err = tx.Udp.Create().
				SetID(FrameMeta.FrameUID).
				SetName(FrameMeta.FrameName).
				SetType(frameInfo.FrameLibs.UdpInfo.Type).
				SetHeader(frameInfo.FrameLibs.UdpInfo.Header).
				SetTypeID(frameInfo.FrameLibs.UdpInfo.TypeID).
				SetDeviceID(queryDevice.ID).
				SetDescription(FrameMeta.FrameDescription).
				Save(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					Msg := fmt.Errorf("Udp数据更改撤销失败: %s", err_rollback.Error())
					return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
				}
				Msg := fmt.Errorf("创建Udp数据失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
			// 添加数据定义
			err = InsertData(l.ctx, tx, *FrameMeta, frameInfo.FrameLibs.UdpInfo.Datas)
			if err != nil {
				Msg := fmt.Errorf("Udp.Datas结构体创建失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
		}
	}
	//提交事务
	err = tx.Commit()
	if err != nil {
		Msg := fmt.Errorf("创建设备帧请求提交失败: %s", err.Error())
		return nil, xerrors.New(http.StatusBadRequest, Msg.Error())
	}

	// 添加到Etcd
	err = utils.Pub2Etcd(l.ctx, l.svcCtx)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "failed to publish to Etcd: "+err.Error())
	}

	//返回响应
	return &types.CreateDeviceFrameResponse{
		DeviceMeta:       DeviceMeta,
		DeviceWorkStatus: *DeviceWorkStatus,
	}, nil
}

func InsertData(ctx context.Context, tx *ent.Tx, FrameMeta types.FrameMeta, datas []types.DataDef) error {
	for _, dataDef := range datas {
		dataUID := uuid.New().String()
		create := tx.Data.Create().
			SetID(dataUID).
			SetName(FrameMeta.FrameName).
			SetFrameID(FrameMeta.FrameUID).
			SetIndex(dataDef.Index).
			SetName(dataDef.Name).
			SetType(dataDef.Type).
			SetUnit(dataDef.Unit).
			SetDescription(dataDef.Desc)

		_, err := create.Save(ctx)
		if err != nil {
			err_rollback := tx.Rollback()
			if err_rollback != nil {
				Msg := fmt.Errorf("数据更改撤销失败: %s", err_rollback.Error())
				return xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
			Msg := fmt.Errorf("Data数据结构体创建失败: %s", err.Error())
			return xerrors.New(http.StatusNotAcceptable, Msg.Error())
		}
	}
	return nil
}
