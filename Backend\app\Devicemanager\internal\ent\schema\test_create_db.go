package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Test_create_db holds the schema definition for the Test_create_db entity.
type Test_create_db struct {
	ent.Schema
}

// Fields of the Test_create_db.
func (Test_create_db) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			NotEmpty().
			Unique().
			Immutable(),

		field.String("name"),
		field.Time("create_at").
			Default(time.Now).
			Immutable(),
		field.Float32("price").Comment("unit price of the machine"),
	}
}

// Edges of the Test_create_db.
func (Test_create_db) Edges() []ent.Edge {
	return nil
}
