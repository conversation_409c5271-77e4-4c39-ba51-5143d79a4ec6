package logic

import (
	"context"
	"fmt"
	"net/http"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type FetchDeviceDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFetchDeviceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FetchDeviceDataLogic {
	return &FetchDeviceDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FetchDeviceDataLogic) FetchDeviceData(req *types.FetchDeviceDataRequest) (resp *types.FetchDeviceDataResponse, err error) {
	// 查询设备信息
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备失败 : %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	frameInfos, err := utils.FetchDeviceData()
	if err != nil {
		return nil, xerrors.New(http.StatusNotFound, "未查询到frameInfos")
	}
	resp = &types.FetchDeviceDataResponse{
		DeviceWorkStatus: *DeviceWorkStatus,
		FrameInfos:       frameInfos,
	}
	return resp, nil
}
