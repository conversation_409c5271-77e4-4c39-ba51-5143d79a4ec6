// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1
// Source: data.proto

package data

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	ChangeDataRequest          = datamanager.ChangeDataRequest
	ChangeDataResponse         = datamanager.ChangeDataResponse
	ChangeInstanceDataRequest  = datamanager.ChangeInstanceDataRequest
	ChangeInstanceDataResponse = datamanager.ChangeInstanceDataResponse
	ChangeProducerDataRequest  = datamanager.ChangeProducerDataRequest
	ChangeProducerDataResponse = datamanager.ChangeProducerDataResponse
	DataGroupTagInfo           = datamanager.DataGroupTagInfo
	DataRow                    = datamanager.DataRow
	DeleteInstanceDataRequest  = datamanager.DeleteInstanceDataRequest
	DeleteInstanceDataResponse = datamanager.DeleteInstanceDataResponse
	DeleteProducerDataRequest  = datamanager.DeleteProducerDataRequest
	DeleteProducerDataResponse = datamanager.DeleteProducerDataResponse
	FieldsInfo                 = datamanager.FieldsInfo
	GetInstanceDataRequest     = datamanager.GetInstanceDataRequest
	GetInstanceDataResponse    = datamanager.GetInstanceDataResponse
	GetProducerDataRequest     = datamanager.GetProducerDataRequest
	GetProducerDataResponse    = datamanager.GetProducerDataResponse
	InstanceTagInfo            = datamanager.InstanceTagInfo
	SingleData                 = datamanager.SingleData
	TableInfo                  = datamanager.TableInfo
	WhereInfo                  = datamanager.WhereInfo

	Data interface {
		GetProducerData(ctx context.Context, in *GetProducerDataRequest, opts ...grpc.CallOption) (*GetProducerDataResponse, error)
		DeleteProducerData(ctx context.Context, in *DeleteProducerDataRequest, opts ...grpc.CallOption) (*DeleteProducerDataResponse, error)
		ChangeProducerData(ctx context.Context, in *ChangeProducerDataRequest, opts ...grpc.CallOption) (*ChangeProducerDataResponse, error)
		GetInstanceData(ctx context.Context, in *GetInstanceDataRequest, opts ...grpc.CallOption) (*GetInstanceDataResponse, error)
		DeleteInstanceData(ctx context.Context, in *DeleteInstanceDataRequest, opts ...grpc.CallOption) (*DeleteInstanceDataResponse, error)
		ChangeInstanceData(ctx context.Context, in *ChangeInstanceDataRequest, opts ...grpc.CallOption) (*ChangeInstanceDataResponse, error)
		ChangeData(ctx context.Context, in *ChangeDataRequest, opts ...grpc.CallOption) (*ChangeDataResponse, error)
	}

	defaultData struct {
		cli zrpc.Client
	}
)

func NewData(cli zrpc.Client) Data {
	return &defaultData{
		cli: cli,
	}
}

func (m *defaultData) GetProducerData(ctx context.Context, in *GetProducerDataRequest, opts ...grpc.CallOption) (*GetProducerDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.GetProducerData(ctx, in, opts...)
}

func (m *defaultData) DeleteProducerData(ctx context.Context, in *DeleteProducerDataRequest, opts ...grpc.CallOption) (*DeleteProducerDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.DeleteProducerData(ctx, in, opts...)
}

func (m *defaultData) ChangeProducerData(ctx context.Context, in *ChangeProducerDataRequest, opts ...grpc.CallOption) (*ChangeProducerDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.ChangeProducerData(ctx, in, opts...)
}

func (m *defaultData) GetInstanceData(ctx context.Context, in *GetInstanceDataRequest, opts ...grpc.CallOption) (*GetInstanceDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.GetInstanceData(ctx, in, opts...)
}

func (m *defaultData) DeleteInstanceData(ctx context.Context, in *DeleteInstanceDataRequest, opts ...grpc.CallOption) (*DeleteInstanceDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.DeleteInstanceData(ctx, in, opts...)
}

func (m *defaultData) ChangeInstanceData(ctx context.Context, in *ChangeInstanceDataRequest, opts ...grpc.CallOption) (*ChangeInstanceDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.ChangeInstanceData(ctx, in, opts...)
}

func (m *defaultData) ChangeData(ctx context.Context, in *ChangeDataRequest, opts ...grpc.CallOption) (*ChangeDataResponse, error) {
	client := datamanager.NewDataClient(m.cli.Conn())
	return client.ChangeData(ctx, in, opts...)
}
