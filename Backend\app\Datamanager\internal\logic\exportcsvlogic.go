package logic

import (
	"context"
	"errors"
	"net/http"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"
	"GCF/app/Datamanager/internal/types"

	xerrors "github.com/zeromicro/x/errors"
	"github.com/zeromicro/go-zero/core/logx"
)

type ExportCSVLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewExportCSVLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportCSVLogic {
	return &ExportCSVLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportCSVLogic) ExportCSV(req *types.ExportCSVRequest) (resp *types.ExportCSVResponse, err error) {
	rpcLogic := NewGetInstanceDataLogic(l.ctx, l.svcCtx)

	rpcReq := &datamanager.GetInstanceDataRequest{
		InstanceInfo: convertInstanceInfo(req.InstanceInfo),
	}
	rpcResp, err := rpcLogic.GetInstanceData(rpcReq)
	if err != nil {
		Msg := xerrors.New(http.StatusBadRequest, "failed to fetch instance data")
		return nil, errors.New(Msg.Error())
	}
	
	dataRows := convertDataRows(rpcResp.DataRows)
	logx.Infof("dataRows: %v", dataRows)
	
	csvContent, err := convertDataRowsToCSV(dataRows)
	if err != nil {
		Msg := xerrors.New(http.StatusBadRequest, "failed to convert data to CSV")
		return nil, errors.New(Msg.Error())
	}

	resp = &types.ExportCSVResponse{
		FileName:    req.FileName,
		FileContent: csvContent,
	}
	return resp, nil
}