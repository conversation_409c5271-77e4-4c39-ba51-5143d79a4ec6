// Code generated by ent, DO NOT EDIT.

package model

import (
	"GCF/app/Modelmanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldID, id))
}

// ModelID applies equality check predicate on the "model_id" field. It's identical to ModelIDEQ.
func ModelID(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldModelID, v))
}

// ModelNameVersion applies equality check predicate on the "model_name_version" field. It's identical to ModelNameVersionEQ.
func ModelNameVersion(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldModelNameVersion, v))
}

// FrameworkVersion applies equality check predicate on the "framework_version" field. It's identical to FrameworkVersionEQ.
func FrameworkVersion(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldFrameworkVersion, v))
}

// TypeDesc applies equality check predicate on the "type_desc" field. It's identical to TypeDescEQ.
func TypeDesc(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldTypeDesc, v))
}

// StoragePath applies equality check predicate on the "storage_path" field. It's identical to StoragePathEQ.
func StoragePath(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldStoragePath, v))
}

// Accuracy applies equality check predicate on the "accuracy" field. It's identical to AccuracyEQ.
func Accuracy(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldAccuracy, v))
}

// Precision applies equality check predicate on the "precision" field. It's identical to PrecisionEQ.
func Precision(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldPrecision, v))
}

// Recall applies equality check predicate on the "recall" field. It's identical to RecallEQ.
func Recall(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldRecall, v))
}

// F1Score applies equality check predicate on the "f1_score" field. It's identical to F1ScoreEQ.
func F1Score(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldF1Score, v))
}

// CreatedBy applies equality check predicate on the "created_by" field. It's identical to CreatedByEQ.
func CreatedBy(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldCreatedBy, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldUpdateUnix, v))
}

// ModelIDEQ applies the EQ predicate on the "model_id" field.
func ModelIDEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldModelID, v))
}

// ModelIDNEQ applies the NEQ predicate on the "model_id" field.
func ModelIDNEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldModelID, v))
}

// ModelIDIn applies the In predicate on the "model_id" field.
func ModelIDIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldModelID, vs...))
}

// ModelIDNotIn applies the NotIn predicate on the "model_id" field.
func ModelIDNotIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldModelID, vs...))
}

// ModelIDGT applies the GT predicate on the "model_id" field.
func ModelIDGT(v string) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldModelID, v))
}

// ModelIDGTE applies the GTE predicate on the "model_id" field.
func ModelIDGTE(v string) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldModelID, v))
}

// ModelIDLT applies the LT predicate on the "model_id" field.
func ModelIDLT(v string) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldModelID, v))
}

// ModelIDLTE applies the LTE predicate on the "model_id" field.
func ModelIDLTE(v string) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldModelID, v))
}

// ModelIDContains applies the Contains predicate on the "model_id" field.
func ModelIDContains(v string) predicate.Model {
	return predicate.Model(sql.FieldContains(FieldModelID, v))
}

// ModelIDHasPrefix applies the HasPrefix predicate on the "model_id" field.
func ModelIDHasPrefix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasPrefix(FieldModelID, v))
}

// ModelIDHasSuffix applies the HasSuffix predicate on the "model_id" field.
func ModelIDHasSuffix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasSuffix(FieldModelID, v))
}

// ModelIDEqualFold applies the EqualFold predicate on the "model_id" field.
func ModelIDEqualFold(v string) predicate.Model {
	return predicate.Model(sql.FieldEqualFold(FieldModelID, v))
}

// ModelIDContainsFold applies the ContainsFold predicate on the "model_id" field.
func ModelIDContainsFold(v string) predicate.Model {
	return predicate.Model(sql.FieldContainsFold(FieldModelID, v))
}

// ModelNameVersionEQ applies the EQ predicate on the "model_name_version" field.
func ModelNameVersionEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldModelNameVersion, v))
}

// ModelNameVersionNEQ applies the NEQ predicate on the "model_name_version" field.
func ModelNameVersionNEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldModelNameVersion, v))
}

// ModelNameVersionIn applies the In predicate on the "model_name_version" field.
func ModelNameVersionIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldModelNameVersion, vs...))
}

// ModelNameVersionNotIn applies the NotIn predicate on the "model_name_version" field.
func ModelNameVersionNotIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldModelNameVersion, vs...))
}

// ModelNameVersionGT applies the GT predicate on the "model_name_version" field.
func ModelNameVersionGT(v string) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldModelNameVersion, v))
}

// ModelNameVersionGTE applies the GTE predicate on the "model_name_version" field.
func ModelNameVersionGTE(v string) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldModelNameVersion, v))
}

// ModelNameVersionLT applies the LT predicate on the "model_name_version" field.
func ModelNameVersionLT(v string) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldModelNameVersion, v))
}

// ModelNameVersionLTE applies the LTE predicate on the "model_name_version" field.
func ModelNameVersionLTE(v string) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldModelNameVersion, v))
}

// ModelNameVersionContains applies the Contains predicate on the "model_name_version" field.
func ModelNameVersionContains(v string) predicate.Model {
	return predicate.Model(sql.FieldContains(FieldModelNameVersion, v))
}

// ModelNameVersionHasPrefix applies the HasPrefix predicate on the "model_name_version" field.
func ModelNameVersionHasPrefix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasPrefix(FieldModelNameVersion, v))
}

// ModelNameVersionHasSuffix applies the HasSuffix predicate on the "model_name_version" field.
func ModelNameVersionHasSuffix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasSuffix(FieldModelNameVersion, v))
}

// ModelNameVersionEqualFold applies the EqualFold predicate on the "model_name_version" field.
func ModelNameVersionEqualFold(v string) predicate.Model {
	return predicate.Model(sql.FieldEqualFold(FieldModelNameVersion, v))
}

// ModelNameVersionContainsFold applies the ContainsFold predicate on the "model_name_version" field.
func ModelNameVersionContainsFold(v string) predicate.Model {
	return predicate.Model(sql.FieldContainsFold(FieldModelNameVersion, v))
}

// FrameworkVersionEQ applies the EQ predicate on the "framework_version" field.
func FrameworkVersionEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldFrameworkVersion, v))
}

// FrameworkVersionNEQ applies the NEQ predicate on the "framework_version" field.
func FrameworkVersionNEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldFrameworkVersion, v))
}

// FrameworkVersionIn applies the In predicate on the "framework_version" field.
func FrameworkVersionIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldFrameworkVersion, vs...))
}

// FrameworkVersionNotIn applies the NotIn predicate on the "framework_version" field.
func FrameworkVersionNotIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldFrameworkVersion, vs...))
}

// FrameworkVersionGT applies the GT predicate on the "framework_version" field.
func FrameworkVersionGT(v string) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldFrameworkVersion, v))
}

// FrameworkVersionGTE applies the GTE predicate on the "framework_version" field.
func FrameworkVersionGTE(v string) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldFrameworkVersion, v))
}

// FrameworkVersionLT applies the LT predicate on the "framework_version" field.
func FrameworkVersionLT(v string) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldFrameworkVersion, v))
}

// FrameworkVersionLTE applies the LTE predicate on the "framework_version" field.
func FrameworkVersionLTE(v string) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldFrameworkVersion, v))
}

// FrameworkVersionContains applies the Contains predicate on the "framework_version" field.
func FrameworkVersionContains(v string) predicate.Model {
	return predicate.Model(sql.FieldContains(FieldFrameworkVersion, v))
}

// FrameworkVersionHasPrefix applies the HasPrefix predicate on the "framework_version" field.
func FrameworkVersionHasPrefix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasPrefix(FieldFrameworkVersion, v))
}

// FrameworkVersionHasSuffix applies the HasSuffix predicate on the "framework_version" field.
func FrameworkVersionHasSuffix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasSuffix(FieldFrameworkVersion, v))
}

// FrameworkVersionEqualFold applies the EqualFold predicate on the "framework_version" field.
func FrameworkVersionEqualFold(v string) predicate.Model {
	return predicate.Model(sql.FieldEqualFold(FieldFrameworkVersion, v))
}

// FrameworkVersionContainsFold applies the ContainsFold predicate on the "framework_version" field.
func FrameworkVersionContainsFold(v string) predicate.Model {
	return predicate.Model(sql.FieldContainsFold(FieldFrameworkVersion, v))
}

// TypeDescEQ applies the EQ predicate on the "type_desc" field.
func TypeDescEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldTypeDesc, v))
}

// TypeDescNEQ applies the NEQ predicate on the "type_desc" field.
func TypeDescNEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldTypeDesc, v))
}

// TypeDescIn applies the In predicate on the "type_desc" field.
func TypeDescIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldTypeDesc, vs...))
}

// TypeDescNotIn applies the NotIn predicate on the "type_desc" field.
func TypeDescNotIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldTypeDesc, vs...))
}

// TypeDescGT applies the GT predicate on the "type_desc" field.
func TypeDescGT(v string) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldTypeDesc, v))
}

// TypeDescGTE applies the GTE predicate on the "type_desc" field.
func TypeDescGTE(v string) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldTypeDesc, v))
}

// TypeDescLT applies the LT predicate on the "type_desc" field.
func TypeDescLT(v string) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldTypeDesc, v))
}

// TypeDescLTE applies the LTE predicate on the "type_desc" field.
func TypeDescLTE(v string) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldTypeDesc, v))
}

// TypeDescContains applies the Contains predicate on the "type_desc" field.
func TypeDescContains(v string) predicate.Model {
	return predicate.Model(sql.FieldContains(FieldTypeDesc, v))
}

// TypeDescHasPrefix applies the HasPrefix predicate on the "type_desc" field.
func TypeDescHasPrefix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasPrefix(FieldTypeDesc, v))
}

// TypeDescHasSuffix applies the HasSuffix predicate on the "type_desc" field.
func TypeDescHasSuffix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasSuffix(FieldTypeDesc, v))
}

// TypeDescEqualFold applies the EqualFold predicate on the "type_desc" field.
func TypeDescEqualFold(v string) predicate.Model {
	return predicate.Model(sql.FieldEqualFold(FieldTypeDesc, v))
}

// TypeDescContainsFold applies the ContainsFold predicate on the "type_desc" field.
func TypeDescContainsFold(v string) predicate.Model {
	return predicate.Model(sql.FieldContainsFold(FieldTypeDesc, v))
}

// StoragePathEQ applies the EQ predicate on the "storage_path" field.
func StoragePathEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldStoragePath, v))
}

// StoragePathNEQ applies the NEQ predicate on the "storage_path" field.
func StoragePathNEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldStoragePath, v))
}

// StoragePathIn applies the In predicate on the "storage_path" field.
func StoragePathIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldStoragePath, vs...))
}

// StoragePathNotIn applies the NotIn predicate on the "storage_path" field.
func StoragePathNotIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldStoragePath, vs...))
}

// StoragePathGT applies the GT predicate on the "storage_path" field.
func StoragePathGT(v string) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldStoragePath, v))
}

// StoragePathGTE applies the GTE predicate on the "storage_path" field.
func StoragePathGTE(v string) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldStoragePath, v))
}

// StoragePathLT applies the LT predicate on the "storage_path" field.
func StoragePathLT(v string) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldStoragePath, v))
}

// StoragePathLTE applies the LTE predicate on the "storage_path" field.
func StoragePathLTE(v string) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldStoragePath, v))
}

// StoragePathContains applies the Contains predicate on the "storage_path" field.
func StoragePathContains(v string) predicate.Model {
	return predicate.Model(sql.FieldContains(FieldStoragePath, v))
}

// StoragePathHasPrefix applies the HasPrefix predicate on the "storage_path" field.
func StoragePathHasPrefix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasPrefix(FieldStoragePath, v))
}

// StoragePathHasSuffix applies the HasSuffix predicate on the "storage_path" field.
func StoragePathHasSuffix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasSuffix(FieldStoragePath, v))
}

// StoragePathEqualFold applies the EqualFold predicate on the "storage_path" field.
func StoragePathEqualFold(v string) predicate.Model {
	return predicate.Model(sql.FieldEqualFold(FieldStoragePath, v))
}

// StoragePathContainsFold applies the ContainsFold predicate on the "storage_path" field.
func StoragePathContainsFold(v string) predicate.Model {
	return predicate.Model(sql.FieldContainsFold(FieldStoragePath, v))
}

// AccuracyEQ applies the EQ predicate on the "accuracy" field.
func AccuracyEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldAccuracy, v))
}

// AccuracyNEQ applies the NEQ predicate on the "accuracy" field.
func AccuracyNEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldAccuracy, v))
}

// AccuracyIn applies the In predicate on the "accuracy" field.
func AccuracyIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldAccuracy, vs...))
}

// AccuracyNotIn applies the NotIn predicate on the "accuracy" field.
func AccuracyNotIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldAccuracy, vs...))
}

// AccuracyGT applies the GT predicate on the "accuracy" field.
func AccuracyGT(v float64) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldAccuracy, v))
}

// AccuracyGTE applies the GTE predicate on the "accuracy" field.
func AccuracyGTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldAccuracy, v))
}

// AccuracyLT applies the LT predicate on the "accuracy" field.
func AccuracyLT(v float64) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldAccuracy, v))
}

// AccuracyLTE applies the LTE predicate on the "accuracy" field.
func AccuracyLTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldAccuracy, v))
}

// PrecisionEQ applies the EQ predicate on the "precision" field.
func PrecisionEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldPrecision, v))
}

// PrecisionNEQ applies the NEQ predicate on the "precision" field.
func PrecisionNEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldPrecision, v))
}

// PrecisionIn applies the In predicate on the "precision" field.
func PrecisionIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldPrecision, vs...))
}

// PrecisionNotIn applies the NotIn predicate on the "precision" field.
func PrecisionNotIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldPrecision, vs...))
}

// PrecisionGT applies the GT predicate on the "precision" field.
func PrecisionGT(v float64) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldPrecision, v))
}

// PrecisionGTE applies the GTE predicate on the "precision" field.
func PrecisionGTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldPrecision, v))
}

// PrecisionLT applies the LT predicate on the "precision" field.
func PrecisionLT(v float64) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldPrecision, v))
}

// PrecisionLTE applies the LTE predicate on the "precision" field.
func PrecisionLTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldPrecision, v))
}

// RecallEQ applies the EQ predicate on the "recall" field.
func RecallEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldRecall, v))
}

// RecallNEQ applies the NEQ predicate on the "recall" field.
func RecallNEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldRecall, v))
}

// RecallIn applies the In predicate on the "recall" field.
func RecallIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldRecall, vs...))
}

// RecallNotIn applies the NotIn predicate on the "recall" field.
func RecallNotIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldRecall, vs...))
}

// RecallGT applies the GT predicate on the "recall" field.
func RecallGT(v float64) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldRecall, v))
}

// RecallGTE applies the GTE predicate on the "recall" field.
func RecallGTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldRecall, v))
}

// RecallLT applies the LT predicate on the "recall" field.
func RecallLT(v float64) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldRecall, v))
}

// RecallLTE applies the LTE predicate on the "recall" field.
func RecallLTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldRecall, v))
}

// F1ScoreEQ applies the EQ predicate on the "f1_score" field.
func F1ScoreEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldF1Score, v))
}

// F1ScoreNEQ applies the NEQ predicate on the "f1_score" field.
func F1ScoreNEQ(v float64) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldF1Score, v))
}

// F1ScoreIn applies the In predicate on the "f1_score" field.
func F1ScoreIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldF1Score, vs...))
}

// F1ScoreNotIn applies the NotIn predicate on the "f1_score" field.
func F1ScoreNotIn(vs ...float64) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldF1Score, vs...))
}

// F1ScoreGT applies the GT predicate on the "f1_score" field.
func F1ScoreGT(v float64) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldF1Score, v))
}

// F1ScoreGTE applies the GTE predicate on the "f1_score" field.
func F1ScoreGTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldF1Score, v))
}

// F1ScoreLT applies the LT predicate on the "f1_score" field.
func F1ScoreLT(v float64) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldF1Score, v))
}

// F1ScoreLTE applies the LTE predicate on the "f1_score" field.
func F1ScoreLTE(v float64) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldF1Score, v))
}

// CreatedByEQ applies the EQ predicate on the "created_by" field.
func CreatedByEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldCreatedBy, v))
}

// CreatedByNEQ applies the NEQ predicate on the "created_by" field.
func CreatedByNEQ(v string) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldCreatedBy, v))
}

// CreatedByIn applies the In predicate on the "created_by" field.
func CreatedByIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldCreatedBy, vs...))
}

// CreatedByNotIn applies the NotIn predicate on the "created_by" field.
func CreatedByNotIn(vs ...string) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldCreatedBy, vs...))
}

// CreatedByGT applies the GT predicate on the "created_by" field.
func CreatedByGT(v string) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldCreatedBy, v))
}

// CreatedByGTE applies the GTE predicate on the "created_by" field.
func CreatedByGTE(v string) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldCreatedBy, v))
}

// CreatedByLT applies the LT predicate on the "created_by" field.
func CreatedByLT(v string) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldCreatedBy, v))
}

// CreatedByLTE applies the LTE predicate on the "created_by" field.
func CreatedByLTE(v string) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldCreatedBy, v))
}

// CreatedByContains applies the Contains predicate on the "created_by" field.
func CreatedByContains(v string) predicate.Model {
	return predicate.Model(sql.FieldContains(FieldCreatedBy, v))
}

// CreatedByHasPrefix applies the HasPrefix predicate on the "created_by" field.
func CreatedByHasPrefix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasPrefix(FieldCreatedBy, v))
}

// CreatedByHasSuffix applies the HasSuffix predicate on the "created_by" field.
func CreatedByHasSuffix(v string) predicate.Model {
	return predicate.Model(sql.FieldHasSuffix(FieldCreatedBy, v))
}

// CreatedByEqualFold applies the EqualFold predicate on the "created_by" field.
func CreatedByEqualFold(v string) predicate.Model {
	return predicate.Model(sql.FieldEqualFold(FieldCreatedBy, v))
}

// CreatedByContainsFold applies the ContainsFold predicate on the "created_by" field.
func CreatedByContainsFold(v string) predicate.Model {
	return predicate.Model(sql.FieldContainsFold(FieldCreatedBy, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.Model {
	return predicate.Model(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.Model {
	return predicate.Model(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.Model {
	return predicate.Model(sql.FieldLTE(FieldUpdateUnix, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Model) predicate.Model {
	return predicate.Model(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Model) predicate.Model {
	return predicate.Model(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Model) predicate.Model {
	return predicate.Model(sql.NotPredicates(p))
}
