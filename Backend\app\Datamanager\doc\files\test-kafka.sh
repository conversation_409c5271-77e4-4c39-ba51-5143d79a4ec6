#!/bin/bash

echo "测试Kafka集群..."

# 创建测试topic
echo "1. 创建测试topic..."
sudo docker exec kafka1 kafka-topics --create --topic test-topic --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3

# 列出所有topics
echo "2. 列出所有topics..."
sudo docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092

# 查看topic详情
echo "3. 查看topic详情..."
sudo docker exec kafka1 kafka-topics --describe --topic test-topic --bootstrap-server localhost:9092

# 发送测试消息
echo "4. 发送测试消息..."
echo "Hello from Kafka KRaft!" | sudo docker exec -i kafka1 kafka-console-producer --topic test-topic --bootstrap-server localhost:9092

# 消费消息
echo "5. 消费消息（按Ctrl+C退出）..."
sudo docker exec kafka1 kafka-console-consumer --topic test-topic --bootstrap-server localhost:9092 --from-beginning --max-messages 1

echo "测试完成！"
