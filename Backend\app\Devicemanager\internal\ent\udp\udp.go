// Code generated by ent, DO NOT EDIT.

package udp

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the udp type in the database.
	Label = "udp"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeviceID holds the string denoting the device_id field in the database.
	FieldDeviceID = "device_id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldHeader holds the string denoting the header field in the database.
	FieldHeader = "header"
	// FieldTypeID holds the string denoting the type_id field in the database.
	FieldTypeID = "type_id"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldCreateUnix holds the string denoting the create_unix field in the database.
	FieldCreateUnix = "create_unix"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// EdgeDevice holds the string denoting the device edge name in mutations.
	EdgeDevice = "device"
	// Table holds the table name of the udp in the database.
	Table = "udps"
	// DeviceTable is the table that holds the device relation/edge.
	DeviceTable = "udps"
	// DeviceInverseTable is the table name for the Device entity.
	// It exists in this package in order to avoid circular dependency with the "device" package.
	DeviceInverseTable = "devices"
	// DeviceColumn is the table column denoting the device relation/edge.
	DeviceColumn = "device_id"
)

// Columns holds all SQL columns for udp fields.
var Columns = []string{
	FieldID,
	FieldDeviceID,
	FieldType,
	FieldHeader,
	FieldTypeID,
	FieldDescription,
	FieldName,
	FieldCreateUnix,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	DeviceIDValidator func(string) error
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// DefaultHeader holds the default value on creation for the "header" field.
	DefaultHeader string
	// DefaultTypeID holds the default value on creation for the "type_id" field.
	DefaultTypeID string
	// DefaultDescription holds the default value on creation for the "description" field.
	DefaultDescription string
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
	// DefaultCreateUnix holds the default value on creation for the "create_unix" field.
	DefaultCreateUnix func() time.Time
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Udp queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeviceID orders the results by the device_id field.
func ByDeviceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeviceID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByHeader orders the results by the header field.
func ByHeader(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHeader, opts...).ToFunc()
}

// ByTypeID orders the results by the type_id field.
func ByTypeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTypeID, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByCreateUnix orders the results by the create_unix field.
func ByCreateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUnix, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}

// ByDeviceField orders the results by device field.
func ByDeviceField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDeviceStep(), sql.OrderByField(field, opts...))
	}
}
func newDeviceStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DeviceInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, DeviceTable, DeviceColumn),
	)
}
