syntax = "v1"

info (
	title:  "data Service"
	desc:   "data service related api"
	author: ""
	email:  ""
)

type (
	TableInfo {
		TableUID string `json:"tableUID"`
	}
	InstanceTagInfo {
		InstanceName string `json:"instanceName,optional"`
		InstanceUID  string `json:"instanceUID"`
	}
	DataGroupTagInfo {
		DataGroupName string `json:"dataGroupName"`
	}
	FieldsInfo {
		FieldName []string `json:"fieldName"`
	}
	WhereInfo {
		ColumnName    string `json:"columnName"`
		CompareSymbol string `json:"compareSymbol"`
		CompareValue  string `json:"compareValue"`
	}
	SingleData {
		Field string `json:"field"`
		Value string `json:"value"`
	}
	DataRow {
		TimeStamp     int64            `json:"ts"`
		InstanceInfo  InstanceTagInfo  `json:"instanceInfo"`
		DataGroupInfo DataGroupTagInfo `json:"dataGroupInfo"`
		Datas         []SingleData     `json:"datas,optional"`
	}
	DataResult {
		List     []DataRow `json:"list"`
		Page     int64     `json:"page"`
		PageSize int64     `json:"pageSize"`
		Total    uint64    `json:"total"`
	}
)

//响应-请求结构体
type (
	FetchProducerDataRequest {
		TableInfo     TableInfo        `json:"tableInfo"`
		Revision      int64            `json:"revision,optional"`
		WhereInfos    []WhereInfo      `json:"whereInfos,optional"`
		DataRowLimit  uint64           `json:"dataRowLimit,optional"`
		Page          int64            `json:"page,optional"`
		PageSize      int64            `json:"pageSize,optional"`
		DataGroupInfo DataGroupTagInfo `json:"dataGroupInfo,optional"`
		InstanceInfo  InstanceTagInfo  `json:"instanceInfo,optional"`
		FieldsInfo    FieldsInfo       `json:"fieldsInfo,optional"`
	}
	FetchProducerDataResponse {
		List     []DataRow `json:"list"`
		Page     int64     `json:"page"`
		PageSize int64     `json:"pageSize"`
		Total    uint64    `json:"total"`
	}
	FetchInstanceDataRequest {
		DataGroupInfo DataGroupTagInfo `json:"dataGroupInfo,optional"`
		Revision      int64            `json:"revision,optional"`
		WhereInfos    []WhereInfo      `json:"whereInfos,optional"`
		DataRowLimit  uint64           `json:"dataRowLimit,optional"`
		Page          int64            `json:"page,optional"`
		PageSize      int64            `json:"pageSize,optional"`
		InstanceInfo  InstanceTagInfo  `json:"instanceInfo"`
		FieldsInfo    FieldsInfo       `json:"fieldsInfo,optional"`
	}
	FetchInstanceDataResponse {
		List     []DataRow `json:"list"`
		Page     int64     `json:"page"`
		PageSize int64     `json:"pageSize"`
		Total    uint64    `json:"total"`
	}
	DeleteSelectedDataRequest {
		Revision int64     `json:"revision,optional"`
		List     []DataRow `json:"list"`
	}
	DeleteSelectedDataResponse {
		Status       string `json:"status"`
		RowsAffected uint64 `json:"rowsAffected"`
	}
	ClearInstanceDataRequest {
		Revision     int64           `json:"revision,optional"`
		InstanceInfo InstanceTagInfo `json:"instanceInfo"`
	}
	ClearInstanceDataResponse {
		Status       string `json:"status"`
		RowsAffected uint64 `json:"rowsAffected"`
	}
	ExportCSVRequest {
		FileName     string          `json:"fileName"`
		InstanceInfo InstanceTagInfo `json:"instanceInfo"`
	}
	ExportCSVResponse {
		FileContent string `json:"fileContent"`
		FileName    string `json:"fileName"`
	}
)

//api
service data-api {
	@handler FetchProducerDataHandler
	post /api/v1/data/producer/fetch (FetchProducerDataRequest) returns (FetchProducerDataResponse)

	@handler FetchInstanceDataHandler
	post /api/v1/data/instance/fetch (FetchInstanceDataRequest) returns (FetchInstanceDataResponse)

	@handler DeleteSelectedDataHandler
	post /api/v1/data/selected/delete (DeleteSelectedDataRequest) returns (DeleteSelectedDataResponse)

	@handler ClearInstanceDataHandler
	post /api/v1/data/instance/clear (ClearInstanceDataRequest) returns (ClearInstanceDataResponse)

	@handler ExportCSVHandler
	post /api/v1/data/export/csv (ExportCSVRequest) returns (ExportCSVResponse)
}

