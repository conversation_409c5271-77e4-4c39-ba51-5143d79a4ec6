// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/test_create_db"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCreateDbDelete is the builder for deleting a Test_create_db entity.
type TestCreateDbDelete struct {
	config
	hooks    []Hook
	mutation *TestCreateDbMutation
}

// Where appends a list predicates to the TestCreateDbDelete builder.
func (tcdd *TestCreateDbDelete) Where(ps ...predicate.Test_create_db) *TestCreateDbDelete {
	tcdd.mutation.Where(ps...)
	return tcdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (tcdd *TestCreateDbDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, tcdd.sqlExec, tcdd.mutation, tcdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (tcdd *TestCreateDbDelete) ExecX(ctx context.Context) int {
	n, err := tcdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (tcdd *TestCreateDbDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(test_create_db.Table, sqlgraph.NewFieldSpec(test_create_db.FieldID, field.TypeInt))
	if ps := tcdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, tcdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	tcdd.mutation.done = true
	return affected, err
}

// TestCreateDbDeleteOne is the builder for deleting a single Test_create_db entity.
type TestCreateDbDeleteOne struct {
	tcdd *TestCreateDbDelete
}

// Where appends a list predicates to the TestCreateDbDelete builder.
func (tcddo *TestCreateDbDeleteOne) Where(ps ...predicate.Test_create_db) *TestCreateDbDeleteOne {
	tcddo.tcdd.mutation.Where(ps...)
	return tcddo
}

// Exec executes the deletion query.
func (tcddo *TestCreateDbDeleteOne) Exec(ctx context.Context) error {
	n, err := tcddo.tcdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{test_create_db.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (tcddo *TestCreateDbDeleteOne) ExecX(ctx context.Context) {
	if err := tcddo.Exec(ctx); err != nil {
		panic(err)
	}
}
