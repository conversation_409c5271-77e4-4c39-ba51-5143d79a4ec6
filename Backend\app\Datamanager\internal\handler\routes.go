// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1

package handler

import (
	"net/http"

	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/data/export/csv",
				Handler: ExportCSVHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/data/instance/clear",
				Handler: ClearInstanceDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/data/instance/fetch",
				Handler: FetchInstanceDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/data/producer/fetch",
				Handler: FetchProducerDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/data/selected/delete",
				Handler: Delete<PERSON>elected<PERSON><PERSON><PERSON><PERSON><PERSON>(serverCtx),
			},
		},
	)
}
