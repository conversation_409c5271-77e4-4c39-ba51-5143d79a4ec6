// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Modelmanager/internal/ent/model"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Model is the model entity for the Model schema.
type Model struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 模型ID
	ModelID string `json:"model_id,omitempty"`
	// 模型名称
	ModelNameVersion string `json:"model_name_version,omitempty"`
	// 模型框架版本
	FrameworkVersion string `json:"framework_version,omitempty"`
	// 模型任务类型
	TypeDesc string `json:"type_desc,omitempty"`
	// 模型存储路径
	StoragePath string `json:"storage_path,omitempty"`
	// accuracy
	Accuracy float64 `json:"accuracy,omitempty"`
	// precision
	Precision float64 `json:"precision,omitempty"`
	// recall
	Recall float64 `json:"recall,omitempty"`
	// f1_score
	F1Score float64 `json:"f1_score,omitempty"`
	// 模型训练者
	CreatedBy string `json:"created_by,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// Update timestamp
	UpdateUnix   time.Time `json:"update_unix,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Model) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case model.FieldAccuracy, model.FieldPrecision, model.FieldRecall, model.FieldF1Score:
			values[i] = new(sql.NullFloat64)
		case model.FieldID:
			values[i] = new(sql.NullInt64)
		case model.FieldModelID, model.FieldModelNameVersion, model.FieldFrameworkVersion, model.FieldTypeDesc, model.FieldStoragePath, model.FieldCreatedBy:
			values[i] = new(sql.NullString)
		case model.FieldCreateUnix, model.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Model fields.
func (m *Model) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case model.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			m.ID = int(value.Int64)
		case model.FieldModelID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_id", values[i])
			} else if value.Valid {
				m.ModelID = value.String
			}
		case model.FieldModelNameVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_name_version", values[i])
			} else if value.Valid {
				m.ModelNameVersion = value.String
			}
		case model.FieldFrameworkVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field framework_version", values[i])
			} else if value.Valid {
				m.FrameworkVersion = value.String
			}
		case model.FieldTypeDesc:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type_desc", values[i])
			} else if value.Valid {
				m.TypeDesc = value.String
			}
		case model.FieldStoragePath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field storage_path", values[i])
			} else if value.Valid {
				m.StoragePath = value.String
			}
		case model.FieldAccuracy:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field accuracy", values[i])
			} else if value.Valid {
				m.Accuracy = value.Float64
			}
		case model.FieldPrecision:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field precision", values[i])
			} else if value.Valid {
				m.Precision = value.Float64
			}
		case model.FieldRecall:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field recall", values[i])
			} else if value.Valid {
				m.Recall = value.Float64
			}
		case model.FieldF1Score:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field f1_score", values[i])
			} else if value.Valid {
				m.F1Score = value.Float64
			}
		case model.FieldCreatedBy:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field created_by", values[i])
			} else if value.Valid {
				m.CreatedBy = value.String
			}
		case model.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				m.CreateUnix = value.Time
			}
		case model.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				m.UpdateUnix = value.Time
			}
		default:
			m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Model.
// This includes values selected through modifiers, order, etc.
func (m *Model) Value(name string) (ent.Value, error) {
	return m.selectValues.Get(name)
}

// Update returns a builder for updating this Model.
// Note that you need to call Model.Unwrap() before calling this method if this Model
// was returned from a transaction, and the transaction was committed or rolled back.
func (m *Model) Update() *ModelUpdateOne {
	return NewModelClient(m.config).UpdateOne(m)
}

// Unwrap unwraps the Model entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (m *Model) Unwrap() *Model {
	_tx, ok := m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Model is not a transactional entity")
	}
	m.config.driver = _tx.drv
	return m
}

// String implements the fmt.Stringer.
func (m *Model) String() string {
	var builder strings.Builder
	builder.WriteString("Model(")
	builder.WriteString(fmt.Sprintf("id=%v, ", m.ID))
	builder.WriteString("model_id=")
	builder.WriteString(m.ModelID)
	builder.WriteString(", ")
	builder.WriteString("model_name_version=")
	builder.WriteString(m.ModelNameVersion)
	builder.WriteString(", ")
	builder.WriteString("framework_version=")
	builder.WriteString(m.FrameworkVersion)
	builder.WriteString(", ")
	builder.WriteString("type_desc=")
	builder.WriteString(m.TypeDesc)
	builder.WriteString(", ")
	builder.WriteString("storage_path=")
	builder.WriteString(m.StoragePath)
	builder.WriteString(", ")
	builder.WriteString("accuracy=")
	builder.WriteString(fmt.Sprintf("%v", m.Accuracy))
	builder.WriteString(", ")
	builder.WriteString("precision=")
	builder.WriteString(fmt.Sprintf("%v", m.Precision))
	builder.WriteString(", ")
	builder.WriteString("recall=")
	builder.WriteString(fmt.Sprintf("%v", m.Recall))
	builder.WriteString(", ")
	builder.WriteString("f1_score=")
	builder.WriteString(fmt.Sprintf("%v", m.F1Score))
	builder.WriteString(", ")
	builder.WriteString("created_by=")
	builder.WriteString(m.CreatedBy)
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(m.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(m.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Models is a parsable slice of Model.
type Models []*Model
