package logic

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProducerDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetProducerDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProducerDataLogic {
	return &GetProducerDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetProducerDataLogic) GetProducerData(in *datamanager.GetProducerDataRequest) (*datamanager.GetProducerDataResponse, error) {
	// todo: add your logic here and delete this line
	return l.svcCtx.UtilDataService.GetProducerData(l.ctx, in)
}
