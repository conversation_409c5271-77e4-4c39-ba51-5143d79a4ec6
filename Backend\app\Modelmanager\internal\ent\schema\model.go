package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Model holds the schema definition for the Model entity.
type Model struct {
	ent.Schema
}

// Fields of the Model.
func (Model) Fields() []ent.Field {
	return []ent.Field{
		field.String("model_id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("模型ID"),
		field.String("model_name_version").
			Unique().
			Comment("模型名称"),
		field.String("framework_version").
			Comment("模型框架版本").
			Default(""),
		field.String("type_desc").
			Comment("模型任务类型").
			Default(""),
		field.String("storage_path").
			Unique().
			NotEmpty().
			Comment("模型存储路径"),
		field.Float("accuracy").
			Comment("accuracy").
			Default(0).
			Positive(),
		field.Float("precision").
			Comment("precision").
			Default(0).
			Positive(),
		field.Float("recall").
			Comment("recall").
			Default(0).
			Positive(),
		field.Float("f1_score").
			Comment("f1_score").
			Default(0).
			Positive(),
		field.String("created_by").
			Comment("模型训练者").
			Default(""),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("Update timestamp"),
	}
}
