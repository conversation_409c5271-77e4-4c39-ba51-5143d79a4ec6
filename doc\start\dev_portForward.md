#本地API测试
trap 'kill 0' SIGINT
kubectl port-forward services/datamanager 3333:3333 -n default   &
kubectl port-forward services/devicemanager 8888:8888 -n default   &
kubectl port-forward services/etcd-svc 12379:12379 12380:12380 -n etcd   &
kubectl port-forward services/greptimedb-standalone 4000:4000 4001:4001 4002:4002 4003:4003 -n greptimedb   &
kubectl port-forward services/postgresql-svc 5432:5432 -n postgresql   &
kubectl port-forward services/kafka-ui-svc 8080:8080 -n kafka   &
kubectl port-forward services/kafka-svc 9092:9092 -n kafka  &
wait
#调试设备管理器
trap 'kill 0' SIGINT
kubectl port-forward services/datamanager 3333:3333 -n default   &
kubectl port-forward services/etcd-svc 12379:12379 12380:12380 -n etcd   &
kubectl port-forward services/greptimedb-standalone 4000:4000 4001:4001 4002:4002 4003:4003 -n greptimedb   &
kubectl port-forward services/postgresql-svc 5432:5432 -n postgresql   &
kubectl port-forward services/kafka-ui-svc 8080:8080 -n kafka   &
kubectl port-forward services/kafka-svc 9092:9092 -n kafka  &
wait