package logic

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	xerrors "github.com/zeromicro/x/errors"
)

type DeleteDeviceFrameLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDeviceFrameLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDeviceFrameLogic {
	return &DeleteDeviceFrameLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDeviceFrameLogic) DeleteDeviceFrame(req *types.DeleteDeviceFrameRequest) (resp *types.DeleteDeviceFrameResponse, err error) {
	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备失败 : %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		l.Logger.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		Msg := fmt.Errorf("根据DeviceUID %s 查询设备工作状态失败: %v", req.DeviceUID, err)
		return nil, xerrors.New(http.StatusNotFound, Msg.Error())
	}
	//TODO: 根据状态决定是否可以删除
	if DeviceWorkStatus.HealthStatus == utils.HealthStatusRunning {
		Msg := fmt.Errorf("设备仍在运行，请先停止设备")
		return nil, xerrors.New(http.StatusForbidden, Msg.Error())
	}

	//获取帧info数据和deviceMeta数据
	Protocols := strings.Split(queryDevice.Protocol, ",")
	DeviceMeta, FrameInfos, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, Protocols, queryDevice.ID)
	if err != nil {
		return nil, xerrors.New(http.StatusNotFound, "帧info数据和deviceMeta数据获取失败")
	}

	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, xerrors.New(http.StatusForbidden, "删除设备帧事务启动失败")
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()
	//删除Frame记录
	for _, frameMeta := range req.FrameMetas {
		// Protocols = utils.RemoveProtocol(Protocols, frameMeta.FrameType)
		//删除Data记录
		_, err = tx.Data.Delete().Where(data.FrameIDEQ(frameMeta.FrameUID)).Exec(l.ctx)
		if err != nil {
			err_rollback := tx.Rollback()
			if err_rollback != nil {
				Msg := fmt.Errorf("Data数据更改撤销失败: %s", err_rollback.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
			Msg := fmt.Errorf("Data数据删除失败: %s", err.Error())
			return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
		}
		switch frameMeta.FrameType {
		case utils.FrameTypeModbus:
			if frameMeta.FrameUID != "" {
				_, err = tx.Modbus.Delete().Where(modbus.DeviceIDEQ(req.DeviceUID), modbus.IDEQ(frameMeta.FrameUID)).Exec(l.ctx)
			} else {
				_, err = tx.Modbus.Delete().Where(modbus.DeviceIDEQ(req.DeviceUID)).Exec(l.ctx)
			}
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					Msg := fmt.Errorf("Modbus数据更改撤销失败: %s", err_rollback.Error())
					return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
				}
				Msg := fmt.Errorf("Modbus数据删除失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
		case utils.FrameTypeUart:
			if frameMeta.FrameUID != "" {
				_, err = tx.Uart.Delete().Where(uart.DeviceIDEQ(req.DeviceUID), uart.IDEQ(frameMeta.FrameUID)).Exec(l.ctx)
			} else {
				_, err = tx.Uart.Delete().Where(uart.DeviceIDEQ(req.DeviceUID)).Exec(l.ctx)
			}
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					Msg := fmt.Errorf("Uart数据更改撤销失败: %s", err_rollback.Error())
					return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
				}
				Msg := fmt.Errorf("Uart数据删除失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
		case utils.FrameTypeUdp:
			if frameMeta.FrameUID != "" {
				_, err = tx.Udp.Delete().Where(udp.DeviceIDEQ(req.DeviceUID), udp.IDEQ(frameMeta.FrameUID)).Exec(l.ctx)
			} else {
				_, err = tx.Udp.Delete().Where(udp.DeviceIDEQ(req.DeviceUID)).Exec(l.ctx)
			}
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					Msg := fmt.Errorf("Udp数据更改撤销失败: %s", err_rollback.Error())
					return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
				}
				Msg := fmt.Errorf("Udp数据删除失败: %s", err.Error())
				return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
			}
		}
	}

	//更新设备数据库
	_, err = tx.Device.Update().Where(device.IDEQ(req.DeviceUID)).
		SetProtocol(strings.Join(Protocols, ",")).
		Save(l.ctx)
	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			Msg := fmt.Errorf("设备数据更改撤销失败: %s", err_rollback.Error())
			return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
		}
		Msg := fmt.Errorf("设备数据更新失败: %s", err.Error())
		return nil, xerrors.New(http.StatusNotAcceptable, Msg.Error())
	}

	//提交事务
	err = tx.Commit()
	if err != nil {
		Msg := fmt.Errorf("删除设备帧请求提交失败: %s", err.Error())
		return nil, xerrors.New(http.StatusBadRequest, Msg.Error())
	}
	//返回响应
	DeviceMeta, FrameInfos, err = utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, Protocols, queryDevice.ID)
	if err != nil {
		return nil, xerrors.New(http.StatusNotAcceptable, "客户端响应数据发生失败")
	}

	// 添加到Etcd
	err = utils.Pub2Etcd(l.ctx, l.svcCtx)
	if err != nil {
		return nil, xerrors.New(http.StatusInternalServerError, "failed to publish to Etcd: "+err.Error())
	}

	return &types.DeleteDeviceFrameResponse{
		DeviceMeta:       *DeviceMeta,
		FrameInfos:       FrameInfos,
		DeviceWorkStatus: *DeviceWorkStatus,
	}, nil
}
