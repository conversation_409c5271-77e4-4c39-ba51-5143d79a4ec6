package service

import (
	"context"
	"fmt"
	"kafka-producer/pkg/data"
	"kafka-producer/pkg/etcd"
	"kafka-producer/pkg/kafka"
	"kafka-producer/pkg/message"
	"log"
	"sync"
	"time"
)

// ProducerService 生产者服务
type ProducerService struct {
	etcdClient    *etcd.Client
	kafkaProducer *kafka.Producer
	dataGenerator *data.DataGenerator
	driverUID     string
	producerInfo  *etcd.ProducerInfo
	instances     []etcd.Instance
	kafkaBrokers  []string
	mu            sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
}

// NewProducerService 创建新的生产者服务
func NewProducerService(etcdEndpoints []string, kafkaBrokers []string, driverUID string) (*ProducerService, error) {
	etcdClient, err := etcd.NewClient(etcdEndpoints)
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd client: %w", err)
	}

	dataGenerator := data.NewDataGenerator()

	ctx, cancel := context.WithCancel(context.Background())

	return &ProducerService{
		etcdClient:    etcdClient,
		dataGenerator: dataGenerator,
		driverUID:     driverUID,
		kafkaBrokers:  kafkaBrokers,
		ctx:           ctx,
		cancel:        cancel,
	}, nil
}

// Start 启动生产者服务
func (s *ProducerService) Start() error {
	// 初始化获取配置信息
	err := s.loadInitialConfig()
	if err != nil {
		return fmt.Errorf("failed to load initial config: %w", err)
	}

	// 启动etcd监听 - 已禁用，使用第一次Get获取的配置
	// go s.watchEtcdChanges()

	// 启动数据生产
	go s.startDataProduction()

	log.Printf("Producer service started for driver %s", s.driverUID)
	return nil
}

// Stop 停止生产者服务
func (s *ProducerService) Stop() {
	s.cancel()
	if s.kafkaProducer != nil {
		s.kafkaProducer.Close()
	}
	s.etcdClient.Close()
	log.Printf("Producer service stopped for driver %s", s.driverUID)
}

// loadInitialConfig 加载初始配置
func (s *ProducerService) loadInitialConfig() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 获取producer信息
	producerInfo, err := s.etcdClient.GetProducerInfo(ctx, s.driverUID)
	if err != nil {
		return err
	}

	// 获取instances信息
	instances, err := s.etcdClient.GetInstances(ctx, s.driverUID)
	if err != nil {
		return err
	}

	// 使用Producer.Uname作为Topic名称创建kafka生产者
	kafkaProducer := kafka.NewProducer(s.kafkaBrokers, producerInfo.Uname)

	s.mu.Lock()
	s.producerInfo = producerInfo
	s.instances = instances
	s.kafkaProducer = kafkaProducer
	s.mu.Unlock()

	log.Printf("Loaded config for driver %s: %d data groups, %d instances, topic: %s",
		s.driverUID, len(producerInfo.DataGroups), len(instances), producerInfo.Uname)
	return nil
}

// watchEtcdChanges 监听etcd配置变化
func (s *ProducerService) watchEtcdChanges() {
	s.etcdClient.WatchProducer(s.ctx, s.driverUID, func(producerInfo *etcd.ProducerInfo) {
		// 重新创建kafka生产者以使用新的topic
		newKafkaProducer := kafka.NewProducer(s.kafkaBrokers, producerInfo.Uname)

		s.mu.Lock()
		oldProducer := s.kafkaProducer
		s.producerInfo = producerInfo
		s.kafkaProducer = newKafkaProducer
		s.mu.Unlock()

		// 关闭旧的生产者
		if oldProducer != nil {
			oldProducer.Close()
		}

		log.Printf("Producer config updated for driver %s, revision: %d, new topic: %s", s.driverUID, producerInfo.Revision, producerInfo.Uname)
	})
}

// startDataProduction 开始数据生产
func (s *ProducerService) startDataProduction() {
	ticker := time.NewTicker(5 * time.Second) // 每秒生产一次数据
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.produceData()
		}
	}
}

// produceData 生产数据
func (s *ProducerService) produceData() {
	s.mu.RLock()
	producerInfo := s.producerInfo
	instances := s.instances
	s.mu.RUnlock()

	if producerInfo == nil || len(instances) == 0 {
		return
	}

	// 为每个实例的每个数据组生成数据
	for _, instance := range instances {
		for _, dataGroup := range producerInfo.DataGroups {
			// 生成数据
			generatedData := s.dataGenerator.GenerateDataForGroup(dataGroup)

			// 创建消息
			msg := message.NewMessage(
				instance.Name,
				dataGroup.Name,
				producerInfo.Revision,
				generatedData,
			)

			// 发送到kafka
			key := fmt.Sprintf("%s_%s_%s", s.driverUID, instance.Name, dataGroup.Name)
			err := s.kafkaProducer.SendMessage(s.ctx, key, msg)
			if err != nil {
				log.Printf("Failed to send message: %v", err)
			}
		}
	}
}
