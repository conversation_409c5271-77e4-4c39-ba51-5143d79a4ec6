// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Data is the predicate function for data builders.
type Data func(*sql.Selector)

// Device is the predicate function for device builders.
type Device func(*sql.Selector)

// Modbus is the predicate function for modbus builders.
type Modbus func(*sql.Selector)

// Test_create_db is the predicate function for test_create_db builders.
type Test_create_db func(*sql.Selector)

// Uart is the predicate function for uart builders.
type Uart func(*sql.Selector)

// Udp is the predicate function for udp builders.
type Udp func(*sql.Selector)
