// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1
// Source: data.proto

package server

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/logic"
	"GCF/app/Datamanager/internal/svc"
)

type DataServer struct {
	svcCtx *svc.ServiceContext
	datamanager.UnimplementedDataServer
}

func NewDataServer(svcCtx *svc.ServiceContext) *DataServer {
	return &DataServer{
		svcCtx: svcCtx,
	}
}

func (s *DataServer) GetProducerData(ctx context.Context, in *datamanager.GetProducerDataRequest) (*datamanager.GetProducerDataResponse, error) {
	l := logic.NewGetProducerDataLogic(ctx, s.svcCtx)
	return l.GetProducerData(in)
}

func (s *DataServer) DeleteProducerData(ctx context.Context, in *datamanager.DeleteProducerDataRequest) (*datamanager.DeleteProducerDataResponse, error) {
	l := logic.NewDeleteProducerDataLogic(ctx, s.svcCtx)
	return l.DeleteProducerData(in)
}

func (s *DataServer) ChangeProducerData(ctx context.Context, in *datamanager.ChangeProducerDataRequest) (*datamanager.ChangeProducerDataResponse, error) {
	l := logic.NewChangeProducerDataLogic(ctx, s.svcCtx)
	return l.ChangeProducerData(in)
}

func (s *DataServer) GetInstanceData(ctx context.Context, in *datamanager.GetInstanceDataRequest) (*datamanager.GetInstanceDataResponse, error) {
	l := logic.NewGetInstanceDataLogic(ctx, s.svcCtx)
	return l.GetInstanceData(in)
}

func (s *DataServer) DeleteInstanceData(ctx context.Context, in *datamanager.DeleteInstanceDataRequest) (*datamanager.DeleteInstanceDataResponse, error) {
	l := logic.NewDeleteInstanceDataLogic(ctx, s.svcCtx)
	return l.DeleteInstanceData(in)
}

func (s *DataServer) ChangeInstanceData(ctx context.Context, in *datamanager.ChangeInstanceDataRequest) (*datamanager.ChangeInstanceDataResponse, error) {
	l := logic.NewChangeInstanceDataLogic(ctx, s.svcCtx)
	return l.ChangeInstanceData(in)
}

func (s *DataServer) ChangeData(ctx context.Context, in *datamanager.ChangeDataRequest) (*datamanager.ChangeDataResponse, error) {
	l := logic.NewChangeDataLogic(ctx, s.svcCtx)
	return l.ChangeData(in)
}
