#!/usr/bin/env bash
set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

if [[ -z "${SQL_DIRECTORY:-}" ]]; then
  echo "Start a postgres DB with schema, for migration"
  echo "Usage:"
  echo "  SQL_DIRECTORY=db/migrations/postgres $0"
  exit 1
fi

# 检查是否设置了数据库名称环境变�?
if [[ -z "${DB_NAME:-}" ]]; then
  echo "Start a postgres DB with schema, for migration"
  echo "DB:"
  echo "  DB_NAME=migration $0"
  exit 1
fi

SQL_DIRECTORY=$(realpath "${SQL_DIRECTORY}")

# 创建 Docker 网络（如果不存在�?
docker network create migration-network 2>/dev/null || true

# 使用自定义网络启�? PostgreSQL
docker run -itd --rm \
           --name migration-postgres \
           --network migration-network \
           -e POSTGRES_PASSWORD=pass \
           -e POSTGRES_DB=$DB_NAME \
           -p 15432:5432 \
           postgres:15

echo "## start container migration-postgres, waiting..."
sleep 5

# 在同一网络中运�? Flyway
docker run --rm \
           --network migration-network \
           -v "$SQL_DIRECTORY:/flyway/sql" \
           flyway/flyway:10.0 \
           -url=************************************************** \
           -user=postgres \
           -password=pass \
           migrate

echo ""
echo "## postgres is ready on localhost:15432"
echo "PGPASSWORD=pass psql -h localhost -U postgres -d $DB_NAME -p 15432"
echo "## to remove the container and network, run:"
echo "docker stop migration-postgres && docker network rm migration-network"
