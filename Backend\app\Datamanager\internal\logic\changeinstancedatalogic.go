package logic

import (
	"context"

	"GCF/app/Datamanager/internal/datamanager"
	"GCF/app/Datamanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeInstanceDataLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewChangeInstanceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeInstanceDataLogic {
	return &ChangeInstanceDataLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ChangeInstanceDataLogic) ChangeInstanceData(in *datamanager.ChangeInstanceDataRequest) (*datamanager.ChangeInstanceDataResponse, error) {
	// todo: add your logic here and delete this line

	return &datamanager.ChangeInstanceDataResponse{}, nil
}
